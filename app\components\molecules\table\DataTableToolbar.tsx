'use client';
import { Toolbar, Typography, Button, SxProps, Theme } from '@mui/material';

export default function DataTableToolbar({
  numSelected,
  title,
  onAdd,
  sx,
}: {
  numSelected: number;
  title: string;
  onAdd?: () => void;
  sx?: SxProps<Theme>;
}) {
  return (
    <Toolbar
      sx={{
        pl: { sm: 2 },
        pr: { xs: 1, sm: 1 },
        ...(numSelected > 0 && {
          bgcolor: (theme) =>
            theme.palette.mode === 'light'
              ? theme.palette.primary.light
              : theme.palette.primary.dark,
        }),
        ...sx
      }}
    >
      {numSelected > 0 ? (
        <Typography
          sx={{ flex: '1 1 100%' }}
          color="inherit"
          variant="subtitle1"
          component="div"
        >
          {numSelected} selected
        </Typography>
      ) : (
        <Typography
          sx={{ flex: '1 1 100%' }}
          variant="h6"
          id="tableTitle"
          component="div"
        >
          {title}
        </Typography>
      )}

      {onAdd && (
        <Button variant="contained" onClick={onAdd}>
          Add New
        </Button>
      )}
    </Toolbar>
  );
}
