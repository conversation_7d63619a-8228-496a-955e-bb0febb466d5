'use client';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Paper,
  Divider,
  List,
  ListItem,
  ListItemText,
  Chip,
  TextField,
  FormControlLabel,
  Checkbox,
  Alert,
} from '@mui/material';

interface PriceSummaryProps {
  bookingPriceData: any;
  carData?: any;
  totalPrice: number;
  // Suggested price props matching old implementation
  suggestedPrice?: number | null;
  onSuggestedPriceChange?: (price: number | null) => void;
  useSuggestedPrice?: boolean;
  onUseSuggestedPriceChange?: (use: boolean) => void;
  isEditMode?: boolean;
}

export default function PriceSummary({
  bookingPriceData,
  carData,
  totalPrice,
  suggestedPrice,
  onSuggestedPriceChange,
  useSuggestedPrice = false,
  onUseSuggestedPriceChange,
  isEditMode = false,
}: PriceSummaryProps) {
  const { t } = useTranslation();
  const [suggestedPriceInput, setSuggestedPriceInput] = useState(suggestedPrice?.toString() || '');

  if (!bookingPriceData) {
    return (
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          {t('price.summary')}
        </Typography>
        <Alert severity="info">{t('select.car.first.to.see.prices')}</Alert>
      </Paper>
    );
  }

  const {
    aboutRentPrice = {},
    aboutHandoverPrice = {},
    aboutCoupon = {},
    aboutExtraServices = {},
    aboutDeliveryPrice = {},
    deliveryOrderCity = {},
  } = bookingPriceData;

  const handleSuggestedPriceChange = (value: string) => {
    setSuggestedPriceInput(value);

    // Validate and convert to number (matching old implementation regex)
    const regex = /^(\d*\.?\d+|\d+\.?\d*)$/;
    if (value === '' || regex.test(value)) {
      const numValue = value === '' ? null : parseFloat(value);
      if (onSuggestedPriceChange) {
        onSuggestedPriceChange(numValue);
      }
    }
  };

  const displayPrice = useSuggestedPrice && suggestedPrice ? suggestedPrice : totalPrice;

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        {t('price.summary')}
      </Typography>

      <List dense>
        {/* Rent Price */}
        {aboutRentPrice.rentPrice > 0 && (
          <ListItem disableGutters>
            <ListItemText
              primary={t('rent.price')}
              secondary={
                aboutRentPrice.discountValue > 0
                  ? `${t('original')}: ${
                      aboutRentPrice.rentPrice + aboutRentPrice.discountValue
                    } ${t('currency.sr')} | ${t('discount')}: ${aboutRentPrice.discountValue} ${t(
                      'currency.sr'
                    )}`
                  : undefined
              }
            />
            <Typography variant="body1" color="primary">
              {aboutRentPrice.rentPrice} {t('currency.sr')}
            </Typography>
          </ListItem>
        )}

        {/* Unlimited KM */}
        {aboutRentPrice.unlimitedKmPrice > 0 && (
          <ListItem disableGutters>
            <ListItemText primary={t('Unlimited.KM')} />
            <Typography variant="body1">
              {aboutRentPrice.unlimitedKmPrice} {t('currency.sr')}
            </Typography>
          </ListItem>
        )}

        {/* Extra Services */}
        {aboutExtraServices.totalExtraServicesPrice > 0 && (
          <ListItem disableGutters>
            <ListItemText
              primary={t('extra.services')}
              secondary={aboutExtraServices.extraServices
                ?.map((service: any) => `${service.title}: ${service.price} ${t('currency.sr')}`)
                .join(', ')}
            />
            <Typography variant="body1">
              {aboutExtraServices.totalExtraServicesPrice} {t('currency.sr')}
            </Typography>
          </ListItem>
        )}

        {/* Delivery Price */}
        {aboutDeliveryPrice.deliveryPrice > 0 && (
          <ListItem disableGutters>
            <ListItemText primary={t('delivery.price')} secondary={deliveryOrderCity.cityName} />
            <Typography variant="body1">
              {aboutDeliveryPrice.deliveryPrice} {t('currency.sr')}
            </Typography>
          </ListItem>
        )}

        {/* Handover Price */}
        {aboutHandoverPrice.handoverPrice > 0 && (
          <ListItem disableGutters>
            <ListItemText primary={t('handover.price')} />
            <Typography variant="body1">
              {aboutHandoverPrice.handoverPrice} {t('currency.sr')}
            </Typography>
          </ListItem>
        )}

        {/* Coupon Discount */}
        {aboutCoupon.discountValue > 0 && (
          <ListItem disableGutters>
            <ListItemText
              primary={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {t('coupon.discount')}
                  <Chip label={aboutCoupon.couponCode} size="small" color="success" />
                </Box>
              }
            />
            <Typography variant="body1" color="success.main">
              -{aboutCoupon.discountValue} {t('currency.sr')}
            </Typography>
          </ListItem>
        )}
      </List>

      <Divider sx={{ my: 2 }} />

      {/* Suggested Price Section - matching old implementation */}
      {isEditMode && onSuggestedPriceChange && onUseSuggestedPriceChange && (
        <Box sx={{ mb: 2 }}>
          <FormControlLabel
            control={
              <Checkbox
                checked={useSuggestedPrice}
                onChange={(e) => onUseSuggestedPriceChange(e.target.checked)}
                color="primary"
              />
            }
            label={t('use.suggested.price')}
          />

          {useSuggestedPrice && (
            <TextField
              fullWidth
              label={t('suggested.price.per.day')}
              value={suggestedPriceInput}
              onChange={(e) => handleSuggestedPriceChange(e.target.value)}
              type="number"
              InputProps={{
                endAdornment: <Typography>{t('currency.sr')}</Typography>,
              }}
              helperText={t('enter.custom.price.per.day')}
              sx={{ mt: 1 }}
            />
          )}
        </Box>
      )}

      {/* Total Price */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h6">
          {t('total.amount')}
          {useSuggestedPrice && (
            <Chip label={t('suggested')} size="small" color="warning" sx={{ ml: 1 }} />
          )}
        </Typography>
        <Typography variant="h6" color="primary">
          {displayPrice} {t('currency.sr')}
        </Typography>
      </Box>

      {/* Car Info */}
      {carData && (
        <Box sx={{ mt: 2, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
          <Typography variant="body2" color="textSecondary">
            {t('selected.car')}: {carData.title} - {carData.model}
          </Typography>
        </Box>
      )}
    </Paper>
  );
}
