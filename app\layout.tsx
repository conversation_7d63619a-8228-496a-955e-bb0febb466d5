'use client';
import { useState, useEffect } from 'react';
import { ThemeProvider } from '@mui/material';
import CssBaseline from '@mui/material/CssBaseline';
import { ApolloProvider } from '@apollo/client';
import useApolloClient from './gql/apollo-client';
import useTheme from './theme';
import './globals.css';
import '@/app/localization/next-i18next.config';
import { useTranslation } from 'react-i18next';
import { Provider } from 'react-redux';
import { store } from './store/store';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const { client } = useApolloClient();
  const { i18n } = useTranslation();
  const [darkMode, setDarkMode] = useState(false);
  const [highContrast, setHighContrast] = useState(false);
  const isRTL = i18n.language === 'ar';

  // Load user preferences from localStorage on component mount
  useEffect(() => {
    const savedDarkMode = localStorage.getItem('darkMode');
    const savedHighContrast = localStorage.getItem('highContrast');

    if (savedDarkMode) {
      setDarkMode(savedDarkMode === 'true');
    }

    if (savedHighContrast) {
      setHighContrast(savedHighContrast === 'true');
    }
  }, []);

  // Create theme with current mode and contrast settings
  const theme = useTheme(darkMode ? 'dark' : 'light', highContrast);

  // Update document attributes when theme changes
  useEffect(() => {
    // Add data attribute to html element for CSS selectors
    document.documentElement.setAttribute('data-theme-mode', darkMode ? 'dark' : 'light');

    // Add or update scrollbar styles
    let styleEl = document.getElementById('scrollbar-styles');
    if (!styleEl) {
      styleEl = document.createElement('style');
      styleEl.id = 'scrollbar-styles';
      document.head.appendChild(styleEl);
    }

    if (darkMode) {
      styleEl.textContent = `
        /* Force all scrollbars to have consistent styling */
        *::-webkit-scrollbar, ::-webkit-scrollbar { width: 10px !important; height: 8px !important; }
        *::-webkit-scrollbar-track, ::-webkit-scrollbar-track { background: rgba(30, 30, 30, 0.9) !important; border-radius: 4px !important; }
        *::-webkit-scrollbar-thumb, ::-webkit-scrollbar-thumb { background: #f1922380 !important; border-radius: 4px !important; border: none !important; }
        *::-webkit-scrollbar-thumb:hover, ::-webkit-scrollbar-thumb:hover { background: #f19223a0 !important; }

        /* Target specific elements that might have custom scrollbars */
        .MuiTableContainer-root::-webkit-scrollbar-thumb { background: #f1922380 !important; }
        .MuiTableContainer-root::-webkit-scrollbar-track { background: rgba(30, 30, 30, 0.9) !important; }
        [role=tablist] ~ div::-webkit-scrollbar-thumb { background: #f1922380 !important; }
        [role=tablist] ~ div::-webkit-scrollbar-track { background: rgba(30, 30, 30, 0.9) !important; }
      `;
    } else {
      styleEl.textContent = `
        /* Force all scrollbars to have consistent styling */
        *::-webkit-scrollbar, ::-webkit-scrollbar { width: 10px !important; height: 8px !important; }
        *::-webkit-scrollbar-track, ::-webkit-scrollbar-track { background: rgba(0, 0, 0, 0.05) !important; border-radius: 4px !important; }
        *::-webkit-scrollbar-thumb, ::-webkit-scrollbar-thumb { background: rgba(241, 146, 35, 0.5) !important; border-radius: 4px !important; border: none !important; }
        *::-webkit-scrollbar-thumb:hover, ::-webkit-scrollbar-thumb:hover { background: rgba(241, 146, 35, 0.8) !important; }
      `;
    }

    // Force a repaint to apply scrollbar styles
    const forceRepaint = document.createElement('style');
    document.head.appendChild(forceRepaint);
    setTimeout(() => document.head.removeChild(forceRepaint), 100);
  }, [darkMode]);

  return (
    <Provider store={store}>
      <html lang={i18n.language} data-theme-mode={darkMode ? 'dark' : 'light'}>
        <head>
          <script src="/scrollbar-fix.js" defer></script>
        </head>
        <body style={{ direction: isRTL ? 'rtl' : 'ltr' }}>
          <ApolloProvider client={client}>
            <ThemeProvider theme={theme}>
              <CssBaseline />
              {children}
              <ToastContainer
                position="bottom-center"
                autoClose={5000}
                hideProgressBar={false}
                newestOnTop
                closeOnClick
                rtl={isRTL}
                pauseOnFocusLoss
                draggable
                pauseOnHover
                theme={darkMode ? 'dark' : 'light'}
              />
            </ThemeProvider>
          </ApolloProvider>
        </body>
      </html>
    </Provider>
  );
}
