/**
 * Print Utility Functions
 */

interface PrintableBooking {
  bookingNo: string;
  customerName: string;
  customerMobile: string;
  customerEmail?: string;
  carMake?: string;
  carModel?: string;
  carYear?: string;
  pickUpDate: string;
  dropOffDate: string;
  numberOfDays: number;
  totalBookingPrice: number;
  status: string;
  branchName?: string;
  createdAt: string;
}

/**
 * Print booking details
 */
export const printBookingDetails = (booking: PrintableBooking, t: (key: string) => string) => {
  const printWindow = window.open('', '_blank');

  if (!printWindow) {
    alert(t('Unable to open print window. Please check your popup blocker.'));
    return;
  }

  const printContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>${t('Booking Details')} - ${booking.bookingNo}</title>
      <meta charset="utf-8">
      <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        
        body {
          font-family: 'Arial', sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
          background: white;
        }
        
        .header {
          text-align: center;
          margin-bottom: 40px;
          border-bottom: 3px solid #1976d2;
          padding-bottom: 20px;
        }
        
        .header h1 {
          color: #1976d2;
          font-size: 28px;
          margin-bottom: 10px;
        }
        
        .header h2 {
          color: #666;
          font-size: 18px;
          font-weight: normal;
        }
        
        .section {
          margin-bottom: 30px;
          background: #f9f9f9;
          padding: 20px;
          border-radius: 8px;
          border-left: 4px solid #1976d2;
        }
        
        .section h3 {
          color: #1976d2;
          font-size: 18px;
          margin-bottom: 15px;
          border-bottom: 1px solid #e0e0e0;
          padding-bottom: 8px;
        }
        
        .info-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 15px;
        }
        
        .info-item {
          display: flex;
          justify-content: space-between;
          padding: 8px 0;
          border-bottom: 1px dotted #ccc;
        }
        
        .info-label {
          font-weight: bold;
          color: #555;
          min-width: 120px;
        }
        
        .info-value {
          color: #333;
          text-align: right;
        }
        
        .status {
          display: inline-block;
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: bold;
          text-transform: uppercase;
          background: #e3f2fd;
          color: #1976d2;
        }
        
        .total-amount {
          font-size: 24px;
          font-weight: bold;
          color: #1976d2;
          text-align: center;
          background: #e3f2fd;
          padding: 15px;
          border-radius: 8px;
          margin: 20px 0;
        }
        
        .footer {
          text-align: center;
          margin-top: 40px;
          padding-top: 20px;
          border-top: 1px solid #e0e0e0;
          color: #666;
          font-size: 14px;
        }
        
        @media print {
          body {
            max-width: none;
            margin: 0;
            padding: 15px;
          }
          
          .section {
            break-inside: avoid;
            background: white !important;
            border: 1px solid #ccc;
          }
          
          .header {
            border-bottom: 2px solid #000;
          }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Carwah</h1>
        <h2>${t('Booking Details')}</h2>
      </div>
      
      <div class="section">
        <h3>${t('Booking Information')}</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="info-label">${t('Booking Number')}:</span>
            <span class="info-value">${booking.bookingNo}</span>
          </div>
          <div class="info-item">
            <span class="info-label">${t('Status')}:</span>
            <span class="info-value"><span class="status">${booking.status}</span></span>
          </div>
          <div class="info-item">
            <span class="info-label">${t('Created At')}:</span>
            <span class="info-value">${new Date(booking.createdAt).toLocaleDateString()}</span>
          </div>
          <div class="info-item">
            <span class="info-label">${t('Duration')}:</span>
            <span class="info-value">${booking.numberOfDays} ${t('days')}</span>
          </div>
        </div>
      </div>
      
      <div class="section">
        <h3>${t('Customer Information')}</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="info-label">${t('Customer Name')}:</span>
            <span class="info-value">${booking.customerName}</span>
          </div>
          <div class="info-item">
            <span class="info-label">${t('Mobile')}:</span>
            <span class="info-value">${booking.customerMobile}</span>
          </div>
          ${
            booking.customerEmail
              ? `
          <div class="info-item">
            <span class="info-label">${t('Email')}:</span>
            <span class="info-value">${booking.customerEmail}</span>
          </div>
          `
              : ''
          }
        </div>
      </div>
      
      <div class="section">
        <h3>${t('Vehicle Information')}</h3>
        <div class="info-grid">
          ${
            booking.carMake
              ? `
          <div class="info-item">
            <span class="info-label">${t('Make')}:</span>
            <span class="info-value">${booking.carMake}</span>
          </div>
          `
              : ''
          }
          ${
            booking.carModel
              ? `
          <div class="info-item">
            <span class="info-label">${t('Model')}:</span>
            <span class="info-value">${booking.carModel}</span>
          </div>
          `
              : ''
          }
          ${
            booking.carYear
              ? `
          <div class="info-item">
            <span class="info-label">${t('Year')}:</span>
            <span class="info-value">${booking.carYear}</span>
          </div>
          `
              : ''
          }
        </div>
      </div>
      
      <div class="section">
        <h3>${t('Rental Period')}</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="info-label">${t('Pick-up Date')}:</span>
            <span class="info-value">${new Date(booking.pickUpDate).toLocaleDateString()}</span>
          </div>
          <div class="info-item">
            <span class="info-label">${t('Drop-off Date')}:</span>
            <span class="info-value">${new Date(booking.dropOffDate).toLocaleDateString()}</span>
          </div>
          ${
            booking.branchName
              ? `
          <div class="info-item">
            <span class="info-label">${t('Branch')}:</span>
            <span class="info-value">${booking.branchName}</span>
          </div>
          `
              : ''
          }
        </div>
      </div>
      
      <div class="total-amount">
        ${t('Total Amount')}: ${new Intl.NumberFormat('en-SA', {
    style: 'currency',
    currency: 'SAR',
  }).format(booking.totalBookingPrice)}
      </div>
      
      <div class="footer">
        <p>${t('Printed on')}: ${new Date().toLocaleString()}</p>
        <p>Carwah © ${new Date().getFullYear()} ${t('All rights reserved')}</p>
      </div>
      
      <script>
        window.onload = function() {
          window.print();
          setTimeout(function() {
            window.close();
          }, 1000);
        };
      </script>
    </body>
    </html>
  `;

  printWindow.document.write(printContent);
  printWindow.document.close();
};

/**
 * Print any content with custom styling
 */
export const printContent = (content: string, title: string = 'Print') => {
  const printWindow = window.open('', '_blank');

  if (!printWindow) {
    alert('Unable to open print window. Please check your popup blocker.');
    return;
  }

  const printDocument = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>${title}</title>
      <meta charset="utf-8">
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        @media print {
          body {
            max-width: none;
            margin: 0;
            padding: 15px;
          }
        }
      </style>
    </head>
    <body>
      ${content}
      <script>
        window.onload = function() {
          window.print();
          setTimeout(function() {
            window.close();
          }, 1000);
        };
      </script>
    </body>
    </html>
  `;

  printWindow.document.write(printDocument);
  printWindow.document.close();
};
