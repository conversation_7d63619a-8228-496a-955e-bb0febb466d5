# Dependencies
node_modules
.pnp
.pnp.js
.yarn/install-state.gz

# Testing
/coverage

# Next.js
/.next/
/out/

# Production
/build

# Misc
.DS_Store
*.pem

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local env files
.env*.local

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Git
.git
.gitignore

# Docker
Dockerfile
.dockerignore
docker-compose.yml

# IDE
.idea
.vscode

# Old dashboard
old_dashboard
