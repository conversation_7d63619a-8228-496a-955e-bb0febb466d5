import crypto from 'crypto';

/**
 * Generates a signature for API requests
 * @param {Object} params - Parameters for signature generation
 * @param {string} params.queryName - Name of the GraphQL operation
 * @param {Object} params.variables - Variables passed to the GraphQL operation
 * @param {string} params.nonce - Unique identifier for the request
 * @param {string} params.timestamp - ISO timestamp of the request
 * @returns {string} - Generated signature
 */
export const signatureGenerator = ({
  queryName,
  variables,
  nonce,
  timestamp,
}: {
  queryName: string;
  variables: any;
  nonce: string;
  timestamp: string;
}): string => {
  const queryParams = JSON.stringify(variables);
  const message = `${queryName}${queryParams}timestamp=${timestamp}nonce=${nonce}`;
  return hmacSha256Hex('carwah-web', message);
};

/**
 * Creates an HMAC SHA256 hex digest
 * @param {string} key - The secret key for HMAC
 * @param {string} message - The message to hash
 * @returns {string} - Hex digest of the HMAC
 */
const hmacSha256Hex = (key: string, message: string): string => {
  return crypto.createHmac('sha256', key).update(message).digest('hex');
};
