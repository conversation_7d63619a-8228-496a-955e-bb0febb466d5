import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  Select,
  MenuItem,
  InputLabel,
  FormHelperText,
} from '@mui/material';

interface Insurance {
  id: string;
  insuranceName: string;
  price?: number;
}

interface InsuranceSelectionSectionProps {
  insurances: Insurance[];
  selectedInsurance: Insurance | null;
  onInsuranceChange: (insurance: Insurance | null) => void;
  disabled?: boolean;
  clicked?: boolean;
  isLoading?: boolean;
  hasSelectedCar: boolean;
}

export default function InsuranceSelectionSection({
  insurances,
  selectedInsurance,
  onInsuranceChange,
  disabled = false,
  clicked = false,
  isLoading = false,
  hasSelectedCar,
}: InsuranceSelectionSectionProps) {
  const { t, i18n } = useTranslation();

  const isRequired = insurances && insurances.length > 0;
  const hasError = clicked && isRequired && !selectedInsurance;

  const handleInsuranceChange = (insuranceId: string) => {
    const insurance = insurances.find((ins) => ins.id === insuranceId) || null;
    onInsuranceChange(insurance);
  };

  if (!hasSelectedCar) {
    return (
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          {t('select.insurance')}
        </Typography>
        <Typography variant="body2" color="textSecondary">
          {t('select.car.first.to.see.insurances')}
        </Typography>
      </Paper>
    );
  }

  if (!insurances || insurances.length === 0) {
    return (
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          {t('select.insurance')}
        </Typography>
        <Typography variant="body2" color="textSecondary">
          {t('no.insurances.available.for.this.car')}
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        {t('select.insurance')}
      </Typography>

      <Box sx={{ mt: 2 }}>
        <FormControl fullWidth error={hasError}>
          <InputLabel id="insurance-select-label">{t('select.insurance')}</InputLabel>
          <Select
            labelId="insurance-select-label"
            value={selectedInsurance?.id || ''}
            onChange={(event) => handleInsuranceChange(event.target.value)}
            label={t('select.insurance')}
            disabled={disabled}
          >
            <MenuItem value="">
              <em>{t('select.insurance')}</em>
            </MenuItem>
            {insurances.map((insurance) => (
              <MenuItem key={insurance.id} value={insurance.id}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
                  <Typography variant="body1">{t(insurance.insuranceName)}</Typography>
                  {insurance.price && (
                    <Typography variant="body2" color="primary">
                      {insurance.price} {t('currency.sr')}
                    </Typography>
                  )}
                </Box>
              </MenuItem>
            ))}
          </Select>
          {hasError && <FormHelperText>{t('This field is required')}</FormHelperText>}
        </FormControl>
      </Box>

      {selectedInsurance && (
        <Box
          sx={{
            mt: 2,
            p: 2,
            bgcolor: 'background.paper',
            border: 1,
            borderColor: 'divider',
            borderRadius: 1,
          }}
        >
          <Typography variant="body1">
            <strong>{t('selected.insurance')}:</strong> {t(selectedInsurance.insuranceName)}
          </Typography>
          {selectedInsurance.price && (
            <Typography variant="body2" color="primary">
              <strong>{t('insurance.price')}:</strong> {selectedInsurance.price} {t('currency.sr')}
            </Typography>
          )}
        </Box>
      )}
    </Paper>
  );
}
