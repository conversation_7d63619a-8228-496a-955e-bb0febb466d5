'use client';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import Link from 'next/link';
// Component imports
import TimelineModal from './TimelineModal';
import ExtendModal from './ExtendModal';
import ChangeStatusModal from './ChangeStatusModal';
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TableSortLabel,
  Paper,
  Chip,
  IconButton,
  Typography,
  useTheme,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  CircularProgress,
} from '@mui/material';
import {
  Edit as EditIcon,
  Print as PrintIcon,
  History as HistoryIcon,
  MoreVert as MoreVertIcon,
  Cached as CachedIcon,
  NewReleases as NewReleasesIcon,
  Visibility as VisibilityIcon,
} from '@mui/icons-material';
import { visuallyHidden } from '@mui/utils';
import { format } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import { SortDirection } from '@mui/material/TableCell';

// Define column types
type Order = 'asc' | 'desc';

interface Column {
  id: string;
  label: string;
  minWidth?: number;
  align?: 'right' | 'left' | 'center';
  format?: (value: any, booking: any) => React.ReactNode;
  sortable?: boolean;
}

interface BookingsTableProps {
  bookings: any[];
  page: number;
  limit: number;
  total: number;
  onPageChange: (page: number) => void;
  onLimitChange: (limit: number) => void;
  onSortChange: (field: string, direction: 'asc' | 'desc') => void;
  orderBy: string;
  sortBy: string;
  refetch?: () => void;
}

export default function BookingsTable({
  bookings,
  page,
  limit,
  total,
  onPageChange,
  onLimitChange,
  onSortChange,
  orderBy,
  sortBy,
  refetch = () => {}, // Default empty function if not provided
}: BookingsTableProps) {
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const isRTL = i18n.language === 'ar';

  // State for action menu
  const [actionMenuAnchorEl, setActionMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedBookingId, setSelectedBookingId] = useState<number | null>(null);
  const [timelineModalOpen, setTimelineModalOpen] = useState(false);
  const [extendModalOpen, setExtendModalOpen] = useState(false);
  const [changeStatusModalOpen, setChangeStatusModalOpen] = useState(false);
  const [printLoading, setPrintLoading] = useState(false);

  // Define columns
  const columns: Column[] = [
    {
      id: 'id',
      label: t('ID'),
      minWidth: 70,
      align: isRTL ? 'right' : 'left',
      format: (value) => (
        <Link
          href={`/bookings/${value}`}
          style={{ color: theme.palette.primary.main, textDecoration: 'none', fontWeight: 500 }}
        >
          {value}
        </Link>
      ),
      sortable: true,
    },
    {
      id: 'bookingNo',
      label: t('Booking No.'),
      minWidth: 100,
      align: isRTL ? 'right' : 'left',
      format: (value, booking) => (
        <Link
          href={`/bookings/${booking.id}`}
          style={{ color: theme.palette.primary.main, textDecoration: 'none' }}
        >
          {value}
        </Link>
      ),
      sortable: true,
    },
    {
      id: 'customerName',
      label: t('Customer'),
      minWidth: 200,
      align: isRTL ? 'right' : 'left',
      format: (value, booking) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Link
            href={`/customers/${booking.id}`}
            style={{ color: theme.palette.primary.main, textDecoration: 'none' }}
          >
            {value}
          </Link>
          <Typography
            variant="caption"
            color="text.secondary"
            sx={{ whiteSpace: 'nowrap', display: 'block' }}
          >
            {booking.customerMobile}
          </Typography>
        </Box>
      ),
      sortable: true,
    },
    {
      id: 'allyName',
      label: t('Ally'),
      minWidth: 150,
      align: isRTL ? 'right' : 'left',
      format: (_value, booking) => (
        <Link
          href={`/allies/${booking.allyCompanyId}`}
          style={{ color: theme.palette.primary.main, textDecoration: 'none' }}
        >
          {isRTL ? booking.arAllyName : booking.enAllyName}
        </Link>
      ),
      sortable: true,
    },
    {
      id: 'car',
      label: t('Car'),
      minWidth: 250,
      align: isRTL ? 'right' : 'left',
      format: (_value, booking) => {
        const makeName = isRTL ? booking.arMakeName : booking.enMakeName;
        const modelName = isRTL ? booking.arModelName : booking.enModelName;
        const versionName = isRTL ? booking.arVersionName : booking.enVersionName;

        return (
          <Link
            href={`/cars/${booking.carId}`}
            style={{ color: theme.palette.primary.main, textDecoration: 'none' }}
          >
            {`${makeName} ${modelName} ${versionName} ${booking.year}`}
          </Link>
        );
      },
    },
    {
      id: 'numberOfDays',
      label: t('Days'),
      minWidth: 70,
      align: 'center',
      format: (value, booking) =>
        booking.lastConfirmedExtensionRequest
          ? booking.lastConfirmedExtensionRequest.numberOfDays
          : value,
      sortable: true,
    },
    {
      id: 'pricePerDay',
      label: t('Price/Day'),
      minWidth: 100,
      align: 'center',
      format: (value) => `${value} ${t('SAR')}`,
      sortable: true,
    },
    {
      id: 'totalBookingPrice',
      label: t('Total'),
      minWidth: 100,
      align: 'center',
      format: (value, booking) =>
        `${
          booking.lastConfirmedExtensionRequest
            ? booking.lastConfirmedExtensionRequest.totalBookingPrice
            : value
        } ${t('SAR')}`,
      sortable: true,
    },
    {
      id: 'pickUpDate',
      label: t('Pick-up'),
      minWidth: 230,
      align: 'center',
      format: (value, booking) => {
        const locale = i18n.language === 'ar' ? ar : enUS;
        const date = new Date(value);
        const [hours, minutes] = booking.pickUpTime.split(':');
        const time = new Date(2000, 0, 1, parseInt(hours), parseInt(minutes));

        return (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 1,
            }}
          >
            <Typography variant="body2" sx={{ whiteSpace: 'nowrap', fontSize: '0.8rem' }}>
              {format(date, 'dd MMM yyyy', { locale })}
            </Typography>
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{ whiteSpace: 'nowrap', fontSize: '0.7rem', ml: 1 }}
            >
              {format(time, 'hh:mm a', { locale })}
            </Typography>
          </Box>
        );
      },
      sortable: true,
    },
    {
      id: 'dropOffDate',
      label: t('Drop-off'),
      minWidth: 230,
      align: 'center',
      format: (value, booking) => {
        const locale = i18n.language === 'ar' ? ar : enUS;
        let date = new Date(value);
        const [hours, minutes] = booking.dropOffTime.split(':');
        let time = new Date(2000, 0, 1, parseInt(hours), parseInt(minutes));

        // Use extension data if available
        if (booking.lastConfirmedExtensionRequest) {
          date = new Date(booking.lastConfirmedExtensionRequest.dropOffDate);
        }

        return (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 1,
            }}
          >
            <Typography variant="body2" sx={{ whiteSpace: 'nowrap', fontSize: '0.8rem' }}>
              {format(date, 'dd MMM yyyy', { locale })}
            </Typography>
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{ whiteSpace: 'nowrap', fontSize: '0.7rem', ml: 1 }}
            >
              {format(time, 'hh:mm a', { locale })}
            </Typography>
          </Box>
        );
      },
      sortable: true,
    },
    {
      id: 'status',
      label: t('Status'),
      minWidth: 180,
      align: 'center',
      format: (value, booking) => {
        // Define color based on status
        let color: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' =
          'default';

        switch (value) {
          case 'pending':
            color = 'warning';
            break;
          case 'confirmed':
            color = 'primary';
            break;
          case 'car_received':
            color = 'success';
            break;
          case 'invoiced':
            color = 'info';
            break;
          case 'closed':
            color = 'secondary';
            break;
          case 'cancelled':
            color = 'error';
            break;
          default:
            color = 'default';
        }

        return (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 1,
            }}
          >
            <Chip
              label={t(
                value.replace('_', ' ').charAt(0).toUpperCase() + value.replace('_', ' ').slice(1)
              )}
              color={color}
              size="small"
              sx={{
                fontWeight: 500,
                minWidth: 90,
                maxWidth: '100%',
              }}
            />

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, ml: 1 }}>
              {booking.subStatus && (
                <Typography
                  variant="caption"
                  color="text.secondary"
                  sx={{ whiteSpace: 'nowrap', fontSize: '0.7rem' }}
                >
                  {t(booking.subStatus.replace('_', ' '))}
                </Typography>
              )}

              {booking.lastRentalDateExtensionRequest && (
                <Chip
                  label={t('Extension')}
                  size="small"
                  variant="outlined"
                  color="primary"
                  sx={{ fontSize: '0.7rem', height: 20 }}
                />
              )}
            </Box>
          </Box>
        );
      },
    },
    {
      id: 'createdAt',
      label: t('Created At'),
      minWidth: 120,
      align: 'center',
      format: (value) => {
        const locale = i18n.language === 'ar' ? ar : enUS;
        const date = new Date(value);
        return (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 1,
            }}
          >
            <Typography variant="body2" sx={{ whiteSpace: 'nowrap', fontSize: '0.8rem' }}>
              {format(date, 'dd MMM yyyy', { locale })}
            </Typography>
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{ whiteSpace: 'nowrap', fontSize: '0.7rem' }}
            >
              {format(date, 'hh:mm a', { locale })}
            </Typography>
          </Box>
        );
      },
      sortable: true,
    },
    {
      id: 'actions',
      label: t('Actions'),
      minWidth: 100,
      align: 'center',
      format: (_value, booking) => (
        <Box>
          <IconButton
            aria-label={t('More actions')}
            size="small"
            onClick={(event) => handleActionMenuOpen(event, booking.id)}
          >
            <MoreVertIcon />
          </IconButton>
        </Box>
      ),
    },
  ];

  // Handle sort request
  const handleRequestSort = (property: string) => {
    const isAsc = orderBy === property && sortBy === 'asc';
    onSortChange(property, isAsc ? 'desc' : 'asc');
  };

  // Handle page change
  const handleChangePage = (_event: unknown, newPage: number) => {
    onPageChange(newPage + 1); // API uses 1-based indexing
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newLimit = parseInt(event.target.value, 10);
    // Log limit change for debugging
    onLimitChange(newLimit);
    onPageChange(1); // Reset to first page when changing rows per page
  };

  // Handle action menu open
  const handleActionMenuOpen = (event: React.MouseEvent<HTMLElement>, bookingId: number) => {
    setActionMenuAnchorEl(event.currentTarget);
    setSelectedBookingId(bookingId);
  };

  // Handle action menu close
  const handleActionMenuClose = () => {
    setActionMenuAnchorEl(null);
    setSelectedBookingId(null);
  };

  // Status color function removed as it's not being used

  return (
    <Paper
      sx={{
        width: '100%',
        overflow: 'hidden',
        boxShadow: 'none',
        position: 'relative',
        maxWidth: '100vw',
      }}
    >
      {/* Loading overlay */}
      {printLoading && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor:
              theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.7)' : 'rgba(255, 255, 255, 0.7)',
            zIndex: 10,
          }}
        >
          <Box sx={{ textAlign: 'center' }}>
            <CircularProgress size={40} />
            <Typography variant="body2" sx={{ mt: 2 }}>
              {t('Printing...')}
            </Typography>
          </Box>
        </Box>
      )}
      <TableContainer
        sx={{
          maxHeight: 'calc(100vh - 300px)', // Adjusted for smaller header
          overflowY: 'auto',
          overflowX: 'auto',
          width: '100%',
          maxWidth: '100%',
          '&::-webkit-scrollbar': {
            height: '8px',
            width: '8px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor:
              theme.palette.mode === 'dark' ? 'rgba(241, 146, 35, 0.5)' : 'rgba(0, 0, 0, 0.2)',
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor:
              theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
          },
        }}
      >
        <Table
          stickyHeader
          aria-label={t('Bookings table')}
          sx={{
            minWidth: { xs: 800, md: 1600 }, // Increased width to accommodate all columns without truncation
            tableLayout: 'auto',
            width: '100%',
          }}
        >
          <TableHead>
            <TableRow sx={{ height: '40px', '& th': { fontWeight: 600 } }}>
              {columns.map((column) => (
                <TableCell
                  key={column.id}
                  align={column.align}
                  style={{ minWidth: column.minWidth }}
                  sortDirection={orderBy === column.id ? (sortBy as SortDirection) : false}
                  sx={{
                    height: '40px',
                    padding: '4px 16px',
                    whiteSpace: 'nowrap',
                    verticalAlign: 'middle',
                    overflow: 'visible',
                    minWidth: column.minWidth ? `${column.minWidth}px` : 'auto',
                    width: 'auto',
                  }}
                >
                  {column.sortable ? (
                    <TableSortLabel
                      active={orderBy === column.id}
                      direction={orderBy === column.id ? (sortBy as Order) : 'asc'}
                      onClick={() => handleRequestSort(column.id)}
                      sx={{
                        whiteSpace: 'nowrap',
                        minHeight: '24px',
                        padding: 0,
                        '& .MuiTableSortLabel-icon': {
                          fontSize: '1rem',
                        },
                      }}
                    >
                      {column.label}
                      {orderBy === column.id ? (
                        <Box component="span" sx={visuallyHidden}>
                          {sortBy === 'desc' ? 'sorted descending' : 'sorted ascending'}
                        </Box>
                      ) : null}
                    </TableSortLabel>
                  ) : (
                    column.label
                  )}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {bookings.map((booking) => (
              <TableRow
                hover
                role="checkbox"
                tabIndex={-1}
                key={booking.id}
                sx={{
                  '&:last-child td, &:last-child th': { border: 0 },
                  '&:nth-of-type(odd)': {
                    backgroundColor:
                      theme.palette.mode === 'dark'
                        ? 'rgba(255, 255, 255, 0.03)'
                        : 'rgba(0, 0, 0, 0.02)',
                  },
                  '&:hover': {
                    backgroundColor:
                      theme.palette.mode === 'dark'
                        ? 'rgba(255, 255, 255, 0.08)'
                        : 'rgba(0, 0, 0, 0.04)',
                  },
                  '& td': {
                    py: 0.5, // Reduced vertical padding
                    height: '40px', // Fixed small height for all rows
                    whiteSpace: 'nowrap', // Keep content on one line
                    overflow: 'visible',
                    padding: '4px 16px', // Reduced vertical padding for compact rows
                    minWidth: 'fit-content', // Allow cells to expand as needed
                  },
                }}
              >
                {columns.map((column) => {
                  const value = booking[column.id];
                  return (
                    <TableCell
                      key={column.id}
                      align={column.align}
                      title={typeof value === 'string' ? value : ''}
                      sx={{
                        minWidth: column.minWidth ? `${column.minWidth}px` : 'auto',
                        width: 'auto',
                        overflow: 'visible',
                        textOverflow: 'clip',
                        position: 'relative',
                        zIndex: 1,
                      }}
                    >
                      {column.format ? column.format(value, booking) : value}
                    </TableCell>
                  );
                })}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <TablePagination
        rowsPerPageOptions={[10, 25, 50, 100]}
        component="div"
        count={total}
        rowsPerPage={limit}
        page={page - 1} // API uses 1-based indexing
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        labelRowsPerPage={t('Rows per page:')}
        labelDisplayedRows={({ from, to, count }) => `${from}-${to} ${t('of')} ${count}`}
        getItemAriaLabel={(type) =>
          type === 'first'
            ? t('Go to first page')
            : type === 'last'
            ? t('Go to last page')
            : type === 'next'
            ? t('Go to next page')
            : t('Go to previous page')
        }
        showFirstButton
        showLastButton
        sx={{
          '.MuiTablePagination-selectLabel': {
            fontWeight: 500,
          },
          '.MuiTablePagination-displayedRows': {
            fontWeight: 500,
          },
          '.MuiTablePagination-select': {
            fontWeight: 500,
          },
        }}
      />

      {/* Actions Menu */}
      <Menu
        anchorEl={actionMenuAnchorEl}
        open={Boolean(actionMenuAnchorEl)}
        onClose={handleActionMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem
          component={Link}
          href={`/bookings/${selectedBookingId}`}
          onClick={handleActionMenuClose}
        >
          <ListItemIcon>
            <VisibilityIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('View Details')}</ListItemText>
        </MenuItem>

        <MenuItem
          component={Link}
          href={`/bookings/${selectedBookingId}/edit`}
          onClick={handleActionMenuClose}
        >
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('Edit')}</ListItemText>
        </MenuItem>

        <MenuItem
          onClick={() => {
            handleActionMenuClose();
            setPrintLoading(true);
            // Simulate printing - replace with actual print functionality
            setTimeout(() => {
              setPrintLoading(false);
            }, 2000);
          }}
        >
          <ListItemIcon>
            <PrintIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('Print')}</ListItemText>
        </MenuItem>

        <MenuItem
          onClick={() => {
            handleActionMenuClose();
            setExtendModalOpen(true);
          }}
        >
          <ListItemIcon>
            <CachedIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('Extend')}</ListItemText>
        </MenuItem>

        <MenuItem
          onClick={() => {
            handleActionMenuClose();
            setChangeStatusModalOpen(true);
          }}
        >
          <ListItemIcon>
            <NewReleasesIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('Change Status')}</ListItemText>
        </MenuItem>

        <MenuItem
          onClick={() => {
            handleActionMenuClose();
            setTimelineModalOpen(true);
          }}
        >
          <ListItemIcon>
            <HistoryIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('Timeline')}</ListItemText>
        </MenuItem>
      </Menu>

      {/* Modals */}
      {selectedBookingId && (
        <>
          <TimelineModal
            open={timelineModalOpen}
            onClose={() => setTimelineModalOpen(false)}
            bookingId={selectedBookingId.toString()}
          />

          <ExtendModal
            open={extendModalOpen}
            onClose={() => setExtendModalOpen(false)}
            bookingId={selectedBookingId.toString()}
            onSuccess={() => refetch()}
            currentEndDate={new Date().toISOString()}
            pricePerDay={100}
          />

          <ChangeStatusModal
            open={changeStatusModalOpen}
            onClose={() => setChangeStatusModalOpen(false)}
            bookingId={selectedBookingId.toString()}
            currentStatus="pending"
            onStatusChanged={() => {
              setChangeStatusModalOpen(false);
              // Refresh data if needed
            }}
          />
        </>
      )}
    </Paper>
  );
}
