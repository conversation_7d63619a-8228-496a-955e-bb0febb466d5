import React from 'react';
import { <PERSON>complete, TextField, Chip, CircularProgress } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@apollo/client';
import { GET_VEHICLES } from '../../gql/queries/vehicles';

interface Ally {
  id: number | string;
  name: string;
  arName: string;
}

interface VehicleTypesDropdownProps {
  filters: {
    vehicleType?: string | null;
    [key: string]: any;
  };
  handleAutocompleteChange: (field: string, value: string | null) => void;
}

const VehicleTypesDropdown: React.FC<VehicleTypesDropdownProps> = ({ 
  filters,
  handleAutocompleteChange,
}) => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  
  const { loading, error, data } = useQuery(GET_VEHICLES, {
    fetchPolicy: 'cache-and-network',
    variables: {
      orderBy: "en_name",
      sortBy: "asc"
    }
  });

  const vehivleTypes = data?.vehicleTypes || [];
  return (
    <Autocomplete 
      id="vehicleType" 
      options={vehivleTypes} 
      loading={loading}
      getOptionLabel={(option) => (isRTL ? option.arName : option.name)} 
      value={ 
        filters.vehicleType 
          ? vehivleTypes.find((ally:any) => ally.id.toString() === filters.vehicleType) || null 
          : null 
      } 
      onChange={(_, newValue) => 
        handleAutocompleteChange('vehicleType', newValue?.id?.toString() || null) 
      } 
      renderInput={(params) => ( 
        <TextField 
          {...params} 
          label={t('vehicleType')} 
          variant="outlined" 
          size="small" 
          InputLabelProps={{ 
            sx: { padding: '0 20px' }, 
          }}
          InputProps={{
            ...params.InputProps,
            endAdornment: (
              <>
                {loading ? <CircularProgress color="inherit" size={20} /> : null}
                {params.InputProps.endAdornment}
              </>
            ),
          }}
          error={!!error}
          helperText={error ? t('Error loading vehivleTypes') : ''}
        /> 
      )} 
      renderTags={(value, getTagProps) => 
        value.map((option, index) => ( 
          <Chip 
            label={isRTL ? option.arName : option.name} 
            {...getTagProps({ index })} 
            size="small" 
          /> 
        )) 
      }
    />
  );
};

export default VehicleTypesDropdown;