'use client';

import React from 'react';
import {
  Box,
  Button,
  FormControl,
  FormHelperText,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  TextField,
  Switch,
  FormControlLabel,
  InputAdornment,
} from '@mui/material';
import { Controller, FieldValues } from 'react-hook-form';
import { FormField, GenericFormProps } from './types';

export function GenericForm<T extends FieldValues>({
  fields,
  onSubmit,
  control,
  isLoading = false,
  submitText = 'Submit',
  cancelText = 'Cancel',
  onCancel,
}: GenericFormProps<T>) {
  const renderField = (field: FormField<T>) => {
    const commonProps = {
      fullWidth: true,
      disabled: isLoading || field.disabled,
      label: field.label,
      placeholder: field.placeholder,
      required: field.required,
    };

    return (
      <Controller
        name={field.name}
        control={control}
        render={({ field: { onChange, value, ref }, fieldState: { error } }) => {
          switch (field.type) {
            case 'select':
              return (
                <FormControl fullWidth error={!!error}>
                  <InputLabel>{field.label}</InputLabel>
                  <Select
                    {...commonProps}
                    value={value || ''}
                    onChange={onChange}
                    inputRef={ref}
                  >
                    {field.options?.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {(error?.message || field.helperText) && (
                    <FormHelperText>{error?.message || field.helperText}</FormHelperText>
                  )}
                </FormControl>
              );

            case 'switch':
              return (
                <FormControl fullWidth error={!!error}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={value || false}
                        onChange={(e) => onChange(e.target.checked)}
                        disabled={isLoading || field.disabled}
                      />
                    }
                    label={field.label}
                  />
                  {(error?.message || field.helperText) && (
                    <FormHelperText>{error?.message || field.helperText}</FormHelperText>
                  )}
                </FormControl>
              );

            default:
              return (
                <TextField
                  {...commonProps}
                  type={field.type}
                  value={value || ''}
                  onChange={onChange}
                  inputRef={ref}
                  error={!!error}
                  helperText={error?.message || field.helperText}
                  InputProps={{
                    startAdornment: field.startAdornment && (
                      <InputAdornment position="start">{field.startAdornment}</InputAdornment>
                    ),
                    endAdornment: field.endAdornment && (
                      <InputAdornment position="end">{field.endAdornment}</InputAdornment>
                    ),
                  }}
                />
              );
          }
        }}
      />
    );
  };

  return (
    <Box component="form" onSubmit={onSubmit as any} noValidate>
      <Grid container spacing={2}>
        {fields.map((field, index) => (
          <Grid item xs={12} sm={6} key={field.name}>
            {renderField(field)}
          </Grid>
        ))}
      </Grid>
      <Stack direction="row" spacing={2} sx={{ mt: 3 }}>
        {onCancel && (
          <Button
            variant="outlined"
            onClick={onCancel}
            disabled={isLoading}
          >
            {cancelText}
          </Button>
        )}
        <Button
          type="submit"
          variant="contained"
          disabled={isLoading}
        >
          {submitText}
        </Button>
      </Stack>
    </Box>
  );
}
