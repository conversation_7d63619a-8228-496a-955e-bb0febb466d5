import { ReactNode } from 'react';
import { Control, FieldValues, Path } from 'react-hook-form';

export type FieldType = 'text' | 'number' | 'select' | 'date' | 'switch' | 'password';

export interface FormField<T extends FieldValues> {
  name: Path<T>;
  label: string;
  type: FieldType;
  required?: boolean;
  options?: { label: string; value: any }[];
  disabled?: boolean;
  placeholder?: string;
  helperText?: string;
  startAdornment?: ReactNode;
  endAdornment?: ReactNode;
}

export interface GenericFormProps<T extends FieldValues> {
  fields: FormField<T>[];
  onSubmit: (data: T) => void;
  control: Control<T>;
  isLoading?: boolean;
  submitText?: string;
  cancelText?: string;
  onCancel?: () => void;
}
