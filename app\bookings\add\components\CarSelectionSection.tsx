import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  Autocomplete,
  TextField,
  CircularProgress,
  Al<PERSON>,
  Chip,
} from '@mui/material';

interface Car {
  id: string;
  carModel: {
    id: string;
    arName: string;
    enName: string;
  };
  make: {
    id: string;
    arName: string;
    enName: string;
  };
  carVersion: {
    id: string;
    arName: string;
    enName: string;
  };
  year: number;
  plateNumber: string;
  color: {
    arName: string;
    enName: string;
  };
  carInsurances?: Array<{
    id: string;
    insuranceName: string;
  }>;
}

interface CarSelectionSectionProps {
  cars: Car[];
  selectedCar: Car | null;
  onCarChange: (car: Car | null) => void;
  disabled?: boolean;
  clicked?: boolean;
  isLoading?: boolean;
  hasSelectedBranch: boolean;
}

export default function CarSelectionSection({
  cars,
  selectedCar,
  onCarChange,
  disabled = false,
  clicked = false,
  isLoading = false,
  hasSelectedBranch,
}: CarSelectionSectionProps) {
  const { t } = useTranslation();

  const isRequired = true;
  const hasError = clicked && isRequired && !selectedCar;

  const getCarDisplayName = (car: Car) => {
    const make = car.make?.enName;
    const model = car.carModel?.enName;
    const version = car.carVersion?.enName;
    const color = car.color?.enName;

    return `${make} ${model} ${version} ${car.year} - ${color} (${car.plateNumber})`;
  };

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        {t('select.car')}
      </Typography>

      <Box sx={{ mt: 2 }}>
        <FormControl fullWidth error={hasError}>
          <Autocomplete
            options={cars || []}
            value={selectedCar}
            onChange={(event, newValue) => onCarChange(newValue)}
            getOptionLabel={(option) => getCarDisplayName(option)}
            isOptionEqualToValue={(option, value) => option.id === value.id}
            loading={isLoading}
            disabled={disabled || isLoading || !hasSelectedBranch}
            renderInput={(params) => (
              <TextField
                {...params}
                label={t('select.car')}
                placeholder={hasSelectedBranch ? t('select.car') : t('select.branch.first')}
                error={hasError}
                helperText={hasError ? t('This field is required') : ''}
                InputProps={{
                  ...params.InputProps,
                  endAdornment: (
                    <>
                      {isLoading ? <CircularProgress color="inherit" size={20} /> : null}
                      {params.InputProps.endAdornment}
                    </>
                  ),
                }}
              />
            )}
            renderOption={(props, option) => (
              <Box component="li" {...props}>
                <Box>
                  <Typography variant="body1">{getCarDisplayName(option)}</Typography>
                  {option.carInsurances && option.carInsurances.length > 0 && (
                    <Box sx={{ mt: 0.5 }}>
                      <Chip
                        label={`${option.carInsurances.length} ${t('insurances.available')}`}
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                    </Box>
                  )}
                </Box>
              </Box>
            )}
          />
        </FormControl>

        {/* Car Details Display */}
        {selectedCar && (
          <Alert severity="info" sx={{ mt: 2 }}>
            <Typography variant="body1">
              <strong>{t('selected.car')}:</strong>
            </Typography>
            <Typography variant="body2">
              {selectedCar.make?.enName} -{selectedCar.carModel?.enName} -
              {selectedCar.carVersion?.enName} -{selectedCar.year}
            </Typography>
            <Typography variant="body2">
              <strong>{t('plate.number')}:</strong> {selectedCar.plateNumber}
            </Typography>
            <Typography variant="body2">
              <strong>{t('color')}:</strong> {selectedCar.color?.enName}
            </Typography>
            {selectedCar.carInsurances && selectedCar.carInsurances.length > 0 && (
              <Typography variant="body2">
                <strong>{t('available.insurances')}:</strong> {selectedCar.carInsurances.length}
              </Typography>
            )}
          </Alert>
        )}
      </Box>
    </Paper>
  );
}
