import React from 'react';
import { useTranslation } from 'react-i18next';
import { Box, Button, IconButton, Tooltip } from '@mui/material';
import { Print, Map, Edit, Delete } from '@mui/icons-material';
import { printBookingDetails } from '../../utils/print';

interface BookingData {
  id: string;
  bookingNo: string;
  customerName: string;
  customerMobile: string;
  customerEmail?: string;
  carMake?: string;
  carModel?: string;
  carYear?: string;
  pickUpDate: string;
  dropOffDate: string;
  numberOfDays: number;
  totalBookingPrice: number;
  status: string;
  branchName?: string;
  createdAt: string;
}

interface BookingActionsProps {
  booking: BookingData;
  onEdit?: () => void;
  onDelete?: () => void;
  onShowMap?: () => void;
  showPrint?: boolean;
  showMap?: boolean;
  showEdit?: boolean;
  showDelete?: boolean;
}

const BookingActions: React.FC<BookingActionsProps> = ({
  booking,
  onEdit,
  onDelete,
  onShowMap,
  showPrint = true,
  showMap = true,
  showEdit = true,
  showDelete = true,
}) => {
  const { t } = useTranslation();

  const handlePrint = () => {
    printBookingDetails(booking, t);
  };

  return (
    <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
      {showPrint && (
        <Tooltip title={t('print.bookingDetails')}>
          <IconButton
            onClick={handlePrint}
            color="primary"
            size="small"
            sx={{
              backgroundColor: 'rgba(25, 118, 210, 0.04)',
              '&:hover': {
                backgroundColor: 'rgba(25, 118, 210, 0.08)',
              },
            }}
          >
            <Print fontSize="small" />
          </IconButton>
        </Tooltip>
      )}

      {showMap && onShowMap && (
        <Tooltip title={t('map.location')}>
          <IconButton
            onClick={onShowMap}
            color="primary"
            size="small"
            sx={{
              backgroundColor: 'rgba(25, 118, 210, 0.04)',
              '&:hover': {
                backgroundColor: 'rgba(25, 118, 210, 0.08)',
              },
            }}
          >
            <Map fontSize="small" />
          </IconButton>
        </Tooltip>
      )}

      {showEdit && onEdit && (
        <Tooltip title={t('common.edit')}>
          <IconButton
            onClick={onEdit}
            color="primary"
            size="small"
            sx={{
              backgroundColor: 'rgba(25, 118, 210, 0.04)',
              '&:hover': {
                backgroundColor: 'rgba(25, 118, 210, 0.08)',
              },
            }}
          >
            <Edit fontSize="small" />
          </IconButton>
        </Tooltip>
      )}

      {showDelete && onDelete && (
        <Tooltip title={t('common.delete')}>
          <IconButton
            onClick={onDelete}
            color="error"
            size="small"
            sx={{
              backgroundColor: 'rgba(211, 47, 47, 0.04)',
              '&:hover': {
                backgroundColor: 'rgba(211, 47, 47, 0.08)',
              },
            }}
          >
            <Delete fontSize="small" />
          </IconButton>
        </Tooltip>
      )}
    </Box>
  );
};

export default BookingActions;
