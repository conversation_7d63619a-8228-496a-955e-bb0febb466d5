'use client';
import React from 'react';
import { Container, Typography, Box } from '@mui/material';
import BookingAssignment from '../bookings/components/BookingAssignment';

export default function TestAssignmentPage() {
  // Test with a sample booking ID
  const testBookingId = '1'; // You can change this to test with different booking IDs

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Booking Assignment Test Page
      </Typography>

      <Box sx={{ mt: 3 }}>
        <Typography variant="h6" gutterBottom>
          Testing Booking ID: {testBookingId}
        </Typography>

        <BookingAssignment bookingId={testBookingId} refetchBooking={() => {}} />
      </Box>
    </Container>
  );
}
