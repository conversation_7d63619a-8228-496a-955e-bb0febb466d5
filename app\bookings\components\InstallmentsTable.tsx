'use client';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation, useQuery, gql } from '@apollo/client';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Select,
  MenuItem,
  FormControl,
  Button,
  IconButton,
  Chip,
  Box,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Tooltip,
  Alert,
  SelectChangeEvent,
} from '@mui/material';
import {
  Edit as EditIcon,
  Refresh as RefreshIcon,
  Payment as PaymentIcon,
  Replay as ReplayIcon,
  Send as SendIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';

// GraphQL mutations - matching old dashboard exactly
const UPDATE_INSTALLMENT_STATUS = gql`
  mutation UpdateInstallmentStatus($installmentId: ID!, $status: String!) {
    updateInstallmentStatus(installmentId: $installmentId, status: $status) {
      success
      message
    }
  }
`;

const REFUND_INSTALLMENT = gql`
  mutation RefundInstallment($amount: Float, $installmentId: ID!, $toWallet: Boolean) {
    refundInstallment(amount: $amount, installmentId: $installmentId, toWallet: $toWallet) {
      errors
      installment {
        id
        amount
        status
        statusLocalized
        refundedAmount
        totalPaidAmount
      }
      status
    }
  }
`;

const INSTALLMENT_BANK_TRANSFER_REFUND = gql`
  mutation InstallmentBankTransferRefund($installmentId: ID!, $amount: Float!) {
    installmentBankTransferRefund(installmentId: $installmentId, amount: $amount) {
      success
      errors
    }
  }
`;

const RECALL_PAYMENT_GATEWAY = gql`
  query RecallPaymentGateway($rentalId: ID!, $installmentId: ID!) {
    recallPaymentGateway(rentalId: $rentalId, installmentId: $installmentId) {
      success
      message
    }
  }
`;

const RESEND_INSTALLMENT_TO_ALLY = gql`
  mutation ResendInstallmentToAlly($installmentId: ID!) {
    resendInstallmentToAlly(installmentId: $installmentId) {
      success
      message
    }
  }
`;

interface LoyaltyCollection {
  id: string;
  numberOfPoints: number;
  pointPrice: number;
  status: string;
}

interface LastPaidInstallment {
  paymentBrand?: string;
}

interface Installment {
  id: string;
  installmentNumber: number;
  amount: number;
  dueDate: string;
  status: string;
  statusLocalized?: string;
  paymentMethod?: string;
  paymentStatus?: string;
  totalAmountPaidAt?: string;
  walletPaidAmount?: number;
  hasPendingPaymentTransaction?: boolean;
  lastPaidInstallment?: LastPaidInstallment;
  loyaltyCollections?: LoyaltyCollection[];
  canSendToAlly?: boolean;
  refundedAmount?: number;
  totalPaidAmount?: number;
}

interface BookingDetails {
  status: string;
  totalBookingPrice: number;
}

interface InstallmentsTableProps {
  installments: Installment[];
  rentalId: string;
  refetchBooking: () => void;
  bookingDetails: BookingDetails;
}

interface RefundModalProps {
  open: boolean;
  onClose: () => void;
  installment: Installment;
  onRefund: (amount: number, isFullRefund: boolean, isBankTransfer: boolean) => void;
}

function RefundModal({ open, onClose, installment, onRefund }: RefundModalProps) {
  const { t } = useTranslation();
  const [amount, setAmount] = useState<number>(installment?.amount || 0);
  const [error, setError] = useState<string>('');
  const [refundType, setRefundType] = useState<'full' | 'partial' | 'bank'>('full');

  const handleRefund = (type: 'full' | 'partial' | 'bank') => {
    if ((type === 'partial' || type === 'bank') && (!amount || amount <= 0)) {
      setError(t('bookings.installments.amountRequired'));
      return;
    }
    if ((type === 'partial' || type === 'bank') && amount > installment?.amount) {
      setError(t('bookings.installments.amountExceedsInstallment'));
      return;
    }

    const refundAmount = type === 'full' ? installment?.amount : amount;
    const isBankTransfer = type === 'bank';
    onRefund(refundAmount, type === 'full', isBankTransfer);
    onClose();
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>{t('bookings.installments.refundInstallment')}</DialogTitle>
      <DialogContent>
        <Typography variant="body2" sx={{ mb: 2 }}>
          {t('bookings.installments.refundConfirmation')}
        </Typography>

        {refundType !== 'full' && (
          <>
            <TextField
              fullWidth
              label={t('bookings.installments.refundAmount')}
              type="number"
              value={amount}
              onChange={(e) => {
                setAmount(parseFloat(e.target.value));
                setError('');
              }}
              error={!!error}
              helperText={error}
              inputProps={{ max: installment?.amount }}
              sx={{ mt: 2 }}
            />
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              {t('bookings.installments.maximumRefundable')}: {installment?.amount}
            </Typography>
          </>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>{t('common.cancel')}</Button>
        <Button onClick={() => handleRefund('full')} variant="contained" color="info">
          {t('bookings.installments.fullRefund')}
        </Button>
        <Button
          onClick={() => {
            setRefundType('partial');
            handleRefund('partial');
          }}
          variant="contained"
          color="secondary"
        >
          {t('bookings.installments.partialRefund')}
        </Button>
        <Button
          onClick={() => {
            setRefundType('bank');
            handleRefund('bank');
          }}
          variant="contained"
          color="primary"
        >
          {t('bookings.installments.bankTransferRefund')}
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default function InstallmentsTable({
  installments,
  rentalId,
  refetchBooking,
  bookingDetails,
}: InstallmentsTableProps) {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  const dateLocale = isRTL ? ar : enUS;
  const [refundModal, setRefundModal] = useState<{
    open: boolean;
    installment: Installment | null;
  }>({
    open: false,
    installment: null,
  });

  // Mutations - matching old dashboard
  const [updateInstallmentStatus] = useMutation(UPDATE_INSTALLMENT_STATUS);
  const [refundInstallment] = useMutation(REFUND_INSTALLMENT);
  const [installmentBankTransferRefund] = useMutation(INSTALLMENT_BANK_TRANSFER_REFUND);
  const [resendInstallmentToAlly] = useMutation(RESEND_INSTALLMENT_TO_ALLY);
  const { refetch: recallPaymentGateway } = useQuery(RECALL_PAYMENT_GATEWAY, { skip: true });

  const getStatusOptions = (installment: Installment) => {
    const status = installment?.status?.toLowerCase();
    const paymentMethod = installment?.paymentMethod;

    // Exact logic from old dashboard
    if (status === 'not_collected' && paymentMethod === 'CASH') {
      return [
        { value: 'not_collected', label: t('bookings.installments.notCollected') },
        { value: 'not_collectable', label: t('bookings.installments.notCollectable') },
        { value: 'paid', label: t('bookings.installments.paid') },
      ];
    }

    if (status === 'upcoming' && !paymentMethod) {
      return [
        { value: 'upcoming', label: t('bookings.installments.upcoming') },
        { value: 'paid', label: t('bookings.installments.paid') },
      ];
    }

    if (
      (status === 'due' && !paymentMethod) ||
      (status === 'due' && paymentMethod?.toLowerCase() === 'online')
    ) {
      return [
        { value: 'due', label: t('bookings.installments.due') },
        { value: 'paid', label: t('bookings.installments.paid') },
        { value: 'not_collectable', label: t('bookings.installments.notCollectable') },
      ];
    }

    if (status === 'paid' && paymentMethod === 'CASH') {
      return [
        { value: 'paid', label: t('bookings.installments.paid') },
        { value: 'fully_refunded', label: t('bookings.installments.fullyRefunded') },
        { value: 'partially_refunded', label: t('bookings.installments.partiallyRefunded') },
      ];
    }

    if (status === 'overdue' && !paymentMethod) {
      return [
        { value: 'overdue', label: t('bookings.installments.overdue') },
        { value: 'paid', label: t('bookings.installments.paid') },
        { value: 'not_collected', label: t('bookings.installments.notCollected') },
      ];
    }

    return [{ value: status, label: t(`bookings.installments.${status}`) || status }];
  };

  const handleStatusChange = (installment: Installment, newStatus: string) => {
    if (newStatus !== installment?.status?.toLowerCase()) {
      updateInstallmentStatus({
        variables: {
          installmentId: installment?.id,
          status: newStatus.toLowerCase(),
        },
      })
        .then(() => {
          refetchBooking();
        })
        .catch((error) => {
          console.error('Error updating installment status:', error);
        });
    }
  };

  const handleRefund = (
    installment: Installment,
    amount: number,
    isFullRefund: boolean,
    isBankTransfer: boolean
  ) => {
    if (isBankTransfer) {
      installmentBankTransferRefund({
        variables: {
          installmentId: installment?.id,
          amount,
        },
      })
        .then(() => {
          refetchBooking();
        })
        .catch((error) => {
          console.error('Error with bank transfer refund:', error);
        });
    } else {
      // Check if it's TAMARA payment (from old dashboard logic)
      const isTamara = installment?.lastPaidInstallment?.paymentBrand === 'TAMARA';

      refundInstallment({
        variables: {
          installmentId: installment?.id,
          amount: isFullRefund ? undefined : amount,
          toWallet: !isTamara, // Wallet refund for non-TAMARA payments
        },
      })
        .then(() => {
          refetchBooking();
        })
        .catch((error) => {
          console.error('Error refunding installment:', error);
        });
    }
  };

  const handleRecallPaymentGateway = (installment: Installment) => {
    recallPaymentGateway({
      variables: {
        rentalId,
        installmentId: installment?.id,
      },
    })
      .then(() => {
        refetchBooking();
      })
      .catch((error) => {
        console.error('Error recalling payment gateway:', error);
      });
  };

  const handleResendToAlly = (installment: Installment) => {
    resendInstallmentToAlly({
      variables: {
        installmentId: installment?.id,
      },
    })
      .then(() => {
        refetchBooking();
      })
      .catch((error) => {
        console.error('Error resending to ally:', error);
      });
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '-';
    try {
      return format(new Date(dateString), 'dd/MM/yyyy', { locale: dateLocale });
    } catch {
      return '-';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(isRTL ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR',
    }).format(amount || 0);
  };

  const shouldShowEditAction = (installment: Installment) => {
    const status = installment?.status?.toLowerCase();
    const paymentMethod = installment?.paymentMethod;

    return (
      (status === 'not_collected' && paymentMethod === 'CASH') ||
      (status === 'upcoming' && !paymentMethod) ||
      (status === 'due' && !paymentMethod) ||
      (status === 'due' && paymentMethod?.toLowerCase() === 'online') ||
      (status === 'overdue' && !paymentMethod) ||
      (status === 'paid' && paymentMethod === 'CASH')
    );
  };

  const shouldShowRefundAction = (installment: Installment) => {
    return (
      installment?.paymentMethod?.toLowerCase() !== 'cash' &&
      installment?.status?.toLowerCase() === 'paid' &&
      bookingDetails?.status?.toLowerCase() !== 'pending'
    );
  };

  const shouldShowRecallAction = (installment: Installment) => {
    return (
      (installment?.paymentStatus === 'failed' || installment?.hasPendingPaymentTransaction) &&
      installment?.paymentMethod?.toLowerCase() !== 'cash'
    );
  };

  const shouldShowResendToAllyAction = (installment: Installment) => {
    return installment?.canSendToAlly && installment?.installmentNumber !== 1;
  };

  if (!installments?.length) {
    return <Alert severity="info">{t('bookings.installments.noInstallmentsFound')}</Alert>;
  }

  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2 }}>
        {t('bookings.installments.title')}
      </Typography>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>{t('bookings.installments.dueDate')}</TableCell>
              <TableCell>{t('bookings.installments.amount')}</TableCell>
              <TableCell>{t('bookings.installments.status')}</TableCell>
              <TableCell>{t('bookings.installments.paymentMethod')}</TableCell>
              <TableCell>{t('bookings.installments.paymentBrand')}</TableCell>
              <TableCell>{t('bookings.installments.paidAt')}</TableCell>
              <TableCell>{t('bookings.installments.walletValue')}</TableCell>
              <TableCell>{t('bookings.installments.earningValue')}</TableCell>
              <TableCell>{t('bookings.installments.earnStatus')}</TableCell>
              <TableCell>{t('bookings.installments.milesPrice')}</TableCell>
              <TableCell>{t('bookings.installments.actions')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {installments.map((installment) => (
              <TableRow key={installment?.id}>
                <TableCell>{formatDate(installment?.dueDate)}</TableCell>

                <TableCell>{formatCurrency(installment?.amount)}</TableCell>

                <TableCell>
                  <FormControl size="small" sx={{ minWidth: 200 }}>
                    <Select
                      value={installment?.status?.toLowerCase() || ''}
                      onChange={(e: SelectChangeEvent) =>
                        handleStatusChange(installment, e.target.value)
                      }
                      disabled={bookingDetails?.status === 'pending'}
                    >
                      {getStatusOptions(installment).map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </TableCell>

                <TableCell>
                  {installment?.paymentMethod
                    ? t(`bookings.installments.${installment?.paymentMethod.toLowerCase()}`)
                    : '-'}
                </TableCell>

                <TableCell>
                  {installment?.paymentMethod?.toLowerCase() !== 'cash'
                    ? installment?.lastPaidInstallment?.paymentBrand || '-'
                    : '-'}
                </TableCell>

                <TableCell>{formatDate(installment?.totalAmountPaidAt || '')}</TableCell>

                <TableCell>{installment?.walletPaidAmount || '-'}</TableCell>

                <TableCell>{installment?.loyaltyCollections?.[0]?.numberOfPoints || '-'}</TableCell>

                <TableCell>
                  {installment?.loyaltyCollections?.[0]?.status ? (
                    <Chip
                      label={t(
                        `bookings.installments.${installment?.loyaltyCollections[0].status}`
                      )}
                      size="small"
                      color="primary"
                    />
                  ) : (
                    '-'
                  )}
                </TableCell>

                <TableCell>{installment?.loyaltyCollections?.[0]?.pointPrice || '-'}</TableCell>

                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    {/* Edit Status Action */}
                    {shouldShowEditAction(installment) && (
                      <Tooltip title={t('bookings.installments.editStatus')}>
                        <IconButton size="small" color="primary">
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}

                    {/* Refund Action */}
                    {shouldShowRefundAction(installment) && (
                      <Tooltip title={t('bookings.installments.refundInstallment')}>
                        <IconButton
                          size="small"
                          color="secondary"
                          onClick={() => setRefundModal({ open: true, installment })}
                        >
                          <ReplayIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}

                    {/* Resend to Ally Action */}
                    {shouldShowResendToAllyAction(installment) && (
                      <Tooltip title={t('bookings.installments.resendToAlly')}>
                        <IconButton
                          size="small"
                          color="info"
                          onClick={() => handleResendToAlly(installment)}
                        >
                          <SendIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}

                    {/* Recall Payment Gateway Action */}
                    {shouldShowRecallAction(installment) && (
                      <Tooltip title={t('bookings.installments.recallPaymentGateway')}>
                        <IconButton
                          size="small"
                          color="info"
                          onClick={() => handleRecallPaymentGateway(installment)}
                        >
                          <RefreshIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Refund Modal */}
      <RefundModal
        open={refundModal.open}
        onClose={() => setRefundModal({ open: false, installment: null })}
        installment={refundModal.installment!}
        onRefund={(amount, isFullRefund, isBankTransfer) =>
          refundModal.installment &&
          handleRefund(refundModal.installment, amount, isFullRefund, isBankTransfer)
        }
      />
    </Box>
  );
}
