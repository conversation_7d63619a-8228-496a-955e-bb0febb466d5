(function() {
  // Apply scrollbar styles immediately
  function applyScrollbarStyles() {
    const isDarkMode = document.documentElement.getAttribute('data-theme-mode') === 'dark';
    
    const style = document.createElement('style');
    style.id = 'immediate-scrollbar-styles';
    
    if (isDarkMode) {
      style.textContent = `
        *::-webkit-scrollbar, ::-webkit-scrollbar { width: 10px !important; height: 8px !important; }
        *::-webkit-scrollbar-track, ::-webkit-scrollbar-track { background: rgba(30, 30, 30, 0.9) !important; border-radius: 4px !important; }
        *::-webkit-scrollbar-thumb, ::-webkit-scrollbar-thumb { background: #f1922380 !important; border-radius: 4px !important; border: none !important; }
        *::-webkit-scrollbar-thumb:hover, ::-webkit-scrollbar-thumb:hover { background: #f19223a0 !important; }
        
        /* Target specific elements */
        .MuiTableContainer-root::-webkit-scrollbar-thumb { background: #f1922380 !important; }
        .MuiTableContainer-root::-webkit-scrollbar-track { background: rgba(30, 30, 30, 0.9) !important; }
        [role=tablist] ~ div::-webkit-scrollbar-thumb { background: #f1922380 !important; }
        [role=tablist] ~ div::-webkit-scrollbar-track { background: rgba(30, 30, 30, 0.9) !important; }
      `;
    } else {
      style.textContent = `
        *::-webkit-scrollbar, ::-webkit-scrollbar { width: 10px !important; height: 8px !important; }
        *::-webkit-scrollbar-track, ::-webkit-scrollbar-track { background: rgba(0, 0, 0, 0.05) !important; border-radius: 4px !important; }
        *::-webkit-scrollbar-thumb, ::-webkit-scrollbar-thumb { background: rgba(241, 146, 35, 0.5) !important; border-radius: 4px !important; border: none !important; }
        *::-webkit-scrollbar-thumb:hover, ::-webkit-scrollbar-thumb:hover { background: rgba(241, 146, 35, 0.8) !important; }
      `;
    }
    
    // Remove existing style if it exists
    const existingStyle = document.getElementById('immediate-scrollbar-styles');
    if (existingStyle) {
      document.head.removeChild(existingStyle);
    }
    
    document.head.appendChild(style);
  }
  
  // Apply styles immediately
  applyScrollbarStyles();
  
  // Set up a MutationObserver to watch for theme changes
  const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
      if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme-mode') {
        applyScrollbarStyles();
      }
    });
  });
  
  // Start observing the document with the configured parameters
  observer.observe(document.documentElement, { attributes: true });
})();
