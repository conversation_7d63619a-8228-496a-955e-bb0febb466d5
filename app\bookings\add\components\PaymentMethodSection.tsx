import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  FormControlLabel,
  RadioGroup,
  Radio,
  FormHelperText,
} from '@mui/material';

interface PaymentMethodSectionProps {
  paymentMethod: string;
  onPaymentMethodChange: (method: string) => void;
  disabled?: boolean;
  clicked?: boolean;
}

export default function PaymentMethodSection({
  paymentMethod,
  onPaymentMethodChange,
  disabled = false,
  clicked = false,
}: PaymentMethodSectionProps) {
  const { t } = useTranslation();

  const isRequired = true;
  const hasError = clicked && isRequired && !paymentMethod;

  const paymentOptions = [
    { value: 'CASH', label: t('payment.cash') },
    { value: 'ONLINE', label: t('payment.online') },
  ];

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        {t('payment.method')}
      </Typography>

      <Box sx={{ mt: 2 }}>
        <FormControl error={hasError} disabled={disabled}>
          <RadioGroup
            value={paymentMethod}
            onChange={(e) => onPaymentMethodChange(e.target.value)}
            sx={{ flexDirection: 'row', gap: 3 }}
          >
            {paymentOptions.map((option) => (
              <FormControlLabel
                key={option.value}
                value={option.value}
                control={<Radio color="primary" />}
                label={
                  <Box>
                    <Typography variant="body1">{option.label}</Typography>
                    <Typography variant="body2" color="textSecondary">
                      {option.value === 'CASH'
                        ? t('payment.cash.description')
                        : t('payment.online.description')}
                    </Typography>
                  </Box>
                }
                sx={{
                  border: 1,
                  borderColor: paymentMethod === option.value ? 'primary.main' : 'divider',
                  borderRadius: 1,
                  p: 2,
                  m: 0,
                  minWidth: 200,
                  backgroundColor: paymentMethod === option.value ? 'primary.50' : 'transparent',
                }}
              />
            ))}
          </RadioGroup>
          {hasError && <FormHelperText>{t('This field is required')}</FormHelperText>}
        </FormControl>
      </Box>

      {paymentMethod && (
        <Box
          sx={{
            mt: 3,
            p: 2,
            bgcolor: 'background.paper',
            border: 1,
            borderColor: 'divider',
            borderRadius: 1,
          }}
        >
          <Typography variant="subtitle2" gutterBottom>
            {t('selected.payment.method')}:
          </Typography>
          <Typography variant="body1">
            {paymentMethod === 'CASH' ? t('payment.cash') : t('payment.online')}
          </Typography>
          <Typography variant="body2" color="textSecondary">
            {paymentMethod === 'CASH'
              ? t('payment.will.be.collected.on.delivery')
              : t('payment.will.be.processed.online')}
          </Typography>
        </Box>
      )}
    </Paper>
  );
}
