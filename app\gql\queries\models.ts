import { gql } from "@apollo/client";
export const GET_CAR_MODELS = gql`
  query CarModels(
    $acrissCode: String
    $limit: Int
    $make: [ID!]
    $orderBy: String
    $page: Int
    $sortBy: String
  )
{
    carModels(
      acrissCode: $acrissCode
      limit:  $limit
      make: $make
      orderBy: $orderBy
      page: $page
      sortBy: $sortBy
    ) {
      collection {
        acrissCode
        arName
        enName
        id
        name
      }
      metadata {
        currentPage
        limitValue
        totalCount
        totalPages
      }
    }
  }
  `;