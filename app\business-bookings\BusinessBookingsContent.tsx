'use client';
import { useState, useMemo } from 'react';
import { useQuery } from '@apollo/client';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { useRouter } from 'next/navigation';
import {
  Box,
  Typography,
  Paper,
  Button,
  Collapse,
  Badge,
  CircularProgress,
  Alert,
  useTheme,
  useMediaQuery,
  Skeleton,
} from '@mui/material';
import FilterListIcon from '@mui/icons-material/FilterList';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import AddIcon from '@mui/icons-material/Add';
import BusinessBookingsTable from './components/BusinessBookingsTable';
import BusinessBookingsMobileView from './components/BusinessBookingsMobileView';
import GenericFilters, { FilterSection } from '../shared/components/GenericFilters';
import {
  BUSINESS_RENTALS_QUERY,
  BUSINESS_RENTALS_COUNT_QUERY,
} from '../gql/queries/businessBookings';
import { BusinessBookingFilters } from '../models/businessBookings.model';

// Business booking status options
const BUSINESS_BOOKING_STATUS_OPTIONS = [
  'all',
  'confirmed',
  'car_received',
  'invoiced',
  'cancelled',
  'closed',
] as const;

export default function BusinessBookingsContent() {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useSelector((state: any) => state.auth);
  const isAlly = user?.ally_id;
  const router = useRouter();

  // State for filters and pagination
  const [filtersOpen, setFiltersOpen] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(50);
  const [filters, setFilters] = useState<BusinessBookingFilters>({});
  const [orderBy, setOrderBy] = useState('pick_up_datetime');
  const [sortBy, setSortBy] = useState('desc');

  // Fetch business bookings data
  const {
    data: bookingsData,
    loading: bookingsLoading,
    error: bookingsError,
    refetch,
  } = useQuery(BUSINESS_RENTALS_QUERY, {
    variables: {
      page,
      limit,
      orderBy,
      sortBy,
      ...filters,
    },
  });

  // Fetch booking counts for tabs
  const {
    data: countsData,
    loading: countsLoading,
    error: countsError,
  } = useQuery(BUSINESS_RENTALS_COUNT_QUERY);

  // Use real data from GraphQL queries
  const bookings = bookingsLoading ? [] : bookingsData?.businessRentals?.collection || [];
  const bookingsCount = bookingsLoading
    ? { currentPage: 1, totalCount: 0 }
    : bookingsData?.businessRentals?.metadata || { currentPage: 1, totalCount: 0 };

  // Process the API response to get all status counts
  const statusCounts = useMemo(() => {
    if (countsLoading) {
      return [
        ['All', '-'],
        ['Confirmed', '-'],
        ['Car Received', '-'],
        ['Invoiced', '-'],
        ['Cancelled', '-'],
        ['Closed', '-'],
      ];
    }

    // Check if we have the all array in the response
    if (
      countsData?.businessRentalsCount?.all &&
      Array.isArray(countsData.businessRentalsCount.all)
    ) {
      return countsData.businessRentalsCount.all;
    } else {
      // Fallback format
      return [
        ['All', countsData?.businessRentalsCount?.all || 0],
        ['Confirmed', 0],
        ['Car Received', 0],
        ['Invoiced', 0],
        ['Cancelled', 0],
        ['Closed', 0],
      ];
    }
  }, [countsLoading, countsData]);

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent | null, newValue: number) => {
    setActiveTab(newValue);
    setPage(1);

    // Apply filter based on selected tab
    if (newValue === 0) {
      // All bookings
      setFilters({});
    } else {
      // Filter by status
      const statusName = String(statusCounts[newValue][0]);
      const status = statusName.includes('_')
        ? statusName
        : statusName.toLowerCase().replace(/ /g, '_');
      setFilters({ status: [status] });
    }

    refetch();
  };

  // Handle filter toggle
  const toggleFilters = () => {
    setFiltersOpen(!filtersOpen);
  };

  // Handle filter apply
  const handleFilterApply = () => {
    setPage(1);
    refetch();
  };

  // Handle filter reset
  const handleFilterReset = () => {
    setFilters({});
    setPage(1);
    refetch();
  };

  // Handle sort change
  const handleSortChange = (field: string, direction: 'asc' | 'desc') => {
    setOrderBy(field);
    setSortBy(direction);
    refetch();
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    refetch();
  };

  // Handle rows per page change
  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1);
    refetch();
  };

  // Loading state
  const isLoading = bookingsLoading || countsLoading;

  // Error state
  const hasError = bookingsError || countsError;

  if (hasError) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          {t('Error loading business bookings data. Please try again later.')}
        </Alert>
      </Box>
    );
  }

  // Define filter sections for business bookings
  const filterSections: FilterSection[] = [
    {
      title: t('Search Fields'),
      fields: [
        {
          id: 'customerName',
          label: t('Customer Name'),
          type: 'text',
          placeholder: t('Enter customer name'),
          gridSize: { xs: 12, sm: 6, md: 4 },
        },
        {
          id: 'customerNid',
          label: t('nid.placeholder'),
          type: 'text',
          placeholder: t('Enter customer NID'),
          gridSize: { xs: 12, sm: 6, md: 4 },
        },
        {
          id: 'bookingNo',
          label: t('bookingNo.'),
          type: 'text',
          placeholder: t('Enter booking number'),
          gridSize: { xs: 12, sm: 6, md: 4 },
        },
        {
          id: 'customerMobile',
          label: t('Customer Mobile'),
          type: 'text',
          placeholder: t('Enter customer mobile'),
          gridSize: { xs: 12, sm: 6, md: 4 },
        },
        {
          id: 'allyCompanyName',
          label: t('Ally Company Name'),
          type: 'text',
          placeholder: t('Enter ally company name'),
          gridSize: { xs: 12, sm: 6, md: 4 },
        },
      ],
    },
    {
      title: t('Date Filters'),
      fields: [
        {
          id: 'pickUpDateFrom',
          label: t('Pickup Date From'),
          type: 'date',
          gridSize: { xs: 12, sm: 6, md: 3 },
        },
        {
          id: 'pickUpDate',
          label: t('Pickup Date To'),
          type: 'date',
          gridSize: { xs: 12, sm: 6, md: 3 },
        },
        {
          id: 'dropOffDate',
          label: t('Drop-off Date From'),
          type: 'date',
          gridSize: { xs: 12, sm: 6, md: 3 },
        },
        {
          id: 'dropOffDateTo',
          label: t('Drop-off Date To'),
          type: 'date',
          gridSize: { xs: 12, sm: 6, md: 3 },
        },
      ],
    },
  ];

  return (
    <Box
      sx={{
        p: { xs: 0.5, sm: 1, md: 2 },
        maxWidth: '100%',
        overflow: 'hidden',
      }}
    >
      {/* Page Header */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexDirection: { xs: 'column', sm: 'row' },
          gap: 2,
          mb: 3,
        }}
      >
        <Typography
          variant="h4"
          component="h1"
          sx={{
            width: '100%',
            fontWeight: 600,
            fontSize: { xs: '1.5rem', md: '2rem' },
            color: theme.palette.mode === 'dark' ? '#fff' : 'inherit',
            textShadow: theme.palette.mode === 'dark' ? '0 2px 4px rgba(0,0,0,0.5)' : 'none',
            position: 'relative',
            '&::after':
              theme.palette.mode === 'dark'
                ? {
                    content: '""',
                    position: 'absolute',
                    bottom: -5,
                    left: 0,
                    width: '40px',
                    height: '3px',
                    backgroundColor: theme.palette.primary.main,
                    borderRadius: '2px',
                  }
                : {},
          }}
        >
          {t('sidebar.businessBookings')}
        </Typography>

        <Box
          sx={{
            display: 'flex',
            gap: 2,
            alignSelf: { xs: 'stretch', sm: 'auto' },
            flexDirection: { xs: 'column', sm: 'row' },
            width: { xs: '100%', sm: 'auto' },
            justifyContent: 'flex-end',
            p: 0,
          }}
        >
          <Button
            startIcon={
              isRTL ? filtersOpen ? <ExpandLessIcon /> : <ExpandMoreIcon /> : <FilterListIcon />
            }
            endIcon={
              isRTL ? <FilterListIcon /> : filtersOpen ? <ExpandLessIcon /> : <ExpandMoreIcon />
            }
            onClick={toggleFilters}
            color="primary"
            variant={filtersOpen ? 'contained' : 'outlined'}
            aria-expanded={filtersOpen}
            aria-label={filtersOpen ? t('Hide Filters') : t('Show Filters')}
            sx={{
              borderRadius: 2,
              px: 2,
              py: 0.75,
              fontWeight: 500,
              color: `${
                filtersOpen ? theme.palette.primary.contrastText : theme.palette.primary.main
              } !important`,
              border: `2px solid ${theme.palette.primary.main}`,
              boxShadow: filtersOpen ? 3 : 1,
              borderColor: theme.palette.primary.dark,
              transition: 'all 0.2s ease-in-out',
              minWidth: 120,
              width: { xs: '100%', sm: 'auto' },
            }}
          >
            {t('Filters')}
          </Button>
        </Box>
      </Box>

      {/* Main Content */}
      <Paper
        elevation={3}
        sx={{
          borderRadius: 2,
          overflow: 'hidden',
          mb: 3,
          boxShadow:
            theme.palette.mode === 'dark'
              ? '0 4px 20px 0 rgba(0,0,0,0.5)'
              : '0 4px 20px 0 rgba(0,0,0,0.1)',
          transition: 'all 0.3s ease-in-out',
          border: theme.palette.mode === 'dark' ? '1px solid rgba(255,255,255,0.05)' : 'none',
          '&:hover': {
            boxShadow:
              theme.palette.mode === 'dark'
                ? '0 6px 25px 0 rgba(0,0,0,0.6)'
                : '0 6px 25px 0 rgba(0,0,0,0.15)',
            borderColor: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.1)' : 'transparent',
          },
          width: '100%',
          maxWidth: '100%',
          mx: 'auto',
        }}
      >
        {/* Status Tabs */}
        <Box
          sx={{ width: '100%', bgcolor: 'background.paper', maxWidth: '100%', overflow: 'hidden' }}
        >
          <Box
            sx={{
              borderBottom: 1,
              borderColor: 'divider',
              background: theme.palette.mode === 'dark' ? '#2a2a2a' : '#f5f5f5',
            }}
          >
            <Box
              sx={{
                overflowX: 'auto',
                display: 'flex',
                width: '100%',
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  minWidth: 'max-content',
                  gap: '4px',
                  px: 2,
                  py: 1,
                }}
              >
                {statusCounts.map((status: any, index: number) => (
                  <Box
                    key={status[0]}
                    onClick={() => handleTabChange(null as any, index)}
                    sx={{
                      px: { xs: 1.5, md: 2 },
                      py: 1.5,
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderBottom:
                        activeTab === index
                          ? `3px solid ${theme.palette.primary.main}`
                          : '3px solid transparent',
                      color:
                        activeTab === index
                          ? theme.palette.primary.main
                          : theme.palette.text.primary,
                      fontWeight: activeTab === index ? 600 : 500,
                      transition: 'all 0.2s ease',
                      '&:hover': {
                        backgroundColor: theme.palette.action.hover,
                        color: theme.palette.primary.main,
                      },
                      whiteSpace: 'nowrap',
                      flexShrink: 0,
                      position: 'relative',
                      zIndex: 1,
                      borderRadius: '4px 4px 0 0',
                      mx: 0.5,
                      width: 'max-content !important',
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'end', gap: 0.1 }}>
                      <Typography
                        component="span"
                        sx={{
                          fontWeight: 'inherit',
                          fontSize: { xs: '0.75rem', sm: '0.875rem', md: '0.9rem' },
                          textOverflow: 'ellipsis',
                          overflow: 'hidden',
                          whiteSpace: 'nowrap',
                          maxWidth: '100%',
                          marginInlineEnd: 0.5,
                        }}
                      >
                        {(() => {
                          const originalText = String(status[0]);
                          const translatedOriginal = t(originalText);

                          if (translatedOriginal !== originalText) {
                            return translatedOriginal;
                          }

                          const snakeCase = originalText.toLowerCase().replace(/ /g, '_');
                          const translatedSnakeCase = t(snakeCase);
                          if (translatedSnakeCase !== snakeCase) {
                            return translatedSnakeCase;
                          }

                          const titleCase = originalText
                            .split(' ')
                            .map(
                              (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
                            )
                            .join(' ');
                          const translatedTitleCase = t(titleCase);
                          if (translatedTitleCase !== titleCase) {
                            return translatedTitleCase;
                          }

                          return originalText;
                        })()}
                      </Typography>
                      {countsLoading ? (
                        <Skeleton variant="circular" width={20} height={20} />
                      ) : (
                        <Badge
                          badgeContent={status[1]}
                          max={99999999999999}
                          color="primary"
                          sx={{
                            marginInlineStart: 0.5,
                            '& .MuiBadge-badge': {
                              fontSize: '0.7rem',
                              height: 18,
                              minWidth: 18,
                              borderRadius: 9,
                              fontWeight: 600,
                              backgroundColor:
                                activeTab === index
                                  ? theme.palette.primary.main
                                  : theme.palette.mode === 'dark'
                                  ? 'rgba(255,255,255,0.15)'
                                  : 'rgba(0,0,0,0.1)',
                              color:
                                activeTab === index
                                  ? theme.palette.primary.contrastText
                                  : theme.palette.text.secondary,
                              transform: 'translate(0, 0)',
                              position: 'relative',
                            },
                          }}
                        />
                      )}
                    </Box>
                  </Box>
                ))}
              </Box>
            </Box>
          </Box>
        </Box>

        {/* Filters */}
        <Collapse in={filtersOpen} timeout="auto" unmountOnExit>
          <GenericFilters
            sections={filterSections}
            filters={filters}
            onFiltersChange={setFilters}
            onApply={handleFilterApply}
            onReset={handleFilterReset}
            loading={isLoading}
          />
        </Collapse>

        {/* Table/Mobile View */}
        <Box sx={{ p: { xs: 1, sm: 2 } }}>
          {isLoading && bookings.length === 0 ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : isMobile ? (
            <BusinessBookingsMobileView
              bookings={bookings}
              page={page}
              limit={limit}
              total={bookingsCount.totalCount}
              onPageChange={handlePageChange}
              onLimitChange={handleLimitChange}
            />
          ) : (
            <BusinessBookingsTable
              bookings={bookings}
              page={page}
              limit={limit}
              total={bookingsCount.totalCount}
              onPageChange={handlePageChange}
              onLimitChange={handleLimitChange}
              onSortChange={handleSortChange}
              orderBy={orderBy}
              sortBy={sortBy}
              loading={isLoading}
            />
          )}
        </Box>
      </Paper>
    </Box>
  );
}
