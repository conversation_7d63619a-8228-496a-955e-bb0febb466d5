import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Chip,
} from '@mui/material';

interface CouponAvailability {
  id: string;
  code: string;
  isValid: boolean;
  discountType: 'PERCENTAGE' | 'FIXED';
  discountValue: number;
  message: string;
}

interface CouponSectionProps {
  couponCode: string;
  couponAvailability: CouponAvailability | null;
  onCouponCodeChange: (code: string) => void;
  onCouponValidate: (code: string, carId: string) => Promise<CouponAvailability | null>;
  disabled?: boolean;
  selectedCarId?: string;
}

export default function CouponSection({
  couponCode,
  couponAvailability,
  onCouponCodeChange,
  onCouponValidate,
  disabled = false,
  selectedCarId,
}: CouponSectionProps) {
  const { t } = useTranslation();
  const [validating, setValidating] = useState(false);
  const [validationMessage, setValidationMessage] = useState('');

  const handleValidate = async () => {
    if (!couponCode.trim()) {
      setValidationMessage(t('please.enter.coupon.code'));
      return;
    }

    if (!selectedCarId) {
      setValidationMessage(t('please.select.car.first'));
      return;
    }

    setValidating(true);
    setValidationMessage('');

    try {
      const result = await onCouponValidate(couponCode, selectedCarId);
      if (result && result.isValid) {
        setValidationMessage(t('coupon.applied.successfully'));
      } else {
        setValidationMessage(result?.message || t('coupon.validation.failed'));
      }
    } catch (error) {
      setValidationMessage(t('coupon.validation.error'));
    } finally {
      setValidating(false);
    }
  };

  const getDiscountText = (availability: CouponAvailability) => {
    if (availability.discountType === 'PERCENTAGE') {
      return `${availability.discountValue}% ${t('discount')}`;
    } else {
      return `${availability.discountValue} ${t('currency.sr')} ${t('discount')}`;
    }
  };

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        {t('coupon.code')}
      </Typography>

      <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
        {t('enter.coupon.code.for.discount')}
      </Typography>

      <Box sx={{ display: 'flex', gap: 2, alignItems: 'flex-start' }}>
        <TextField
          label={t('coupon.code')}
          value={couponCode}
          onChange={(e) => onCouponCodeChange(e.target.value.toUpperCase())}
          placeholder={t('enter.coupon.code')}
          disabled={disabled || validating || couponAvailability?.isValid === true}
          sx={{ flex: 1 }}
        />

        <Button
          variant="contained"
          onClick={handleValidate}
          disabled={
            disabled ||
            validating ||
            couponAvailability?.isValid === true ||
            !couponCode.trim() ||
            !selectedCarId
          }
          startIcon={validating ? <CircularProgress size={20} /> : null}
        >
          {validating ? t('validating') : couponAvailability?.isValid ? t('applied') : t('apply')}
        </Button>
      </Box>

      {!selectedCarId && (
        <Alert severity="info" sx={{ mt: 2 }}>
          {t('select.car.to.apply.coupon')}
        </Alert>
      )}

      {validationMessage && (
        <Alert severity={couponAvailability?.isValid ? 'success' : 'error'} sx={{ mt: 2 }}>
          {validationMessage}
        </Alert>
      )}

      {couponAvailability?.isValid && (
        <Box
          sx={{
            mt: 3,
            p: 2,
            bgcolor: 'success.50',
            border: 1,
            borderColor: 'success.main',
            borderRadius: 1,
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
            <Typography variant="subtitle2" color="success.main">
              {t('coupon.applied')}
            </Typography>
            <Chip label={couponAvailability.code} size="small" color="success" variant="outlined" />
          </Box>
          <Typography variant="body1">
            <strong>{t('discount')}:</strong> {getDiscountText(couponAvailability)}
          </Typography>
          <Typography variant="body2" color="textSecondary">
            {couponAvailability.message}
          </Typography>
        </Box>
      )}

      {/* Coupon tips */}
      <Box
        sx={{
          mt: 2,
          p: 2,
          bgcolor: 'info.50',
          border: 1,
          borderColor: 'info.main',
          borderRadius: 1,
        }}
      >
        <Typography variant="subtitle2" gutterBottom color="info.main">
          {t('coupon.tips')}
        </Typography>
        <Typography variant="body2" color="textSecondary">
          • {t('coupon.tip.valid.with.car')}
        </Typography>
        <Typography variant="body2" color="textSecondary">
          • {t('coupon.tip.one.per.booking')}
        </Typography>
        <Typography variant="body2" color="textSecondary">
          • {t('coupon.tip.check.expiry')}
        </Typography>
      </Box>
    </Paper>
  );
}
