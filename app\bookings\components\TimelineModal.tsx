'use client';
import React, { useEffect, useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  List,
  ListItem,
  ListItemText,
  Divider,
  CircularProgress,
  Alert,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useQuery, gql } from '@apollo/client';
import { format } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';

// GraphQL query matching old dashboard exactly
const RENTALS_AUDITS = gql`
  query RentalsAudits($id: ID!) {
    rentalsAudits(id: $id) {
      createdAt
      newData
      oldData
      rentalId
      userId
      userName
      userRole
      referenceNo
      auditType
    }
  }
`;

interface RentalAudit {
  createdAt: string;
  newData?: string;
  oldData?: string;
  rentalId: string;
  userId: string;
  userName: string;
  userRole?: string;
  referenceNo?: string;
  auditType?: string;
}

interface TimelineModalProps {
  open: boolean;
  onClose: () => void;
  bookingId: string;
}

export default function TimelineModal({ open, onClose, bookingId }: TimelineModalProps) {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  const dateLocale = isRTL ? ar : enUS;

  const [oldData, setOldData] = useState<any[]>([]);
  const [newData, setNewData] = useState<any[]>([]);

  const {
    data: rentalAudits,
    loading,
    error,
    refetch,
  } = useQuery(RENTALS_AUDITS, {
    skip: !bookingId,
    variables: { id: bookingId },
  });

  const isJSONString = (str: string): boolean => {
    try {
      JSON.parse(str);
      return true;
    } catch (e) {
      return false;
    }
  };

  useEffect(() => {
    if (rentalAudits?.rentalsAudits?.length) {
      const newDataArray: any[] = [];
      const oldDataArray: any[] = [];

      rentalAudits.rentalsAudits.forEach((rental: RentalAudit) => {
        if (rental?.newData && isJSONString(rental.newData)) {
          newDataArray.push(JSON.parse(rental.newData));
        } else {
          newDataArray.push({});
        }

        if (rental?.oldData && isJSONString(rental.oldData)) {
          oldDataArray.push(JSON.parse(rental.oldData));
        } else {
          oldDataArray.push({});
        }
      });

      setOldData(oldDataArray);
      setNewData(newDataArray);
    }
  }, [rentalAudits]);

  useEffect(() => {
    if (bookingId && open) {
      refetch();
    }
  }, [bookingId, open, refetch]);

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy h:mm:ss a', { locale: dateLocale });
    } catch {
      return dateString;
    }
  };

  const formatTime = (timeString: string) => {
    try {
      // Handle HHmmss format from old dashboard
      if (timeString && timeString.length === 6) {
        const hours = timeString.slice(0, 2);
        const minutes = timeString.slice(2, 4);
        const seconds = timeString.slice(4, 6);
        return `${hours}:${minutes}:${seconds}`;
      }
      return timeString;
    } catch {
      return timeString;
    }
  };

  // Field name translations
  const getFieldLabel = (key: string): string => {
    const fieldTranslations: { [key: string]: string } = {
      // Booking fields
      notes: t('common.notes', { defaultValue: 'Notes' }),
      installment_status: t('bookings.installments.status', { defaultValue: 'Installment Status' }),
      payment_method: t('bookings.details.paymentMethod', { defaultValue: 'Payment Method' }),
      extension_status: t('bookings.extensionRequests.status', {
        defaultValue: 'Extension Status',
      }),
      rental_sub_status: t('bookings.details.subStatus', { defaultValue: 'Sub Status' }),
      drop_off_date: t('bookings.details.dropOffDate', { defaultValue: 'Drop-off Date' }),
      drop_off_time: t('bookings.details.dropOffTime', { defaultValue: 'Drop-off Time' }),
      pick_up_date: t('bookings.details.pickUpDate', { defaultValue: 'Pick-up Date' }),
      pick_up_time: t('bookings.details.pickUpTime', { defaultValue: 'Pick-up Time' }),
      status: t('common.status', { defaultValue: 'Status' }),
      sub_status: t('bookings.details.subStatus', { defaultValue: 'Sub Status' }),
      deliver_type: t('bookings.details.deliveryType', { defaultValue: 'Delivery Type' }),
      customer_booking_lat: t('bookings.details.customerLat', {
        defaultValue: 'Customer Latitude',
      }),
      customer_booking_lng: t('bookings.details.customerLng', {
        defaultValue: 'Customer Longitude',
      }),
      is_unlimited: t('bookings.details.unlimitedMileage', { defaultValue: 'Unlimited Mileage' }),
      with_wallet: t('bookings.details.withWallet', { defaultValue: 'With Wallet' }),
      with_installment: t('bookings.details.withInstallment', { defaultValue: 'With Installment' }),
      car_branch_lat: t('bookings.details.branchLat', { defaultValue: 'Branch Latitude' }),
      car_branch_lng: t('bookings.details.branchLng', { defaultValue: 'Branch Longitude' }),
      user: t('common.user', { defaultValue: 'User' }),
      en_branch_name: t('bookings.details.branchName', { defaultValue: 'Branch Name' }),
      ar_branch_name: t('bookings.details.branchNameAr', { defaultValue: 'Branch Name (Arabic)' }),
      ar_pick_up_city_name: t('bookings.details.pickUpCity', { defaultValue: 'Pick-up City' }),
      en_pick_up_city_name: t('bookings.details.pickUpCityEn', {
        defaultValue: 'Pick-up City (English)',
      }),
      ar_drop_off_city_name: t('bookings.details.dropOffCity', { defaultValue: 'Drop-off City' }),
      en_drop_off_city_name: t('bookings.details.dropOffCityEn', {
        defaultValue: 'Drop-off City (English)',
      }),
      ar_insurance_name: t('bookings.details.insurance', { defaultValue: 'Insurance' }),
      en_insurance_name: t('bookings.details.insuranceEn', { defaultValue: 'Insurance (English)' }),
      ar_drop_off_branch_name: t('bookings.details.dropOffBranch', {
        defaultValue: 'Drop-off Branch',
      }),
      en_drop_off_branch_name: t('bookings.details.dropOffBranchEn', {
        defaultValue: 'Drop-off Branch (English)',
      }),
      ar_car_name: t('bookings.details.vehicleName', { defaultValue: 'Vehicle Name' }),
      en_car_name: t('bookings.details.vehicleNameEn', { defaultValue: 'Vehicle Name (English)' }),
    };

    return fieldTranslations[key] || t(key, { defaultValue: key.replace(/_/g, ' ') });
  };

  // Value translations and formatting
  const formatFieldValue = (key: string, value: any): string => {
    if (!value || value === null || value === '-') return '-';

    // Status translations
    const statusTranslations: { [key: string]: string } = {
      upcoming: t('bookings.installments.upcoming', { defaultValue: 'Upcoming' }),
      paid: t('bookings.installments.paid', { defaultValue: 'Paid' }),
      due: t('bookings.installments.due', { defaultValue: 'Due' }),
      overdue: t('bookings.installments.overdue', { defaultValue: 'Overdue' }),
      pending: t('bookings.status.pending', { defaultValue: 'Pending' }),
      confirmed: t('bookings.status.confirmed', { defaultValue: 'Confirmed' }),
      car_received: t('bookings.status.carReceived', { defaultValue: 'Car Received' }),
      invoiced: t('bookings.status.invoiced', { defaultValue: 'Invoiced' }),
      closed: t('bookings.status.closed', { defaultValue: 'Closed' }),
      cancelled: t('bookings.status.cancelled', { defaultValue: 'Cancelled' }),
      ONLINE: t('bookings.payment.online', { defaultValue: 'Online' }),
      CASH: t('bookings.payment.cash', { defaultValue: 'Cash' }),
      WALLET: t('bookings.payment.wallet', { defaultValue: 'Wallet' }),
      no_delivery: t('bookings.delivery.noDelivery', { defaultValue: 'No Delivery' }),
      delivery: t('bookings.delivery.delivery', { defaultValue: 'Delivery' }),
      new_request: t('bookings.status.newRequest', { defaultValue: 'New Request' }),
    };

    // Handle specific field types
    if (['pick_up_time', 'drop_off_time', 'refunded_at'].includes(key)) {
      return formatTime(value);
    }

    if (['pick_up_date', 'drop_off_date'].includes(key)) {
      try {
        return format(new Date(value), 'dd/MM/yyyy', { locale: dateLocale });
      } catch {
        return value;
      }
    }

    if (['with_wallet', 'with_installment', 'is_unlimited'].includes(key)) {
      return value === true || value === 'true'
        ? t('common.yes', { defaultValue: 'Yes' })
        : value === false || value === 'false'
        ? t('common.no', { defaultValue: 'No' })
        : value;
    }

    // Translate status values
    if (typeof value === 'string' && statusTranslations[value]) {
      return statusTranslations[value];
    }

    // Try to translate the value if it's a string
    if (typeof value === 'string') {
      return t(value, { defaultValue: value });
    }

    return String(value);
  };

  // Fields to hide from timeline (technical fields)
  const hideFields = [
    'customer_booking_lat',
    'customer_booking_lng',
    'car_branch_lat',
    'car_branch_lng',
    'en_branch_name',
    'en_pick_up_city_name',
    'en_drop_off_city_name',
    'en_insurance_name',
    'en_drop_off_branch_name',
    'en_car_name',
  ];

  const renderDataValue = (key: string, value: any) => {
    if (!value || value === null) return '-';

    // Handle decline reason with comma separation
    if (key === 'decline_reason' && typeof value === 'string') {
      return (
        <List dense sx={{ pl: 2 }}>
          {value.split(',').map((reason, idx) => (
            <ListItem key={idx} sx={{ py: 0 }}>
              <ListItemText primary={reason.trim()} />
            </ListItem>
          ))}
        </List>
      );
    }

    // Handle notes array
    if (key === 'notes' && Array.isArray(value)) {
      return value.map((item: any, idx: number) => <div key={idx}>{item?.note || item}</div>);
    }

    return formatFieldValue(key, value);
  };

  const renderDataSection = (data: any, title: string) => (
    <Box sx={{ flex: 1, mx: 1 }}>
      <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
        {title}
      </Typography>
      <List dense>
        {data &&
          Object.entries(data)
            .filter(([key]) => !hideFields.includes(key)) // Filter out technical fields
            .map(([key, value]) => (
              <ListItem key={key} sx={{ px: 0, py: 0.5 }}>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {getFieldLabel(key)}:
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {renderDataValue(key, value)}
                      </Typography>
                    </Box>
                  }
                />
              </ListItem>
            ))}
      </List>
    </Box>
  );

  if (loading) {
    return (
      <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
        <DialogContent>
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        </DialogContent>
      </Dialog>
    );
  }

  if (error) {
    return (
      <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
        <DialogTitle>{t('bookings.timeline.title')}</DialogTitle>
        <DialogContent>
          <Alert severity="error">
            {t('error.loadingTimeline', { defaultValue: 'Error loading timeline data' })}
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>{t('common.close')}</Button>
        </DialogActions>
      </Dialog>
    );
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { height: '80vh' },
      }}
    >
      <DialogTitle>
        <Typography variant="h6">{t('bookings.timeline.title')}</Typography>
      </DialogTitle>

      <DialogContent>
        {rentalAudits?.rentalsAudits?.length ? (
          <Box sx={{ height: '500px', overflowY: 'auto' }}>
            {/* Booking ID Header */}
            <Box sx={{ mb: 3, p: 2, backgroundColor: 'background.paper', borderRadius: 1 }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                {t('bookings.details.bookingNumber')}: {rentalAudits.rentalsAudits[0]?.rentalId}
              </Typography>
            </Box>

            {/* Timeline Items */}
            {rentalAudits.rentalsAudits.map((audit: RentalAudit, index: number) => (
              <Box key={index} sx={{ mb: 3 }}>
                {/* Audit Header */}
                <Box
                  sx={{
                    mb: 2,
                    p: 2,
                    backgroundColor: 'primary.main',
                    color: 'white',
                    borderRadius: 1,
                  }}
                >
                  <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                    {audit.userName}
                  </Typography>
                  <Typography variant="body2">{formatDate(audit.createdAt)}</Typography>
                  {audit.referenceNo && (
                    <Typography variant="body2">
                      {t('bookings.timeline.extensionId', { defaultValue: 'Extension ID' })}:{' '}
                      {audit.referenceNo}
                    </Typography>
                  )}
                </Box>

                {/* Data Comparison */}
                <Box sx={{ display: 'flex', gap: 2 }}>
                  {renderDataSection(
                    oldData[index],
                    t('bookings.timeline.oldData', { defaultValue: 'Old Data' })
                  )}
                  <Divider orientation="vertical" flexItem />
                  {renderDataSection(
                    newData[index],
                    t('bookings.timeline.newData', { defaultValue: 'New Data' })
                  )}
                </Box>

                {index < rentalAudits.rentalsAudits.length - 1 && <Divider sx={{ my: 3 }} />}
              </Box>
            ))}
          </Box>
        ) : (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <Typography variant="body1">
              {t('bookings.timeline.noDataFound', { defaultValue: 'No data found' })}
            </Typography>
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} variant="contained">
          {t('common.close')}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
