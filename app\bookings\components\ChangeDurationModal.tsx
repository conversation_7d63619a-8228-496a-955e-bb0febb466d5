'use client';
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation } from '@apollo/client';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Alert,
  CircularProgress,
  Snackbar,
  Grid,
} from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import {
  EDIT_RENTAL_DURATION_MUTATION,
  REJECT_EXTENSION_REQUEST_MUTATION,
} from '../../gql/mutations/bookings';

interface ChangeDurationModalProps {
  open: boolean;
  onClose: () => void;
  bookingId: string;
  currentStartDate: string;
  currentEndDate: string;
  pricePerDay: number;
  totalPrice: number;
  onDurationChanged: () => void;
}

export default function ChangeDurationModal({
  open,
  onClose,
  bookingId,
  currentStartDate,
  currentEndDate,
  pricePerDay,
  totalPrice,
  onDurationChanged,
}: ChangeDurationModalProps) {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';

  const [pickUpDate, setPickUpDate] = useState<dayjs.Dayjs | null>(null);
  const [dropOffDate, setDropOffDate] = useState<dayjs.Dayjs | null>(null);
  const [error, setError] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [showSuccess, setShowSuccess] = useState<boolean>(false);

  const [editRentalDuration] = useMutation(EDIT_RENTAL_DURATION_MUTATION);
  const [rejectExtensionRequest] = useMutation(REJECT_EXTENSION_REQUEST_MUTATION);

  useEffect(() => {
    if (open && currentStartDate && currentEndDate) {
      // Parse the dates from the booking
      const startDate = dayjs(currentStartDate);
      const endDate = dayjs(currentEndDate);

      setPickUpDate(startDate);
      setDropOffDate(endDate);
      setError('');
    }
  }, [open, currentStartDate, currentEndDate]);

  const handleSubmit = async () => {
    if (!pickUpDate || !dropOffDate) {
      setError(t('bookings.duration.validation.datesRequired'));
      return;
    }

    if (dropOffDate.isBefore(pickUpDate) || dropOffDate.isSame(pickUpDate)) {
      setError(t('bookings.duration.validation.endDateAfterStart'));
      return;
    }

    setLoading(true);
    setError('');

    try {
      const result = await editRentalDuration({
        variables: {
          rentalId: bookingId,
          pickUpDate: pickUpDate.format('DD/MM/YYYY'),
          pickUpTime: pickUpDate.format('HH:mm:ss'),
          dropOffDate: dropOffDate.format('DD/MM/YYYY'),
          dropOffTime: dropOffDate.format('HH:mm:ss'),
        },
      });

      if (!result?.data?.editRentalDuration?.errors?.length) {
        setShowSuccess(true);
        setTimeout(() => {
          onDurationChanged();
          handleClose();
        }, 1500);
      } else {
        setError(
          result.data.editRentalDuration.errors[0] || t('bookings.duration.errors.failedToUpdate')
        );
      }
    } catch (err: any) {
      console.error('Error updating duration:', err);
      setError(err.message || t('bookings.duration.errors.generalError'));
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setPickUpDate(null);
    setDropOffDate(null);
    setError('');
    setLoading(false);
    setShowSuccess(false);
    onClose();
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle>{t('bookings.duration.changeDuration.title')}</DialogTitle>

        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <DateTimePicker
                  label={t('bookings.duration.pickUpDateTime')}
                  value={pickUpDate}
                  onChange={(newValue) => {
                    setPickUpDate(newValue);
                    setError('');
                  }}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      error: !!error && error.includes('pickup'),
                    },
                  }}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <DateTimePicker
                  label={t('bookings.duration.dropOffDateTime')}
                  value={dropOffDate}
                  onChange={(newValue) => {
                    setDropOffDate(newValue);
                    setError('');
                  }}
                  minDateTime={pickUpDate || undefined}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      error: !!error && error.includes('dropoff'),
                    },
                  }}
                />
              </Grid>
            </Grid>

            {error && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {error}
              </Alert>
            )}
          </Box>
        </DialogContent>

        <DialogActions>
          <Button
            onClick={handleClose}
            disabled={loading}
            sx={{
              backgroundColor: '#f44336',
              color: 'white',
              '&:hover': {
                backgroundColor: '#d32f2f',
              },
              '&:disabled': {
                backgroundColor: '#cccccc',
                color: '#666666',
              },
            }}
          >
            {t('common.cancel')}
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={loading || !pickUpDate || !dropOffDate}
            sx={{
              backgroundColor: '#00bcd4',
              color: 'white',
              '&:hover': {
                backgroundColor: '#0097a7',
              },
              '&:disabled': {
                backgroundColor: '#cccccc',
                color: '#666666',
              },
            }}
          >
            {loading ? <CircularProgress size={20} /> : t('bookings.duration.change')}
          </Button>
        </DialogActions>

        <Snackbar
          open={showSuccess}
          autoHideDuration={1500}
          onClose={() => setShowSuccess(false)}
          anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        >
          <Alert severity="success" onClose={() => setShowSuccess(false)}>
            {t('bookings.duration.durationChangedSuccessfully')}
          </Alert>
        </Snackbar>
      </Dialog>
    </LocalizationProvider>
  );
}
