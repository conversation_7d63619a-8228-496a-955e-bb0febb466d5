import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Paper,
  FormControlLabel,
  Radio,
  RadioGroup,
  FormControl,
  Grid,
  Card,
  CardContent,
  Chip,
} from '@mui/material';

interface CarPlan {
  id: string;
  noOfMonths: number;
  firstInstallment: number;
  finalInstallment: number;
  monthlyInstallment?: number;
  totalAmount?: number;
}

interface RentToOwnPlansSectionProps {
  carPlans: CarPlan[];
  selectedPlan: CarPlan | null;
  onPlanChange: (plan: CarPlan | null) => void;
  disabled?: boolean;
  clicked?: boolean;
  bookingType: string;
  hasSelectedCar: boolean;
}

export default function RentToOwnPlansSection({
  carPlans,
  selectedPlan,
  onPlanChange,
  disabled = false,
  clicked = false,
  bookingType,
  hasSelectedCar,
}: RentToOwnPlansSectionProps) {
  const { t } = useTranslation();

  // Only show for rent-to-own booking type
  if (bookingType !== 'rent-to-own') {
    return null;
  }

  if (!hasSelectedCar) {
    return (
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          {t('rent.to.own.plans')}
        </Typography>
        <Typography variant="body2" color="textSecondary">
          {t('select.car.first.to.see.plans')}
        </Typography>
      </Paper>
    );
  }

  if (!carPlans || carPlans.length === 0) {
    return (
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          {t('rent.to.own.plans')}
        </Typography>
        <Typography variant="body2" color="textSecondary">
          {t('no.rent.to.own.plans.available.for.this.car')}
        </Typography>
      </Paper>
    );
  }

  const isRequired = true;
  const hasError = clicked && isRequired && !selectedPlan;

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        {t('rent.to.own.plans')}
      </Typography>

      <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
        {t('choose.rent.to.own.plan.description')}
      </Typography>

      <FormControl error={hasError} disabled={disabled}>
        <RadioGroup
          value={selectedPlan?.id || ''}
          onChange={(e) => {
            const plan = carPlans.find((p) => p.id === e.target.value) || null;
            onPlanChange(plan);
          }}
        >
          <Grid container spacing={2}>
            {carPlans.map((plan) => (
              <Grid item xs={12} md={6} lg={4} key={plan.id}>
                <Card
                  sx={{
                    border: 2,
                    borderColor: selectedPlan?.id === plan.id ? 'primary.main' : 'divider',
                    backgroundColor: selectedPlan?.id === plan.id ? 'primary.50' : 'transparent',
                    cursor: 'pointer',
                    '&:hover': {
                      borderColor: 'primary.main',
                      backgroundColor: 'primary.25',
                    },
                  }}
                  onClick={() => onPlanChange(plan)}
                >
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                      <FormControlLabel
                        value={plan.id}
                        control={<Radio color="primary" />}
                        label=""
                        sx={{ m: 0 }}
                      />
                      <Typography variant="h6" color="primary">
                        {plan.noOfMonths} {t('months')}
                      </Typography>
                      {plan.noOfMonths === 36 && (
                        <Chip label={t('popular')} size="small" color="secondary" />
                      )}
                    </Box>

                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <Box>
                        <Typography variant="body2" color="textSecondary">
                          {t('first.installment')}
                        </Typography>
                        <Typography variant="body1" fontWeight="bold">
                          {plan.firstInstallment} {t('currency.sr')}
                        </Typography>
                      </Box>

                      {plan.monthlyInstallment && (
                        <Box>
                          <Typography variant="body2" color="textSecondary">
                            {t('monthly.installment')}
                          </Typography>
                          <Typography variant="body1" fontWeight="bold">
                            {plan.monthlyInstallment} {t('currency.sr')}
                          </Typography>
                        </Box>
                      )}

                      <Box>
                        <Typography variant="body2" color="textSecondary">
                          {t('final.installment')}
                        </Typography>
                        <Typography variant="body1" fontWeight="bold">
                          {plan.finalInstallment} {t('currency.sr')}
                        </Typography>
                      </Box>

                      {plan.totalAmount && (
                        <Box sx={{ pt: 1, borderTop: 1, borderColor: 'divider' }}>
                          <Typography variant="body2" color="textSecondary">
                            {t('total.amount')}
                          </Typography>
                          <Typography variant="body1" fontWeight="bold" color="primary">
                            {plan.totalAmount} {t('currency.sr')}
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </RadioGroup>
      </FormControl>

      {hasError && (
        <Typography variant="caption" color="error" sx={{ mt: 1, display: 'block' }}>
          {t('please.select.a.plan')}
        </Typography>
      )}

      {selectedPlan && (
        <Box
          sx={{
            mt: 3,
            p: 2,
            bgcolor: 'background.paper',
            border: 1,
            borderColor: 'divider',
            borderRadius: 1,
          }}
        >
          <Typography variant="subtitle2" gutterBottom>
            {t('selected.plan.summary')}:
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2">
                <strong>{t('plan.duration')}:</strong> {selectedPlan.noOfMonths} {t('months')}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2">
                <strong>{t('first.payment')}:</strong> {selectedPlan.firstInstallment}{' '}
                {t('currency.sr')}
              </Typography>
            </Grid>
            {selectedPlan.monthlyInstallment && (
              <Grid item xs={12} sm={6}>
                <Typography variant="body2">
                  <strong>{t('monthly.payment')}:</strong> {selectedPlan.monthlyInstallment}{' '}
                  {t('currency.sr')}
                </Typography>
              </Grid>
            )}
            <Grid item xs={12} sm={6}>
              <Typography variant="body2">
                <strong>{t('final.payment')}:</strong> {selectedPlan.finalInstallment}{' '}
                {t('currency.sr')}
              </Typography>
            </Grid>
          </Grid>
        </Box>
      )}

      {/* Plan benefits info */}
      <Box
        sx={{
          mt: 2,
          p: 2,
          bgcolor: 'info.50',
          border: 1,
          borderColor: 'info.main',
          borderRadius: 1,
        }}
      >
        <Typography variant="subtitle2" gutterBottom color="info.main">
          {t('rent.to.own.benefits')}
        </Typography>
        <Typography variant="body2" color="textSecondary">
          • {t('own.the.car.at.end.of.term')}
        </Typography>
        <Typography variant="body2" color="textSecondary">
          • {t('flexible.payment.options')}
        </Typography>
        <Typography variant="body2" color="textSecondary">
          • {t('maintenance.included')}
        </Typography>
        <Typography variant="body2" color="textSecondary">
          • {t('comprehensive.insurance.included')}
        </Typography>
      </Box>
    </Paper>
  );
}
