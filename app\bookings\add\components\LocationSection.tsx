'use client';
import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Grid,
  TextField,
  FormControlLabel,
  Checkbox,
  Autocomplete,
} from '@mui/material';

interface LocationSectionProps {
  areasData: any[];
  pickUpCity: any;
  dropOffCity: any;
  isPickSameReturn: boolean;
  isDelivery: boolean;
  gettingAreas: boolean;
  customerId: string | null;
  mode: 'add' | 'edit';
  clicked: boolean;
  locale: string;
  onPickUpCityChange: (city: any) => void;
  onDropOffCityChange: (city: any) => void;
  onIsPickSameReturnChange: (checked: boolean) => void;
  onChanged: () => void;
}

export default function LocationSection({
  areasData,
  pickUpCity,
  dropOffCity,
  isPickSameReturn,
  isDelivery,
  gettingAreas,
  customerId,
  mode,
  clicked,
  locale,
  onPickUpCityChange,
  onDropOffCityChange,
  onIsPickSameReturnChange,
  onChanged,
}: LocationSectionProps) {
  const { t } = useTranslation();

  const handlePickUpCityChange = (e: any, newValue: any) => {
    if (newValue) {
      onPickUpCityChange(newValue);
      if (isPickSameReturn) {
        onDropOffCityChange(newValue);
      }
      onChanged();
    }
  };

  const handleDropOffCityChange = (e: any, val: any) => {
    if (val) {
      onDropOffCityChange(val);
      onChanged();
    }
  };

  const handleIsPickSameReturnChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const checked = e.target.checked;
    onIsPickSameReturnChange(checked);
    if (checked) {
      onDropOffCityChange(pickUpCity);
    }
    onChanged();
  };

  return (
    <Box sx={{ mb: 3 }}>
      <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
        {t('rental.bookingLocation')}
      </Typography>

      <FormControlLabel
        control={<Checkbox checked={isPickSameReturn} onChange={handleIsPickSameReturnChange} />}
        label={t('same.as.pickup.location')}
        sx={{ mb: 2 }}
      />

      <Grid container spacing={2}>
        {/* Pickup Location */}
        <Grid item xs={12} md={6}>
          <Autocomplete
            options={areasData}
            getOptionLabel={(option) => option[`${locale}Name` as keyof any] as string}
            value={pickUpCity}
            onChange={handlePickUpCityChange}
            loading={gettingAreas}
            disabled={!customerId && mode === 'add'}
            renderInput={(params) => (
              <TextField {...params} label={t('rental.pickupLocation')} required fullWidth />
            )}
          />
        </Grid>

        {/* Dropoff Location */}
        {!isPickSameReturn && (
          <Grid item xs={12} md={6}>
            <Autocomplete
              options={areasData.filter((city) => city.id !== pickUpCity?.id)}
              getOptionLabel={(option) => option[`${locale}Name` as keyof any] as string}
              value={dropOffCity}
              onChange={handleDropOffCityChange}
              loading={gettingAreas}
              disabled={!customerId && mode === 'add'}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label={t('dropoff-location')}
                  required
                  fullWidth
                  error={clicked && !dropOffCity}
                  helperText={
                    !dropOffCity && !isPickSameReturn && clicked
                      ? t('thisfieldisrequired')
                      : undefined
                  }
                />
              )}
            />
          </Grid>
        )}
      </Grid>

      {/* Map for delivery location */}
      {isDelivery && (
        <Box sx={{ mt: 3, height: 300, border: '1px solid #ddd', borderRadius: 1 }}>
          <Box
            sx={{
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              bgcolor: '#f5f5f5',
            }}
          >
            <Typography color="textSecondary">{t('mapComponentPlaceholder')}</Typography>
          </Box>
        </Box>
      )}
    </Box>
  );
}
