import { Status_Enum } from "./enums/status.enum";
import { USHER } from "./usher.model";

export interface EVENT {
  name?: string;
  id?: string;
  start_date?: string;
  end_date?: string;
  duration?: number;
  location?: string;
  accepted_ushers?: string;
  requested_ushers?: string;
  applied_ushers?: string;
  status?: string;
  category?: CATEGORY;
  key?: string;
  description?: string;
  is_top_event?: boolean;
  event_days?: EVENT_DAYS[];
  image?: string;
  usher_types?: USHER[];
}
export interface META {
  current_page?: number;
  last_page?: number;
  limitValue?: number;
  totalCount?: number;
}

export interface USHER_LIST {
  status?: string;
  usher_number?: number;
  salary?: number;
}

export interface CATEGORY {
  created_at?: string;
  description?: string;
  id?: number;
  name?: string;
}

export interface EVENT_DAYS {
  start_time?: any;
  end_time?: any;
  date?: string;
  event_id?: number;
  id?: number;
  activities?: ACTIVITIES[];
}

export interface ACTIVITIES {
  end_time: string;
  start_time: string;
  title: string;
}

export interface EVENT_QUESTION {
  type: string;
  title: string;
  choices: any;
}

export interface EVENT_FORM {
  name?: string;
  location?: string;
  latitude?: string;
  longitude?: string;
  start_date?: string;
  end_date?: string;
  duration?: number;
  category_id?: number;
  description?: string;
  is_top_event?: boolean;
  usher_types?: {
    guide?: number;
    promoter?: number;
  };
  event_days?: any;
  questions?: [{}];
  image?: string;
}

export interface QUESTION {
  type?: string;
  title?: string;
  is_required?: number;
  choices?: any;
  answer?: string;
}
export interface CATEGORY {
  label?: string;
  value?: number;
}

export interface STEP_ONE {
  category_id?: number;
  description?: string;
  end_date?: string;
  is_top_event?: boolean;
  location?: string;
  latitude: number;
  longitude: number;
  name?: string;
  start_date?: string;
  status?: Status_Enum;
  team_leader_id?: number;
  image?: string;
}

export interface STEP_TWO {
  duration?: number;
  event_days: {
    date?: string;
    start_time?: string;
    end_time?: string;
  }[];
}

export interface STEP_THREE {
  type?: number;
  salary?: string;
  count?: number;
}

export interface USHER_TYPE {
  id?: number;
  name?: string;
  slug?: string;
}

export interface STEP_FOUR {
  type?: string;
  title?: string;
  is_required?: boolean;
  choices?: any;
}
