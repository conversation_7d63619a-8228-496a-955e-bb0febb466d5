'use client';
import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@apollo/client';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Typography,
  Box,
  CircularProgress,
  Paper,
  Divider,
} from '@mui/material';
import {
  Timeline,
  TimelineItem,
  TimelineOppositeContent,
  TimelineSeparator,
  TimelineDot,
  TimelineConnector,
  TimelineContent,
} from '@mui/lab';
import CloseIcon from '@mui/icons-material/Close';
import { format } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import { BOOKING_DETAILS_QUERY } from '../../gql/queries/bookings';

interface TimelineModalProps {
  isOpen: boolean;
  onClose: () => void;
  bookingId: number | null;
}

export default function TimelineModal({ isOpen, onClose, bookingId }: TimelineModalProps) {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  const dateLocale = isRTL ? ar : enUS;

  const { data, loading, error } = useQuery(BOOKING_DETAILS_QUERY, {
    skip: !bookingId,
    variables: {
      id: bookingId,
    },
  });

  const timeline = data?.rental?.timeline || [];

  return (
    <Dialog
      open={isOpen}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      aria-labelledby="timeline-dialog-title"
    >
      <DialogTitle id="timeline-dialog-title" sx={{ m: 0, p: 2 }}>
        <Typography variant="h6" component="div">
          {t('Timeline')}
        </Typography>
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <Divider />
      <DialogContent sx={{ p: 3 }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Typography color="error">{t('Error loading timeline data')}</Typography>
        ) : timeline.length === 0 ? (
          <Typography>{t('No timeline events found')}</Typography>
        ) : (
          <Timeline position={isRTL ? 'right' : 'left'}>
            {timeline.map((event: any, index: number) => (
              <TimelineItem key={event.id}>
                <TimelineOppositeContent sx={{ flex: 0.2 }}>
                  <Typography variant="body2" color="text.secondary">
                    {format(new Date(event.timestamp), 'PPp', { locale: dateLocale })}
                  </Typography>
                </TimelineOppositeContent>
                <TimelineSeparator>
                  <TimelineDot color="primary" />
                  {index < timeline.length - 1 && <TimelineConnector />}
                </TimelineSeparator>
                <TimelineContent sx={{ py: '12px', px: 2 }}>
                  <Paper elevation={1} sx={{ p: 2 }}>
                    <Typography variant="h6" component="div">
                      {t(event.action)}
                    </Typography>
                    {event.user && (
                      <Typography variant="body2" color="text.secondary">
                        {t('By')}: {event.user.name}
                      </Typography>
                    )}
                  </Paper>
                </TimelineContent>
              </TimelineItem>
            ))}
          </Timeline>
        )}
      </DialogContent>
    </Dialog>
  );
}
