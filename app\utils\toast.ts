import { toast, ToastOptions, TypeOptions } from 'react-toastify';
import { getLocalizedErrorMessage } from './network';

// Default toast configuration
const defaultToastOptions: ToastOptions = {
  position: 'bottom-center',
  autoClose: 5000,
  hideProgressBar: false,
  closeOnClick: true,
  pauseOnHover: true,
  draggable: true,
};

// Map GraphQL error codes to translation keys
const errorCodeToTranslationKey: Record<string, string> = {
  UNAUTHENTICATED: 'login.invalidCredentials',
  FORBIDDEN: 'error.forbidden',
  NOT_FOUND: 'error.notFound',
  BAD_USER_INPUT: 'error.validation',
  INTERNAL_SERVER_ERROR: 'error.serverError',
  DUPLICATE_ENTRY: 'error.duplicate',
  TIMEOUT: 'error.timeout',
  VALIDATION_ERROR: 'error.validation',
};

/**
 * Display a toast notification
 * @param message - Message to display
 * @param type - Type of toast (success, error, info, warning)
 * @param options - Additional toast options
 */
export const showToast = (
  message: string,
  type: TypeOptions = 'info',
  options: Partial<ToastOptions> = {}
) => {
  toast(message, {
    ...defaultToastOptions,
    ...options,
    type,
  });
};

/**
 * Display an error toast with proper translation
 * @param error - Error object or message
 * @param translationKey - Optional translation key
 */
export const showErrorToast = (error: Error | string | unknown, translationKey?: string) => {
  let message: string;

  if (typeof error === 'string') {
    message = error;
  } else if (error instanceof Error) {
    message = error.message;
  } else {
    message = 'An unknown error occurred';
  }

  // Try to use translation if a key is provided
  if (translationKey) {
    message = getLocalizedErrorMessage(translationKey, message);
  }

  showToast(message, 'error');
};

/**
 * Display a success toast with proper translation
 * @param message - Success message
 * @param translationKey - Optional translation key
 */
export const showSuccessToast = (message: string, translationKey?: string) => {
  if (translationKey) {
    message = getLocalizedErrorMessage(translationKey, message);
  }

  showToast(message, 'success');
};

/**
 * Display a warning toast with proper translation
 * @param message - Warning message
 * @param translationKey - Optional translation key
 */
export const showWarningToast = (message: string, translationKey?: string) => {
  if (translationKey) {
    message = getLocalizedErrorMessage(translationKey, message);
  }

  showToast(message, 'warning');
};

/**
 * Format and display a GraphQL error as a toast
 * @param error - GraphQL error object
 */
export const showGraphQLErrorToast = (error: any) => {
  // Try to extract a meaningful message from the GraphQL error
  let message = getLocalizedErrorMessage(
    'error.graphql',
    'An error occurred while processing your request'
  );
  let translationKey: string | undefined;

  // Handle various error formats
  if (error.graphQLErrors && error.graphQLErrors.length > 0) {
    // Use the first GraphQL error message and code
    const graphQLError = error.graphQLErrors[0];

    // Check for error code in extensions
    if (graphQLError.extensions?.code) {
      const code = graphQLError.extensions.code.toString().toUpperCase();
      translationKey = errorCodeToTranslationKey[code];
    }

    // If we have a specific error message, use it
    if (graphQLError.message) {
      message = graphQLError.message;
    }
  } else if (error.message) {
    // Use the error message if available
    message = error.message;
  }

  // Use translated message if we have a translation key
  if (translationKey) {
    message = getLocalizedErrorMessage(translationKey, message);
  }

  // Special case handling for common messages
  if (message.includes('not authenticated') || message.includes('unauthenticated')) {
    message = getLocalizedErrorMessage('login.invalidCredentials', 'You are not authenticated');
  } else if (message.includes('permission') || message.includes('forbidden')) {
    message = getLocalizedErrorMessage(
      'error.forbidden',
      'You do not have permission to perform this action'
    );
  } else if (message.includes('not found') || message.includes('404')) {
    message = getLocalizedErrorMessage('error.notFound', 'The requested resource was not found');
  } else if (message.includes('duplicate') || message.includes('already exists')) {
    message = getLocalizedErrorMessage('error.duplicate', 'A duplicate entry already exists');
  } else if (message.includes('validation') || message.includes('invalid')) {
    message = getLocalizedErrorMessage(
      'error.validation',
      'Validation error. Please check your input.'
    );
  } else if (message.includes('server error') || message.includes('500')) {
    message = getLocalizedErrorMessage(
      'error.serverError',
      'Server error. Please try again later.'
    );
  } else if (message.includes('timeout')) {
    message = getLocalizedErrorMessage('error.timeout', 'The request timed out. Please try again.');
  }

  showToast(message, 'error');
};
