import React from 'react';
import { useTranslation } from 'react-i18next';
import { Box, Typography, Paper, TextField } from '@mui/material';

interface NotesSectionProps {
  notes: string;
  onNotesChange: (notes: string) => void;
  disabled?: boolean;
  isEditMode?: boolean;
}

export default function NotesSection({
  notes,
  onNotesChange,
  disabled = false,
  isEditMode = false,
}: NotesSectionProps) {
  const { t } = useTranslation();

  // Only show notes section in edit mode (matching old implementation)
  if (!isEditMode) {
    return null;
  }

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        {t('notes')}
      </Typography>

      <TextField
        fullWidth
        multiline
        rows={4}
        value={notes}
        onChange={(e) => onNotesChange(e.target.value)}
        placeholder={t('enter.notes.here')}
        disabled={disabled}
        variant="outlined"
        helperText={t('optional.notes.about.booking')}
      />
    </Paper>
  );
}
