'use client';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Box, Typography, RadioGroup, FormControlLabel, Radio, Checkbox } from '@mui/material';

const BOOKING_TYPES = [
  { id: 'daily', name: 'daily', label: 'daily' },
  { id: 'monthly', name: 'monthly', label: 'monthly' },
  { id: 'rent-to-own', name: 'rent-to-own', label: 'rentToOwn' },
];

interface BookingTypeSectionProps {
  bookingType: string;
  withInstallment: boolean;
  bookingId?: string;
  pickUpDate: any;
  onBookingTypeChange: (type: string) => void;
  onInstallmentChange: (checked: boolean) => void;
  onChanged: () => void;
}

export default function BookingTypeSection({
  bookingType,
  withInstallment,
  bookingId,
  pickUpDate,
  onBookingTypeChange,
  onInstallmentChange,
  onChanged,
}: BookingTypeSectionProps) {
  const { t } = useTranslation();

  const handleBookingTypeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    onChanged();
    onBookingTypeChange(value);
  };

  const handleInstallmentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChanged();
    onInstallmentChange(e.target.checked);
  };

  return (
    <Box sx={{ mb: 3 }}>
      <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
        {t('rental.bookingType')}
      </Typography>

      <RadioGroup row value={bookingType} onChange={handleBookingTypeChange}>
        {BOOKING_TYPES.map((type) => (
          <FormControlLabel
            key={type.id}
            value={type.name}
            control={<Radio />}
            label={t(type.label)}
            disabled={
              (!!bookingId && bookingType === 'rent-to-own') ||
              (!!bookingId && type.name === 'rent-to-own')
            }
          />
        ))}
      </RadioGroup>

      {/* Installment Option for non-rent-to-own */}
      {!bookingId && bookingType !== 'rent-to-own' && (
        <FormControlLabel
          control={<Checkbox checked={withInstallment} onChange={handleInstallmentChange} />}
          label={t('Installments.booking')}
          sx={{ mt: 1 }}
        />
      )}
    </Box>
  );
}
