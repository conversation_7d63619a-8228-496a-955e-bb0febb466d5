'use client';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import StatisticsContent from './statisticsContent';
import { getAuthToken } from '../utils/cookies';

export default function Statistics() {
  const router = useRouter();

  useEffect(() => {
    // Check for token in cookies
    const token = getAuthToken();
    if (!token) {
      router.replace('/login');
    }
  }, [router]);

  return (
    <div>
      <StatisticsContent />
    </div>
  );
}
