'use client';
import React, { useState } from 'react';
import { Container, Typography, Box, Button } from '@mui/material';
import AddNoteModal from '../bookings/components/AddNoteModal';

export default function TestNotesPage() {
  const [modalOpen, setModalOpen] = useState(false);
  const testBookingId = '1'; // You can change this to test with different booking IDs

  const handleNoteAdded = () => {
    console.log('Note added successfully!');
    // In a real scenario, this would refetch the booking data
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Notes Functionality Test Page
      </Typography>

      <Box sx={{ mt: 3 }}>
        <Typography variant="h6" gutterBottom>
          Testing Booking ID: {testBookingId}
        </Typography>

        <Button variant="contained" onClick={() => setModalOpen(true)} sx={{ mt: 2 }}>
          Add Note
        </Button>

        <AddNoteModal
          open={modalOpen}
          onClose={() => setModalOpen(false)}
          bookingId={testBookingId}
          onNoteAdded={handleNoteAdded}
        />
      </Box>
    </Container>
  );
}
