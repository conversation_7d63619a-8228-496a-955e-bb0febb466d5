import { useSelector, useDispatch } from 'react-redux';
import { useRouter } from 'next/navigation';
import { RootState } from '../store/store';
import { logout } from '../store/slices/authSlice';
import { useApolloClient } from '@apollo/client';

export const useAuth = () => {
  const auth = useSelector((state: RootState) => state.auth);
  const dispatch = useDispatch();
  const router = useRouter();
  const client = useApolloClient();

  const handleLogout = async () => {
    // Clear Apollo cache
    await client.clearStore();

    // Clear local storage
    localStorage.removeItem('token');

    // Update Redux state
    dispatch(logout());

    // Redirect to login
    router.push('/login');
  };

  return {
    isAuthenticated: auth.isAuthenticated,
    user: auth.user,
    loading: auth.loading,
    error: auth.error,
    logout: handleLogout,
  };
};
