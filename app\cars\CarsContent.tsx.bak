'use client';
import { useState, useMemo } from 'react';
import { useQuery } from '@apollo/client';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import {
  Box,
  Typography,
  Paper,
  Button,
  Collapse,
  Badge,
  CircularProgress,
  Alert,
  useTheme,
  useMediaQuery,
  Chip,
  Skeleton,
} from '@mui/material';
import FilterListIcon from '@mui/icons-material/FilterList';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import AddIcon from '@mui/icons-material/Add';
// import { mockBookings } from './mockData'; // No longer needed
import BookingsFilters from './components/BookingsFilters';
import BookingsTable from './components/BookingsTable';
import BookingsMobileView from './components/BookingsMobileView';
import { BOOKINGS_QUERY, BOOKINGS_COUNT_QUERY } from '../gql/queries/bookings';

export default function BookingsContent() {
  const { t } = useTranslation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useSelector((state: any) => state.auth);
  const isAlly = user?.ally_id;

  // State for filters and pagination
  const [filtersOpen, setFiltersOpen] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  // We don't need isClient anymore since we're using a custom tabs implementation
  const [page, setPage] = useState(1);
  // Set default limit to 50 items per page
  const [limit, setLimit] = useState(50);
  const [filters, setFilters] = useState({});
  const [orderBy, setOrderBy] = useState('pick_up_datetime');
  const [sortBy, setSortBy] = useState('desc');

  // Fetch bookings data
  const {
    data: bookingsData,
    loading: bookingsLoading,
    error: bookingsError,
    refetch,
  } = useQuery(BOOKINGS_QUERY, {
    variables: {
      page,
      limit: 50, // Force limit to 50
      orderBy,
      sortBy,
      ...filters,
    },
  });

  // Fetch booking counts for tabs
  const {
    data: countsData,
    loading: countsLoading,
    error: countsError,
  } = useQuery(BOOKINGS_COUNT_QUERY);

  // Use real data from GraphQL queries
  const bookings = bookingsLoading ? [] : bookingsData?.dashboardRentals?.collection || [];
  const bookingsCount = bookingsLoading
    ? { currentPage: 1, totalCount: 0 }
    : bookingsData?.dashboardRentals?.metadata || { currentPage: 1, totalCount: 0 };

  // Process the API response to get all status counts
  const statusCounts = useMemo(() => {
    if (countsLoading) {
      return [
        ['All', '-'],
        ['Pending', '-'],
        ['Confirmed', '-'],
        ['Car Received', '-'],
        ['Invoiced', '-'],
        ['Closed', '-'],
        ['Cancelled', '-'],
      ];
    }

    // Check if we have the all array in the response
    if (countsData?.rentalsCount?.all && Array.isArray(countsData.rentalsCount.all)) {
      // Format from API response which is an array of [status, count] pairs
      return countsData.rentalsCount.all;
    } else {
      // Fallback to the old format if the new format is not available
      return [
        ['All', countsData?.rentalsCount?.all || 0],
        ['Pending', countsData?.rentalsCount?.pending || 0],
        ['Confirmed', countsData?.rentalsCount?.confirmed || 0],
        ['Car Received', countsData?.rentalsCount?.car_received || 0],
        ['Invoiced', countsData?.rentalsCount?.invoiced || 0],
        ['Closed', countsData?.rentalsCount?.closed || 0],
        ['Cancelled', countsData?.rentalsCount?.cancelled || 0],
      ];
    }
  }, [countsLoading, countsData]);

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent | null, newValue: number) => {
    setActiveTab(newValue);

    // Reset pagination
    setPage(1);

    // Apply filter based on selected tab
    if (newValue === 0) {
      // All bookings
      setFilters({});
    } else {
      // Filter by status
      // Convert status name to API format (lowercase with underscores)
      const statusName = String(statusCounts[newValue][0]);
      const status = statusName.toLowerCase().replace(/ /g, '_');
      setFilters({ status });
    }

    // Refetch data with new filters
    refetch();
  };

  // Handle filter toggle
  const toggleFilters = () => {
    setFiltersOpen(!filtersOpen);
  };

  // Handle filter apply
  const handleFilterApply = (newFilters: any) => {
    setFilters({ ...filters, ...newFilters });
    setPage(1);
    refetch();
  };

  // Handle filter reset
  const handleFilterReset = () => {
    setFilters({});
    setPage(1);
    refetch();
  };

  // Handle sort change
  const handleSortChange = (field: string, direction: 'asc' | 'desc') => {
    setOrderBy(field);
    setSortBy(direction);
    refetch();
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    refetch();
  };

  // Handle rows per page change
  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1);
    refetch();
  };

  // Loading state
  const isLoading = bookingsLoading || countsLoading;

  // Error state
  const hasError = bookingsError || countsError;

  if (hasError) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{t('Error loading bookings data. Please try again later.')}</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 1, sm: 2, md: 3 } }}>
      {/* Page Header */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexDirection: { xs: 'column', sm: 'row' },
          gap: 2,
          mb: 3,
        }}
      >
        <Typography
          variant="h4"
          component="h1"
          sx={{
            fontWeight: 600,
            fontSize: { xs: '1.5rem', md: '2rem' },
          }}
        >
          {t('Bookings')}
        </Typography>

        {!isAlly && (
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            sx={{
              borderRadius: 2,
              px: 3,
              py: 1,
              fontWeight: 500,
              boxShadow: 2,
              '&:hover': {
                boxShadow: 4,
              },
              alignSelf: { xs: 'stretch', sm: 'auto' },
            }}
            aria-label={t('Create New Booking')}
          >
            {t('Create New Booking')}
          </Button>
        )}
      </Box>

      {/* Main Content */}
      <Paper
        elevation={3}
        sx={{
          borderRadius: 2,
          overflow: 'hidden',
          mb: 3,
          boxShadow: theme.palette.mode === 'dark' ? '0 4px 20px 0 rgba(0,0,0,0.4)' : '0 4px 20px 0 rgba(0,0,0,0.1)',
          transition: 'box-shadow 0.3s ease-in-out',
          '&:hover': {
            boxShadow: theme.palette.mode === 'dark' ? '0 6px 25px 0 rgba(0,0,0,0.5)' : '0 6px 25px 0 rgba(0,0,0,0.15)',
          },
        }}
      >

          {/* Status Tabs with Material UI */}
          <Box sx={{ width: '100%', bgcolor: 'background.paper' }}>
            <Box
              sx={{
                borderBottom: 1,
                borderColor: 'divider',
                background: theme.palette.mode === 'dark' ? theme.palette.background.paper : '#f5f5f5',
              }}
            >
              <Box
                sx={{
                  overflowX: 'auto',
                  display: 'flex',
                  width: '100%',
                  '&::-webkit-scrollbar': {
                    height: '6px',
                  },
                  '&::-webkit-scrollbar-thumb': {
                    backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.2)',
                    borderRadius: '3px',
                  },
                  scrollbarWidth: 'thin',
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    minWidth: 'max-content',
                    gap: '4px',
                    px: 2,
                    py: 1,
                  }}
                >
                  {statusCounts.map((status: any, index: number) => (
                    <Box
                      key={status[0]}
                      onClick={() => handleTabChange(null as any, index)}
                      sx={{
                        px: { xs: 1.5, md: 2 },
                        py: 1.5,
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderBottom:
                          activeTab === index
                            ? `3px solid ${theme.palette.primary.main}`
                            : '3px solid transparent',
                        color:
                          activeTab === index ? theme.palette.primary.main : theme.palette.text.primary,
                        fontWeight: activeTab === index ? 600 : 500,
                        transition: 'all 0.2s ease',
                        '&:hover': {
                          backgroundColor: theme.palette.action.hover,
                          color: theme.palette.primary.main,
                        },
                        whiteSpace: 'nowrap',
                        flexShrink: 0,
                        position: 'relative',
                        zIndex: 1,
                        borderRadius: '4px 4px 0 0',
                        mx: 0.5,
                        minWidth: { xs: 'auto' }, // Allow tabs to size based on content
                        maxWidth: { xs: '120px', sm: 'none' } // Remove max width constraint
                      }>

                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography
                          component="span"
                          sx={{
                            fontWeight: 'inherit',
                            fontSize: { xs: '0.75rem', sm: '0.875rem', md: '0.9rem' },
                            textOverflow: 'ellipsis',
                            overflow: 'hidden',
                            whiteSpace: 'nowrap',
                            maxWidth: '100%'
                          }}
                        >
                          {t(String(status[0]))}
                        </Typography>
                        {countsLoading ? (
                          <Skeleton variant="circular" width={20} height={20} />
                        ) : (
                          <Badge
                            badgeContent={status[1]}
                            max={999}
                            color="primary"
                            sx={{
                              '& .MuiBadge-badge': {
                                fontSize: '0.7rem',
                                height: 18,
                                minWidth: 18,
                                borderRadius: 9,
                                fontWeight: 600,
                                backgroundColor: activeTab === index ?
                                  theme.palette.primary.main :
                                  theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.15)' : 'rgba(0,0,0,0.1)',
                                color: activeTab === index ?
                                  theme.palette.primary.contrastText :
                                  theme.palette.text.secondary,
                              },
                            }}
                          />
                        )}
                      </Box>
                    </Box>
                  ))}
                </Box>
              </Box>
            </Box>
          </Box>
          {/* Filters Toggle Button */}
          <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              {Object.keys(filters).length > 0 && (
                <>
                  <Typography
                    variant="body2"
                    sx={{ fontWeight: 500, display: 'flex', alignItems: 'center' }}
                  >
                    {t('Active Filters')}:
                  </Typography>
                  {Object.entries(filters).map(
                    ([key, value]) =>
                      value && (
                        <Chip
                          key={key}
                          label={`${t(
                            key.charAt(0).toUpperCase() + key.slice(1).replace('_', ' ')
                          )}: ${value}`}
                          onDelete={() => {
                            const newFilters = { ...filters } as Record<string, any>;
                            delete newFilters[key];
                            setFilters(newFilters as any);
                            refetch();
                          }}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                      )
                  )}
                  <Button size="small" onClick={handleFilterReset} sx={{ ml: 1 }}>
                    {t('Clear All')}
                  </Button>
                </>
              )}
            </Box>
            <Button
              startIcon={<FilterListIcon />}
              endIcon={filtersOpen ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              onClick={toggleFilters}
              color="primary"
              aria-expanded={filtersOpen}
              aria-label={filtersOpen ? t('Hide Filters') : t('Show Filters')}
            >
              {t('Filters')}
            </Button>
          </Box>

          <Collapse in={filtersOpen} timeout="auto">
            <Box sx={{ mt: 2 }}>
              <BookingsFilters
                onApply={handleFilterApply}
                onReset={handleFilterReset}
                initialFilters={filters}
              />
            </Box>
          </Collapse>
        </Box>

        {/* Bookings List */}
        <Box sx={{ p: { xs: 1, sm: 2 } }}>
          {isLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              {isMobile ? (
                <BookingsMobileView
                  bookings={bookings}
                  page={page}
                  limit={limit}
                  total={bookingsCount.totalCount}
                  onPageChange={handlePageChange}
                  onLimitChange={handleLimitChange}
                />
              ) : (
                <BookingsTable
                  bookings={bookings}
                  page={page}
                  limit={limit}
                  total={bookingsCount.totalCount}
                  onPageChange={handlePageChange}
                  onLimitChange={handleLimitChange}
                  onSortChange={handleSortChange}
                  orderBy={orderBy}
                  sortBy={sortBy}
                  refetch={refetch}
                />
              )}
            </>
          )}
        </Box>
      </Paper>
    </Box>
    );
  }
