import { gql } from '@apollo/client';

// Query to fetch bookings with pagination and filtering
export const BOOKINGS_QUERY = gql`
  query GetBookings(
    $page: Int
    $limit: Int
    $from: String
    $to: String
    $orderBy: String
    $sortBy: String
    $customerName: String
    $bookingNo: String
    $plateNo: String
    $customerMobile: String
    $makeName: [String!]
    $cityName: [String!]
    $allyName: String
    $allyCompanyId: ID
    $paymentMethod: String
    $paymentBrand: [PaymentBrand!]
    $status: [String!]
    $subStatus: [String!]
    $pickUpDate: String
    $pickUpDateFrom: String
    $dropOffDate: String
    $dropOffDateTo: String
  ) {
    dashboardRentals(
      page: $page
      limit: $limit
      from: $from
      to: $to
      orderBy: $orderBy
      sortBy: $sortBy
      customerName: $customerName
      bookingNo: $bookingNo
      plateNo: $plateNo
      customerMobile: $customerMobile
      makeName: $makeName
      cityName: $cityName
      allyName: $allyName
      allyCompanyId: $allyCompanyId
      paymentMethod: $paymentMethod
      paymentBrand: $paymentBrand
      status: $status
      subStatus: $subStatus
      pickUpDate: $pickUpDate
      pickUpDateFrom: $pickUpDateFrom
      dropOffDate: $dropOffDate
      dropOffDateTo: $dropOffDateTo
    ) {
      collection {
        id
        bookingNo
        customerName
        customerMobile
        userId
        allyCompanyId
        arAllyName
        enAllyName
        branchId
        branchName
        carId
        arMakeName
        enMakeName
        arModelName
        enModelName
        arVersionName
        enVersionName
        year
        numberOfDays
        pricePerDay
        totalBookingPrice
        totalInsurancePrice
        paidAmount
        pickUpDate
        pickUpTime
        dropOffDate
        dropOffTime
        arPickUpCityName
        enPickUpCityName
        arDropOffCityName
        enDropOffCityName
        status
        subStatus
        paymentMethod
        paymentBrand
        createdAt
        isIntegratedRental
        lastRentalDateExtensionRequest {
          pickUpDate
          dropOffDate
          numberOfDays
          totalBookingPrice
          totalInsurancePrice
        }
        lastConfirmedExtensionRequest {
          pickUpDate
          dropOffDate
          numberOfDays
          totalBookingPrice
          totalInsurancePrice
        }
      }
      metadata {
        currentPage
        totalCount
      }
    }
  }
`;

// Query to fetch booking counts for tabs
export const BOOKINGS_COUNT_QUERY = gql`
  query GetBookingsCounts {
    rentalsCount {
      all
    }
  }
`;

// Query to fetch detailed rental information by ID
export const BOOKING_DETAILS_QUERY = gql`
  query GetRentalDetailsQuery($id: ID!) {
    rentalDetails(id: $id) {
      refundable
      assignedTo
      isPaid
      isRentToOwn
      addsPrice
      loyaltyMembership
      loyaltyPointRate
      loyaltyPointPrice
      loyaltyAppliedValueType
      loyaltyAppliedValue
      loyaltyMaxAppliedValue
      loyaltyCollections {
        id
        numberOfPoints
        pointPrice
        status
      }
      loyaltyPointPrice
      loyaltyPointRate
      loyaltyType
      totalAddsPrice
      allyCompanyId
      allyRate
      allyRentalRate
      arAllyName
      arDropOffCityName
      arMakeName
      arModelName
      arPickUpCityName
      arVersionName
      bookingNo
      branchId
      dropOffBranchId
      handoverLat
      couponId
      handoverLng
      handoverPrice
      cancelledReason
      closingReason
      carId
      paymentBrand
      invoicedAt
      notes
      carImage
      customerMobile
      customerName
      customerProfileImage
      customerRate
      customerRentalRate
      dailyPrice
      couponId
      couponCode
      couponDiscount
      deliverAddress
      deliverType
      deliverLat
      deliverLng
      insuranceId
      deliveryPrice
      discountPercentage
      rentalIntegrationResponse
      hasPendingPaymentTransaction
      isIntegratedRental
      rentalIntegrationErrorMessage
      discountType
      discountValue
      dropOffCityId
      dropOffCityName
      dropOffDate
      dropOffTime
      enAllyName
      enDropOffCityName
      enMakeName
      rentalIntegrationStatus
      enModelName
      enPickUpCityName
      enVersionName
      id
      paidAmount
      insuranceIncluded
      invoicePic
      makeName
      modelName
      newGrandTotal
      numberOfDays
      paymentMethod
      pickUpCityId
      pickUpCityName
      pickUpDate
      pickUpTime
      priceBeforeDiscount
      priceBeforeInsurance
      priceBeforeTax
      pricePerDay
      refundedAmount
      refundedAt
      refundedBy
      status
      statusLocalized
      subStatus
      subStatusLocalized
      suggestedPrice
      taxValue
      totalOfTheRentToOwnPrice
      totalRtoInstallmentsAmount
      rtoPriceBeforeTax
      rtoTaxValue
      totalBookingPrice
      totalAmountDue
      totalInsurancePrice
      userId
      valueAddedTaxPercentage
      versionName
      year
      isUnlimited
      isUnlimitedFree
      unlimitedFeePerDay
      withInstallment
      installmentsFullyPaid
      paidInstallmentsCount
      totalUnlimitedFee
      remainingDueInstallmentsAmount
      completedInstallmentsAmount
      totalInsurancePrice
      insuranceName
      lastConfirmedExtensionRequest {
        dropOffDate
        dropOffTime
        numberOfDays
        totalBookingPrice
        totalInsurancePrice
      }
      rentalExtraServices {
        arDescription
        arSubtitle
        arTitle
        description
        enDescription
        enSubtitle
        enTitle
        extraServiceId
        extraServiceType
        iconUrl
        id
        isRequired
        payType
        rentalId
        serviceValue
        subtitle
        title
        totalServiceValue
      }
      integratedRentalContracts {
        contractNo
        createdAt
        operationDateTime
        operationType
        refundAmount
        rentalId
      }
      totalWalletPaidAmount
      hasPendingExtensionRequests
      ownCarDetails {
        rentalOwnCarPlan {
          finalInstallment
          firstInstallment
          firstPayment
          id
          monthlyInstallment
          noOfMonths
        }
      }
      pendingExtensionRequest {
        createdAt
        dropOffDate
        dropOffTime
        extensionDays
        id
        isPaid
        paymentMethod
        requestNo
        status
        statusLocalized
        totalRemainingPrice
      }
      rentalPayments {
        amount
        expireDate
        id
        paidTime
        paymentBrand
        paymentType
        refundedAmount
        status
      }
      hasPendingExtensionRequests
      lastRentalDateExtensionRequest {
        id
        dropOffDate
        dropOffTime
        isPaid
        status
        requestNo
        extensionDays
        totalRemainingPrice
        statusLocalized
        paymentMethod
      }
      installments {
        loyaltyCollections {
          id
          numberOfPoints
          pointPrice
          status
        }
        hasPendingPaymentTransaction
        amount
        id
        installmentNumber
        paymentMethod
        status
        statusLocalized
        dueDate
        totalAmountPaidAt
        walletPaidAmount
        paymentStatus
        lastPaidInstallment {
          paymentBrand
        }
        canSendToAlly
      }
      mergedInstallments {
        loyaltyCollections {
          id
          numberOfPoints
          pointPrice
          status
        }
        amount
        id
        installmentNumber
        paymentMethod
        status
        statusLocalized
        dueDate
        totalAmountPaidAt
        walletPaidAmount
        paymentStatus
        lastPaidInstallment {
          paymentBrand
        }
        canSendToAlly
      }
      walletTransactions {
        amount
      }
      rentalDateExtensionRequests {
        createdAt
        discount
        dropOffDate
        originalPricePerDay
        dropOffTime
        totalWalletPaidAmount
        canSendExtensionToAlly
        paymentBrand
        extensionDays
        pricePerDay
        packagePricePerDay
        actualPaymentMethod
        id
        isPaid
        paymentMethod
        isPaid
        requestNo
        status
        statusLocalized
        totalRemainingPrice
        refundable
        refundedAt
        withWallet
        walletTransactions {
          amount
          id
        }
        paymentStatus
      }
      allyRentalRejections {
        arAllyName
        arBranchName
        enBranchName
        enAllyName
        rejectedReason
        branchId
        allyCompanyId
        rejectedReasons {
          body
        }
        rejectedByName
        createdAt
      }
      rentalRejectedBaskets {
        arAllyName
        enAllyName
        rejectedReasons {
          arBody
          enBody
        }
      }
    }
  }
`;

// Query to fetch branch details including coordinates
export const BRANCH_DETAILS_QUERY = gql`
  query GetBranchDetails($id: ID!) {
    branch(id: $id) {
      id
      name
      arName
      enName
      address
      lat
      lng
      officeNumber
      allyCompany {
        phoneNumber
      }
    }
  }
`;

// Query to get customer care users for assignment
export const GET_CUSTOMER_CARE_USERS_QUERY = gql`
  query GetCustomerCareUsers {
    users(limit: 500, page: 1, type: "customer_care", isActive: true) {
      collection {
        id
        name
        email
        profileImage
        isActive
        roles {
          id
          enName
          arName
        }
      }
    }
  }
`;

// Query to get assignment history for a booking
export const GET_ASSIGNMENT_HISTORY_QUERY = gql`
  query GetAssignmentHistory($bookingId: ID!) {
    assignmentHistory(bookingId: $bookingId) {
      id
      assignedTo {
        id
        name
        email
        avatar
      }
      assignedBy {
        id
        name
      }
      assignedAt
      unassignedAt
      unassignedBy {
        id
        name
      }
      notes
      reason
      isActive
    }
  }
`;

// Query to get cancelled reasons for closing rentals
export const CANCELLED_REASONS_QUERY = gql`
  query CancelledReasons($rentalType: String, $status: String!, $userType: String!) {
    cancelledReasons(rentalType: $rentalType, status: $status, userType: $userType) {
      arBody
      enBody
      id
    }
  }
`;
