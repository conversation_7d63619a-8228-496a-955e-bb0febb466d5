import React from 'react';
import { Autocomplete, TextField, Chip, CircularProgress } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@apollo/client';
import { AREAS } from '../../gql/queries/areas';

interface Ally {
  id: number | string;
  name: string;
  arName: string;
}

interface CitiesDropdownProps {
  filters: {
    allyCompanyId?: string | null;
    [key: string]: any;
  };
  handleAutocompleteChange: (field: string, value: string | null) => void;
}

const CitiesDropdown: React.FC<CitiesDropdownProps> = ({ 
  filters, 
  handleAutocompleteChange
}) => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  
  const { loading, error, data } = useQuery(AREAS, {
    fetchPolicy: 'cache-and-network',
   
  });

  const areas = data?.areas || [];
  return (
    <Autocomplete 
      id="cities" 
      options={areas} 
      loading={loading}
      getOptionLabel={(option) => (isRTL ? option.arName : option.name)} 
      value={ 
        filters.allyCompanyId 
          ? areas.find((ally:any) => ally.id.toString() === filters.allyCompanyId) || null 
          : null 
      } 
      onChange={(_, newValue) => 
        handleAutocompleteChange('allyCompanyId', newValue?.id?.toString() || null) 
      } 
      renderInput={(params) => ( 
        <TextField 
          {...params} 
          label={t('cities')} 
          variant="outlined" 
          size="small" 
          InputLabelProps={{ 
            sx: { padding: '0 20px' }, 
          }}
          InputProps={{
            ...params.InputProps,
            endAdornment: (
              <>
                {loading ? <CircularProgress color="inherit" size={20} /> : null}
                {params.InputProps.endAdornment}
              </>
            ),
          }}
          error={!!error}
          helperText={error ? t('Error loading areas') : ''}
        /> 
      )} 
      renderTags={(value, getTagProps) => 
        value.map((option, index) => ( 
          <Chip 
            label={isRTL ? option.arName : option.name} 
            {...getTagProps({ index })} 
            size="small" 
          /> 
        )) 
      }
    />
  );
};

export default CitiesDropdown;