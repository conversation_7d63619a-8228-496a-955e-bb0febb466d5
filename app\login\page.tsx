'use client';
import { useState, useEffect, useRef } from 'react';
import { Login_Mutation } from '../gql/mutations/authentication';
import { useMutation } from '@apollo/client';
import { useRouter } from 'next/navigation';
import {
  Box,
  TextField,
  Button,
  Typography,
  IconButton,
  InputAdornment,
  Grid,
  Alert,
} from '@mui/material';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import styles from './login.module.scss';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { loginStart, loginSuccess, loginError } from '../store/slices/authSlice';
import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { setCookie, getAuthToken } from '../utils/cookies';

// We've moved these to the Apollo client
// Rate limiting and timeout settings are now in utils/network.ts
const Login = () => {
  const { t } = useTranslation('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const emailRef = useRef();
  const passwordRef = useRef();

  // Form validation schema
  const loginSchema = z.object({
    email: z.string().email(t('login.invalidEmail')),
    password: z.string().min(8, t('login.passwordTooShort')),
  });

  type LoginFormData = z.infer<typeof loginSchema>;

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const dispatch = useAppDispatch();
  // const { loading: reduxLoading, error: reduxError } = useAppSelector((state) => state.auth);

  const [login] = useMutation(Login_Mutation);

  // Check if user is already logged in
  useEffect(() => {
    const token = getAuthToken();
    if (token) {
      router.replace('/statistics');
    }
  }, [router]);

  // Cleanup function
  useEffect(() => {
    return () => {
      setLoading(false);
      setError('');
    };
  }, []);

  useEffect(() => {
    if (passwordRef.current) (passwordRef.current as HTMLInputElement).focus();
    if (emailRef.current) (emailRef.current as HTMLInputElement).focus();
  }, [emailRef, passwordRef]);

  const onSubmit = async (data: LoginFormData) => {
    // Clear previous errors
    setError('');
    dispatch(loginStart());
    setLoading(true);

    login({
      variables: {
        email: data.email,
        password: data.password,
      },
    })
      .then((response) => {
        if (!response.data?.loginDashboard) {
          throw new Error(t('login.invalidResponse'));
        }

        const token = response.data.loginDashboard?.token;
        if (!token) {
          throw new Error(t('login.invalidToken'));
        }

        // Store token securely
        try {
          // Use secure cookie only - removing localStorage for security
          setCookie('auth_token', token);
          // Also set standard 'token' cookie for middleware compatibility
          setCookie('token', token);

          // First update the auth state
          dispatch(loginSuccess({ token }));

          // Use router.push instead of replace for better navigation control
          router.push('/statistics');
        } catch (storageError) {
          console.error('Storage error:', storageError);
          throw new Error(t('login.storageError'));
        }
      })
      .catch((error) => {
        // Extract meaningful error message from the error
        let errorMessage: string;

        if (error.networkError) {
          errorMessage = error.networkError.message || t('login.networkError');
        } else if (error.graphQLErrors && error.graphQLErrors.length > 0) {
          const gqlError = error.graphQLErrors[0];
          if (gqlError.extensions?.code === 'UNAUTHENTICATED') {
            errorMessage = t('login.invalidCredentials');
          } else {
            errorMessage = gqlError.message || t('login.unknownError');
          }
        } else {
          errorMessage = error.message || t('login.unknownError');
        }

        dispatch(loginError(errorMessage));
        setError(errorMessage);

        // Log for monitoring but don't expose sensitive info
        console.error(
          'Login error:',
          error.name,
          error.networkError ? 'Network error' : 'GraphQL error'
        );
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleClickShowPassword = () => setShowPassword((show) => !show);

  const handleMouseDownPassword = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
  };

  return (
    <Grid
      container
      spacing={0}
      sx={{
        minHeight: '100vh',
        bgcolor: 'background.default',
      }}
    >
      <Grid
        item
        xs={12}
        md={6}
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          p: 4,
          position: 'relative',
        }}
        className={styles.loginLogoWrapper}
      >
        <div className={styles.loginLogo}>
          <img src="/fullLogo.svg" alt="Logo" width={250} loading="lazy" />
        </div>
        <Box
          sx={{
            position: 'absolute',
            bottom: 40,
            left: 0,
            right: 0,
            textAlign: 'center',
            color: 'white',
            opacity: 0.9,
          }}
        >
          <Typography variant="h6" sx={{ mb: 1 }}>
            {t('login.welcome')}
          </Typography>
          <Typography variant="body1">{t('login.login')}</Typography>
        </Box>
      </Grid>
      <Grid
        item
        xs={12}
        md={6}
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          p: { xs: 4, md: 8 },
          bgcolor: 'background.paper',
        }}
      >
        <Box
          component="form"
          onSubmit={handleSubmit(onSubmit)}
          noValidate
          className={styles.loginForm}
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: 3,
            p: 4,
            width: '100%',
            maxWidth: 400,
          }}
        >
          {/* <Typography
            variant="h4"
            component="h1"
            gutterBottom
            color="primary"
            textAlign="center"
            fontWeight="bold"
            sx={{ mb: 1 }}
          >
            {t('login.welcome')}
          </Typography>
          <Typography variant="body1" color="text.secondary" textAlign="center" sx={{ mb: 4 }}>
            {t('login.login')}
          </Typography> */}
          {error && (
            <Alert
              severity="error"
              sx={{
                mb: 2,
                borderRadius: 2,
                '& .MuiAlert-icon': {
                  fontSize: '2rem',
                },
              }}
            >
              {error}
            </Alert>
          )}
          <Controller
            control={control}
            name="email"
            render={({ field }) => (
              <TextField
                inputRef={emailRef}
                required
                fullWidth
                id="email"
                label={t('login.email')}
                type="email"
                autoComplete="email"
                error={!!errors.email}
                helperText={errors.email?.message}
                {...field}
                sx={{
                  '& .MuiInputLabel-root': {
                    fontSize: '1rem',
                  },
                  '& .MuiOutlinedInput-root': {
                    fontSize: '1rem',
                  },
                }}
              />
            )}
          />
          <Controller
            control={control}
            name="password"
            render={({ field }) => (
              <TextField
                inputRef={passwordRef}
                required
                fullWidth
                id="password"
                label={t('login.password')}
                type={showPassword ? 'text' : 'password'}
                autoComplete="current-password"
                error={!!errors.password}
                helperText={errors.password?.message}
                {...field}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={handleClickShowPassword}
                        onMouseDown={handleMouseDownPassword}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
                sx={{
                  '& .MuiInputLabel-root': {
                    fontSize: '1rem',
                  },
                  '& .MuiOutlinedInput-root': {
                    fontSize: '1rem',
                  },
                }}
              />
            )}
          />
          <Button
            type="submit"
            fullWidth
            variant="contained"
            size="large"
            disabled={loading}
            sx={{
              mt: 2,
              py: 1.5,
              fontSize: '1.1rem',
              fontWeight: 600,
            }}
          >
            {loading ? t('login.Signing in...') : t('login.Sign In')}
          </Button>
        </Box>
      </Grid>
    </Grid>
  );
};

export default Login;
