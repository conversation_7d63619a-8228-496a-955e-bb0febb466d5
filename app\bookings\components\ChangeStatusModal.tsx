'use client';
import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Typography,
  Box,
  Alert,
  Chip,
  IconButton,
  CircularProgress,
  Snackbar,
  RadioGroup,
  FormControlLabel,
  Radio,
} from '@mui/material';
import { Close as CloseIcon, Warning as WarningIcon } from '@mui/icons-material';
import { useMutation, useQuery } from '@apollo/client';
import { useTranslation } from 'react-i18next';
import {
  ACCEPT_RENTAL_MUTATION,
  REJECT_RENTAL_MUTATION,
  CLOSE_RENTAL_MUTATION,
  CAR_RECEIVED_MUTATION,
  ALLY_RECEIVE_CAR_MUTATION,
} from '../../gql/mutations/bookings';
import { CANCELLED_REASONS_QUERY } from '../../gql/queries/bookings';

interface ChangeStatusModalProps {
  open: boolean;
  onClose: () => void;
  bookingId: string;
  currentStatus: string;
  onStatusChanged: () => void;
}

interface CancelledReason {
  id: string;
  arBody: string;
  enBody: string;
}

const BOOKING_STATUSES = [
  { value: 'pending', label: 'bookings.status.pending', color: 'warning' },
  { value: 'confirmed', label: 'bookings.status.confirmed', color: 'info' },
  { value: 'car_received', label: 'bookings.status.carReceived', color: 'primary' },
  { value: 'invoiced', label: 'bookings.status.invoiced', color: 'secondary' },
  { value: 'closed', label: 'bookings.status.closed', color: 'success' },
  { value: 'cancelled', label: 'bookings.status.cancelled', color: 'error' },
  { value: 'rejected', label: 'bookings.status.rejected', color: 'error' },
];

const STATUS_TRANSITIONS: Record<string, string[]> = {
  pending: ['confirmed', 'rejected'],
  confirmed: ['car_received', 'cancelled'],
  car_received: ['invoiced', 'cancelled'],
  invoiced: ['closed'],
  closed: [],
  cancelled: [],
  rejected: [],
};

export default function ChangeStatusModal({
  open,
  onClose,
  bookingId,
  currentStatus,
  onStatusChanged,
}: ChangeStatusModalProps) {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [newStatus, setNewStatus] = useState('');
  const [reason, setReason] = useState('');
  const [closeReasonId, setCloseReasonId] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  // Fetch cancelled reasons for close operation
  const { data: cancelledReasonsData } = useQuery(CANCELLED_REASONS_QUERY, {
    variables: {
      status: 'closed',
      userType: 'admin',
      rentalType: 'regular',
    },
    skip: newStatus !== 'closed',
  });

  const [acceptRental] = useMutation(ACCEPT_RENTAL_MUTATION);
  const [rejectRental] = useMutation(REJECT_RENTAL_MUTATION);
  const [closeRental] = useMutation(CLOSE_RENTAL_MUTATION);
  const [carReceived] = useMutation(CAR_RECEIVED_MUTATION);
  const [allyReceiveCar] = useMutation(ALLY_RECEIVE_CAR_MUTATION);

  const handleClose = () => {
    setNewStatus('');
    setReason('');
    setCloseReasonId('');
    setError('');
    setLoading(false);
    setShowSuccess(false);
    onClose();
  };

  const handleSubmit = async () => {
    if (!newStatus) {
      setError(t('bookings.status.errors.selectNewStatus'));
      return;
    }

    // Validate close reason for closing rental
    if (newStatus === 'closed' && !closeReasonId) {
      setError(t('bookings.status.errors.closeReasonRequired'));
      return;
    }

    // Some status changes require a reason
    const requiresReason = ['rejected', 'cancelled'].includes(newStatus);
    if (requiresReason && !reason.trim()) {
      setError(t('bookings.status.errors.reasonRequired'));
      return;
    }

    setError('');
    setLoading(true);

    try {
      let result;

      switch (newStatus) {
        case 'confirmed':
          result = await acceptRental({
            variables: { rentalId: bookingId },
          });
          break;

        case 'rejected':
          result = await rejectRental({
            variables: {
              rentalId: bookingId,
              reason: reason.trim(),
            },
          });
          break;

        case 'car_received':
          result = await carReceived({
            variables: { rentalId: bookingId },
          });
          break;

        case 'invoiced':
          result = await allyReceiveCar({
            variables: {
              rentalId: bookingId,
              invoicePic: '', // This would need to be handled separately
              newGrandTotal: 0, // This would need to be handled separately
            },
          });
          break;

        case 'closed':
          result = await closeRental({
            variables: {
              rentalId: bookingId,
              closeReasonId: parseInt(closeReasonId),
              note: reason.trim() || null,
            },
          });
          break;

        case 'cancelled':
          result = await rejectRental({
            variables: {
              rentalId: bookingId,
              reason: reason.trim(),
            },
          });
          break;

        default:
          throw new Error(t('bookings.status.errors.unsupportedStatus'));
      }

      // Check if the mutation was successful
      const mutationData = Object.values(result.data)[0] as any;
      if (mutationData.status === 'success' || !mutationData.errors?.length) {
        setShowSuccess(true);
        setTimeout(() => {
          onStatusChanged();
          handleClose();
        }, 1500);
      } else {
        setError(mutationData.errors?.[0] || t('bookings.status.errors.failedToChange'));
      }
    } catch (err: any) {
      console.error('Error changing status:', err);
      setError(err.message || t('bookings.status.errors.generalError'));
    } finally {
      setLoading(false);
    }
  };

  const allowedStatuses = STATUS_TRANSITIONS[currentStatus] || [];
  const selectedStatusInfo = BOOKING_STATUSES.find((s) => s.value === newStatus);
  const currentStatusInfo = BOOKING_STATUSES.find((s) => s.value === currentStatus);

  const isDestructiveChange = ['cancelled', 'rejected'].includes(newStatus);
  const cancelledReasons = cancelledReasonsData?.cancelledReasons || [];

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6">{t('bookings.status.changeTitle')}</Typography>
          <IconButton onClick={handleClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ mb: 3 }}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            {t('bookings.status.currentStatus')}:
          </Typography>
          <Chip
            label={t(currentStatusInfo?.label || currentStatus)}
            color={currentStatusInfo?.color as any}
            variant="outlined"
          />
        </Box>

        <FormControl fullWidth sx={{ mb: 3 }}>
          <InputLabel>{t('bookings.status.newStatus')}</InputLabel>
          <Select
            value={newStatus}
            onChange={(e) => {
              setNewStatus(e.target.value);
              setCloseReasonId('');
              setReason('');
            }}
            label={t('bookings.status.newStatus')}
            disabled={loading}
          >
            {allowedStatuses.map((status) => {
              const statusInfo = BOOKING_STATUSES.find((s) => s.value === status);
              return (
                <MenuItem key={status} value={status}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Chip
                      label={t(statusInfo?.label || status)}
                      color={statusInfo?.color as any}
                      size="small"
                      variant="outlined"
                    />
                  </Box>
                </MenuItem>
              );
            })}
          </Select>
        </FormControl>

        {isDestructiveChange && (
          <Alert severity="warning" sx={{ mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <WarningIcon />
              <Typography variant="body2">{t('bookings.status.destructiveWarning')}</Typography>
            </Box>
          </Alert>
        )}

        {/* Close Reason Selection for Closing Rental */}
        {newStatus === 'closed' && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" sx={{ mb: 2 }}>
              {t('bookings.status.closeReason')}
            </Typography>
            <RadioGroup value={closeReasonId} onChange={(e) => setCloseReasonId(e.target.value)}>
              {cancelledReasons.map((closeReason: CancelledReason) => (
                <FormControlLabel
                  key={closeReason.id}
                  value={closeReason.id}
                  control={<Radio />}
                  label={isRTL ? closeReason.arBody : closeReason.enBody}
                />
              ))}
            </RadioGroup>
          </Box>
        )}

        {['rejected', 'cancelled', 'closed'].includes(newStatus) && (
          <TextField
            fullWidth
            multiline
            rows={3}
            label={newStatus === 'closed' ? t('bookings.status.note') : t('bookings.status.reason')}
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            placeholder={
              newStatus === 'closed'
                ? t('bookings.status.notePlaceholder')
                : t('bookings.status.reasonPlaceholder')
            }
            required={newStatus !== 'closed'}
            disabled={loading}
            sx={{ mb: 2 }}
          />
        )}

        {error && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {error}
          </Alert>
        )}
      </DialogContent>

      <DialogActions>
        <Button
          onClick={handleClose}
          disabled={loading}
          sx={{
            backgroundColor: '#f44336',
            color: 'white',
            '&:hover': {
              backgroundColor: '#d32f2f',
            },
            '&:disabled': {
              backgroundColor: '#cccccc',
              color: '#666666',
            },
          }}
        >
          {t('common.cancel')}
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={loading || !newStatus || (newStatus === 'closed' && !closeReasonId)}
          sx={{
            backgroundColor: '#9c27b0',
            color: 'white',
            '&:hover': {
              backgroundColor: '#7b1fa2',
            },
            '&:disabled': {
              backgroundColor: '#cccccc',
              color: '#666666',
            },
          }}
        >
          {loading ? <CircularProgress size={20} /> : t('bookings.status.change')}
        </Button>
      </DialogActions>

      <Snackbar
        open={showSuccess}
        autoHideDuration={1500}
        onClose={() => setShowSuccess(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert severity="success" onClose={() => setShowSuccess(false)}>
          {t('bookings.status.statusChangedSuccessfully')}
        </Alert>
      </Snackbar>
    </Dialog>
  );
}
