'use client';
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation } from '@apollo/client';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Checkbox,
  FormControlLabel,
  Grid,
  Alert,
  CircularProgress,
  Snackbar,
} from '@mui/material';
import { UPDATE_RENTAL_EXTRA_SERVICES_MUTATION } from '../../gql/mutations/bookings';

interface CurrentExtraService {
  id: string;
  extraServiceId: string;
  extraServiceType: string;
  arTitle?: string;
  enTitle?: string;
  totalServiceValue: number;
}

interface UpdateExtraServiceModalProps {
  open: boolean;
  onClose: () => void;
  bookingId: string;
  carId: string;
  allyId?: string;
  currentExtraServices: CurrentExtraService[];
  onServicesUpdated: () => void;
}

export default function UpdateExtraServiceModal({
  open,
  onClose,
  bookingId,
  carId,
  allyId,
  currentExtraServices = [],
  onServicesUpdated,
}: UpdateExtraServiceModalProps) {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [allyExtraServicesIds, setAllyExtraServicesIds] = useState<string[]>([]);
  const [branchExtraServicesIds, setBranchExtraServicesIds] = useState<string[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [showSuccess, setShowSuccess] = useState<boolean>(false);

  const [customerUpdateRentalExtraServices] = useMutation(UPDATE_RENTAL_EXTRA_SERVICES_MUTATION);

  useEffect(() => {
    if (open && currentExtraServices) {
      // Initialize ally extra services
      const allyServiceIds = currentExtraServices
        .filter((service) => service.extraServiceType === 'ally_company')
        .map((service) => service.extraServiceId);
      setAllyExtraServicesIds(allyServiceIds);

      // Initialize branch extra services
      const branchServiceIds = currentExtraServices
        .filter((service) => service.extraServiceType === 'branch')
        .map((service) => service.extraServiceId);
      setBranchExtraServicesIds(branchServiceIds);
    }
  }, [open, currentExtraServices]);

  const handleSubmit = async () => {
    setLoading(true);
    setError('');

    try {
      const result = await customerUpdateRentalExtraServices({
        variables: {
          rentalId: bookingId,
          allyExtraServices: allyExtraServicesIds,
          branchExtraServices: branchExtraServicesIds,
        },
      });

      if (!result?.data?.customerUpdateRentalExtraServices?.errors?.length) {
        setShowSuccess(true);
        setTimeout(() => {
          onServicesUpdated();
          handleClose();
        }, 1500);
      } else {
        setError(
          result.data.customerUpdateRentalExtraServices.errors[0] ||
            t('bookings.extraServices.errors.failedToUpdate')
        );
      }
    } catch (err: any) {
      console.error('Error updating extra services:', err);
      setError(err.message || t('bookings.extraServices.errors.generalError'));
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setAllyExtraServicesIds([]);
    setBranchExtraServicesIds([]);
    setError('');
    setLoading(false);
    setShowSuccess(false);
    onClose();
  };

  const handleAllyServiceChange = (serviceId: string, checked: boolean) => {
    if (checked) {
      setAllyExtraServicesIds([...allyExtraServicesIds, serviceId]);
    } else {
      setAllyExtraServicesIds(allyExtraServicesIds.filter((id) => id !== serviceId));
    }
  };

  const handleBranchServiceChange = (serviceId: string, checked: boolean) => {
    if (checked) {
      setBranchExtraServicesIds([...branchExtraServicesIds, serviceId]);
    } else {
      setBranchExtraServicesIds(branchExtraServicesIds.filter((id) => id !== serviceId));
    }
  };

  const allyServices = currentExtraServices.filter(
    (service) => service.extraServiceType === 'ally_company'
  );
  const branchServices = currentExtraServices.filter(
    (service) => service.extraServiceType === 'branch'
  );

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Typography variant="h6">{t('bookings.extraServices.updateServices.title')}</Typography>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ mt: 2 }}>
          {/* Current Ally Extra Services */}
          {allyServices.length > 0 && (
            <Box sx={{ mb: 4 }}>
              <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
                {t('bookings.extraServices.allyServices')}
              </Typography>
              <Grid container spacing={2}>
                {allyServices.map((service) => (
                  <Grid item xs={12} sm={6} md={4} key={service.id}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={allyExtraServicesIds.includes(service.extraServiceId)}
                          onChange={(e) =>
                            handleAllyServiceChange(service.extraServiceId, e.target.checked)
                          }
                        />
                      }
                      label={
                        <Box>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {isRTL ? service.arTitle : service.enTitle}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {service.totalServiceValue} {t('common.currency')}
                          </Typography>
                        </Box>
                      }
                    />
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}

          {/* Current Branch Extra Services */}
          {branchServices.length > 0 && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
                {t('bookings.extraServices.branchServices')}
              </Typography>
              <Grid container spacing={2}>
                {branchServices.map((service) => (
                  <Grid item xs={12} sm={6} md={4} key={service.id}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={branchExtraServicesIds.includes(service.extraServiceId)}
                          onChange={(e) =>
                            handleBranchServiceChange(service.extraServiceId, e.target.checked)
                          }
                        />
                      }
                      label={
                        <Box>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {isRTL ? service.arTitle : service.enTitle}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {service.totalServiceValue} {t('common.currency')}
                          </Typography>
                        </Box>
                      }
                    />
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}

          {allyServices.length === 0 && branchServices.length === 0 && (
            <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
              {t('bookings.extraServices.noServicesAvailable')}
            </Typography>
          )}

          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}
        </Box>
      </DialogContent>

      <DialogActions>
        <Button
          onClick={handleClose}
          disabled={loading}
          sx={{
            backgroundColor: '#f44336',
            color: 'white',
            '&:hover': {
              backgroundColor: '#d32f2f',
            },
            '&:disabled': {
              backgroundColor: '#cccccc',
              color: '#666666',
            },
          }}
        >
          {t('common.cancel')}
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={loading}
          sx={{
            backgroundColor: '#673ab7',
            color: 'white',
            '&:hover': {
              backgroundColor: '#512da8',
            },
            '&:disabled': {
              backgroundColor: '#cccccc',
              color: '#666666',
            },
          }}
        >
          {loading ? <CircularProgress size={20} /> : t('bookings.extraServices.update')}
        </Button>
      </DialogActions>

      <Snackbar
        open={showSuccess}
        autoHideDuration={1500}
        onClose={() => setShowSuccess(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert severity="success" onClose={() => setShowSuccess(false)}>
          {t('bookings.extraServices.servicesUpdatedSuccessfully')}
        </Alert>
      </Snackbar>
    </Dialog>
  );
}
