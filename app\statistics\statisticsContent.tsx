'use client';
import { useState, useEffect } from 'react';
import { useQuery } from '@apollo/client';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import {
  mockCustomerStatistics,
  mockRentalsPerMonth,
  mockRentalCounts,
  mockRentalCountsAr,
} from './mockData';
import {
  Box,
  Typography,
  Grid,
  CircularProgress,
  Alert,
  Paper,
  Divider,
  useTheme,
} from '@mui/material';
import {
  CustomerStatistics_Query,
  RentalsPerMonth_Query,
  RentalsCount_Query,
} from '../gql/queries/statistics';
import RentalsAreaChart from './components/RentalsAreaChart';
import RentalStatusPieChart from './components/RentalStatusPieChart';
import UsersLineChart from './components/UsersLineChart';
import StatisticCard from './components/StatisticCard';
import { DirectionsCar, People, AttachMoney, CalendarMonth } from '@mui/icons-material';

export default function StatisticsContent() {
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const { user } = useSelector((state: any) => state.auth);
  const isAlly = user?.ally_id;

  // Fetch customer statistics
  const {
    data: customerStatistics,
    // loading: loadingCustomers,
    // error: customerError,
  } = useQuery(CustomerStatistics_Query, {
    skip: true, // Skip the actual API call for testing
  });

  // Use mock data for testing
  const mockCustomerData = mockCustomerStatistics;

  // Fetch rentals per month
  const {
    data: rentalsPerMonth,
    // loading: loadingRentals,
    // error: rentalsError,
  } = useQuery(RentalsPerMonth_Query, {
    skip: true, // Skip the actual API call for testing
  });

  // Use mock data for testing
  const mockRentalsData = mockRentalsPerMonth;

  // Fetch rental status counts
  const {
    data: rentalCounts,
    // loading: loadingCounts,
    // error: countsError,
  } = useQuery(RentalsCount_Query, {
    variables: { userId: user?.id },
    skip: true, // Skip the actual API call for testing
  });

  // Use mock data for testing
  const mockRentalCountsData = i18n.language === 'ar' ? mockRentalCountsAr : mockRentalCounts;

  // State for processed data
  const [userStats, setUserStats] = useState<any[]>([]);
  const [rentalStats, setRentalStats] = useState<any>({
    labels: [],
    rentals: [],
    rentToOwn: [],
  });
  const [statusStats, setStatusStats] = useState<any>({
    labels: [],
    data: [],
    colors: [],
  });

  // Process customer statistics data
  useEffect(() => {
    // Use mock data if no API data is available
    const statsData = customerStatistics?.customerStatistics || mockCustomerData.customerStatistics;

    if (statsData) {
      const data = statsData.map((item: any) => ({
        name: item.month,
        uv: item.count,
        [i18n.language === 'ar' ? 'أجمالي المستخدمين' : 'Total Users']: item.count,
      }));
      setUserStats(data);
    }
  }, [customerStatistics, mockCustomerData, i18n.language]);

  // Process rentals per month data
  useEffect(() => {
    // Use mock data if no API data is available
    const monthsData =
      rentalsPerMonth?.rentalsPerMonth?.months || mockRentalsData.rentalsPerMonth.months;

    if (monthsData) {
      const labels = Object.keys(monthsData);
      const rentals = labels.map((month) => monthsData[month].rentals);
      const rentToOwn = labels.map((month) => monthsData[month].rent_to_own);

      setRentalStats({
        labels,
        rentals,
        rentToOwn,
      });
    }
  }, [rentalsPerMonth, mockRentalsData]);

  // Process rental status counts
  useEffect(() => {
    // Use mock data if no API data is available
    const statusData = rentalCounts?.rentalsCount?.all || mockRentalCountsData.rentalsCount.all;

    if (statusData) {
      const validStatuses =
        i18n.language === 'en'
          ? ['Pending', 'Confirmed', 'Car received', 'Invoiced', 'Closed', 'Cancelled']
          : ['قيد الانتظار', 'مؤكد', 'تم الاستلام', 'مفوترة', 'مغلق', 'ملغي'];

      const filteredData = statusData.filter((item: any) => validStatuses.includes(item[0]));

      const labels = filteredData.map((item: any) => item[0]);
      const data = filteredData.map((item: any) => item[1]);

      // Define colors for each status
      const colors = [
        theme.palette.error.main,
        theme.palette.primary.main,
        theme.palette.success.main,
        theme.palette.warning.main,
        theme.palette.info.main,
        theme.palette.grey[500],
      ];

      setStatusStats({
        labels,
        data,
        colors,
      });
    }
  }, [rentalCounts, mockRentalCountsData, i18n.language, theme]);

  // Calculate total values for statistic cards
  const totalUsers = userStats.length > 0 ? userStats.reduce((sum, item) => sum + item.uv, 0) : 0;

  const totalRentals =
    rentalStats.rentals.length > 0
      ? rentalStats.rentals.reduce((sum: number, val: number) => sum + val, 0)
      : 0;

  const totalRentToOwn =
    rentalStats.rentToOwn.length > 0
      ? rentalStats.rentToOwn.reduce((sum: number, val: number) => sum + val, 0)
      : 0;

  const activeRentals =
    statusStats.labels.length > 0
      ? statusStats.data[
          statusStats.labels.findIndex(
            (label: string) => label === (i18n.language === 'en' ? 'Confirmed' : 'مؤكد')
          )
        ] || 0
      : 0;

  // Loading state - set to false when using mock data
  const isLoading = false; // (loadingCustomers || loadingRentals || loadingCounts);

  // Error state - set to false when using mock data
  const hasError = false; // (customerError || rentalsError || countsError);

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (hasError) {
    return (
      <Box sx={{ p: 2 }}>
        <Alert severity="error">
          {t('Error loading statistics data. Please try again later.')}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 1, sm: 2 } }}>
      <Typography variant="h4" component="h1" mb={4} gutterBottom>
        {t('Dashboard Statistics')}
      </Typography>

      {/* Statistic Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatisticCard
            title={t('Total Rentals')}
            value={totalRentals}
            icon={<DirectionsCar />}
            color={theme.palette.primary.main}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatisticCard
            title={t('Active Rentals')}
            value={activeRentals}
            icon={<CalendarMonth />}
            color={theme.palette.success.main}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatisticCard
            title={t('Rent-to-Own')}
            value={totalRentToOwn}
            icon={<AttachMoney />}
            color={theme.palette.warning.main}
          />
        </Grid>
        {!isAlly && (
          <Grid item xs={12} sm={6} md={3}>
            <StatisticCard
              title={t('Total Users')}
              value={totalUsers}
              icon={<People />}
              color={theme.palette.info.main}
            />
          </Grid>
        )}
      </Grid>

      {/* Charts */}
      <Grid container spacing={3}>
        {/* Rentals Area Chart */}
        <Grid item xs={12} lg={8}>
          <Paper
            elevation={2}
            sx={{
              p: 2,
              height: '100%',
              borderRadius: 2,
              overflow: 'hidden',
            }}
          >
            <Typography variant="h6" gutterBottom>
              {t('Rentals Over Time')}
            </Typography>
            <Divider sx={{ mb: 2 }} />
            <Box sx={{ height: 300 }}>
              <RentalsAreaChart
                labels={rentalStats.labels}
                rentals={rentalStats.rentals}
                rentToOwn={rentalStats.rentToOwn}
              />
            </Box>
          </Paper>
        </Grid>

        {/* Rental Status Pie Chart */}
        <Grid item xs={12} sm={6} lg={4}>
          <Paper
            elevation={2}
            sx={{
              p: 2,
              height: '100%',
              borderRadius: 2,
            }}
          >
            <Typography
              variant="h6"
              gutterBottom
              align="center"
              sx={{
                fontWeight: 'bold',
                borderBottom: '1px solid #eee',
                pb: 1,
                mb: 2,
              }}
            >
              {t('Rentals by Status')}
            </Typography>
            <Box
              sx={{ height: 400, display: 'flex', justifyContent: 'center', alignItems: 'center' }}
            >
              <RentalStatusPieChart
                labels={statusStats.labels}
                data={statusStats.data}
                colors={statusStats.colors}
              />
            </Box>
          </Paper>
        </Grid>

        {/* Users Line Chart - Only show for non-ally users */}
        {!isAlly && (
          <Grid item xs={12}>
            <Paper
              elevation={2}
              sx={{
                p: 2,
                borderRadius: 2,
              }}
            >
              <Typography variant="h6" gutterBottom>
                {t('User Growth')}
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <Box sx={{ height: 300 }}>
                <UsersLineChart data={userStats} />
              </Box>
            </Paper>
          </Grid>
        )}
      </Grid>
    </Box>
  );
}
