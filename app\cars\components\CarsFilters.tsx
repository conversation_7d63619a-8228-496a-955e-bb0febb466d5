'use client';
import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Grid,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete,
  Chip,
  Typography,
  InputAdornment,
  IconButton,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';
import { format } from 'date-fns';
import dayjs from 'dayjs';
import 'dayjs/locale/ar';
import 'dayjs/locale/en';
import MakesDropdown from '../../components/shared/makesDropdowns';
import AlliesDropdown from '@/app/components/shared/alliesDropdown';
import CitiesDropdown from '@/app/components/shared/cities.Dropdown';
import BranchesDropdown from '@/app/components/shared/branchesDropdown';
import CarModelsDropdown from '@/app/components/shared/carModelsDropdown';
import VehicleTypesDropdown from '@/app/components/shared/vehicleTypes';
import RentType from '@/app/components/shared/rentType';
import Transmission from '@/app/components/shared/transmission';

// Mock data for filters
const CITIES = [
  { id: 1, name: 'Riyadh', arName: 'الرياض' },
  { id: 2, name: 'Jeddah', arName: 'جدة' },
  { id: 3, name: 'Dammam', arName: 'الدمام' },
  { id: 4, name: 'Makkah', arName: 'مكة' },
  { id: 5, name: 'Madinah', arName: 'المدينة' },
];

const MAKES = [
  { id: 1, name: 'Toyota', arName: 'تويوتا' },
  { id: 2, name: 'Nissan', arName: 'نيسان' },
  { id: 3, name: 'Honda', arName: 'هوندا' },
  { id: 4, name: 'Hyundai', arName: 'هيونداي' },
  { id: 5, name: 'Kia', arName: 'كيا' },
  { id: 6, name: 'Mercedes', arName: 'مرسيدس' },
  { id: 7, name: 'BMW', arName: 'بي إم دبليو' },
  { id: 8, name: 'Lexus', arName: 'لكزس' },
  { id: 9, name: 'Ford', arName: 'فورد' },
];

const PAYMENT_METHODS = [
  { id: 'credit_card', name: 'Credit Card', arName: 'بطاقة ائتمان' },
  { id: 'debit_card', name: 'Debit Card', arName: 'بطاقة خصم' },
  { id: 'apple_pay', name: 'Apple Pay', arName: 'آبل باي' },
  { id: 'stc_pay', name: 'STC Pay', arName: 'إس تي سي باي' },
  { id: 'cash', name: 'Cash', arName: 'نقدي' },
];

const PAYMENT_BRANDS = [
  { id: 'visa', name: 'Visa', arName: 'فيزا' },
  { id: 'mastercard', name: 'Mastercard', arName: 'ماستركارد' },
  { id: 'american_express', name: 'American Express', arName: 'أمريكان إكسبريس' },
  { id: 'mada', name: 'Mada', arName: 'مدى' },
];

const ALLIES = [
  { id: 101, name: 'Premium Cars', arName: 'سيارات بريميوم' },
  { id: 102, name: 'Luxury Rentals', arName: 'تأجير فاخر' },
  { id: 103, name: 'Economy Cars', arName: 'سيارات اقتصادية' },
];

interface BookingsFiltersProps {
  onApply: (filters: any) => void;
  onReset: () => void;
  initialFilters?: any;
}

export default function BookingsFilters({
  onApply,
  onReset,
  initialFilters = {},
}: BookingsFiltersProps) {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';

  // Define filter state type
  interface FilterState {
    customerName: string;
    bookingNo: string;
    plateNo: string;
    customerMobile: string;
    makeName: string[] | null;
    cityName: string[] | null;
    allyName: string;
    allyCompanyId: string | null;
    paymentMethod: string;
    paymentBrand: string[] | null;
    status: string[] | null;
    subStatus: string[] | null;
    from: string | null;
    to: string | null;
    pickUpDate: string | null;
    pickUpDateFrom: string | null;
    dropOffDate: string | null;
    dropOffDateTo: string | null;
    [key: string]: string | number | string[] | Date | null;
  }

  // Filter state
  const [filters, setFilters] = useState<FilterState>({
    customerName: '',
    bookingNo: '',
    plateNo: '',
    customerMobile: '',
    makeName: null,
    cityName: null,
    allyName: '',
    allyCompanyId: null,
    paymentMethod: '',
    paymentBrand: null,
    status: null,
    subStatus: null,
    from: null,
    to: null,
    pickUpDate: null,
    pickUpDateFrom: null,
    dropOffDate: null,
    dropOffDateTo: null,
    ...initialFilters,
  });

  // Update filters when initialFilters change
  useEffect(() => {
    setFilters((prev) => ({ ...prev, ...initialFilters }));
  }, [initialFilters]);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFilters((prev) => ({ ...prev, [name]: value }));
  };

  // Handle select change
  const handleSelectChange = (e: any) => {
    const { name, value } = e.target;

    // Handle array fields
    if (name === 'paymentBrand') {
      const arrayValue = value ? [value] : null;
      setFilters((prev) => ({ ...prev, [name]: arrayValue }));
    } else {
      setFilters((prev) => ({ ...prev, [name]: value }));
    }
  };

  // Handle autocomplete change
  const handleAutocompleteChange = (name: string, value: any) => {
    // Convert to string array for fields that need it
    if (['makeName', 'cityName', 'paymentBrand', 'status', 'subStatus'].includes(name)) {
      const arrayValue = value ? [value.toString()] : null;
      setFilters((prev) => ({ ...prev, [name]: arrayValue }));
    } else {
      setFilters((prev) => ({ ...prev, [name]: value }));
    }
  };

  // Handle date change
  const handleDateChange = (name: string, value: any) => {
    // Ensure we're storing a valid Date object or null
    const dateValue = value ? new Date(value) : null;
    setFilters((prev) => ({ ...prev, [name]: dateValue }));
  };

  // Handle form submit
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Format dates for API
    const formattedFilters: Record<string, any> = { ...filters };

    if (formattedFilters.pickUpDateFrom) {
      formattedFilters.pickUpDateFrom = format(
        new Date(formattedFilters.pickUpDateFrom),
        'yyyy-MM-dd'
      );
    }

    if (formattedFilters.pickUpDate) {
      formattedFilters.pickUpDate = format(new Date(formattedFilters.pickUpDate), 'yyyy-MM-dd');
    }

    if (formattedFilters.dropOffDateFrom) {
      formattedFilters.dropOffDateFrom = format(
        new Date(formattedFilters.dropOffDateFrom),
        'yyyy-MM-dd'
      );
    }

    if (formattedFilters.dropOffDateTo) {
      formattedFilters.dropOffDateTo = format(
        new Date(formattedFilters.dropOffDateTo),
        'yyyy-MM-dd'
      );
    }

    // Remove empty values
    Object.keys(formattedFilters).forEach((key) => {
      if (formattedFilters[key] === '' || formattedFilters[key] === null) {
        delete formattedFilters[key];
      }
    });

    onApply(formattedFilters);
  };

  // Handle form reset
  const handleReset = () => {
    setFilters({
      customerName: '',
      bookingNo: '',
      plateNo: '',
      customerMobile: '',
      makeName: null,
      cityName: null,
      allyName: '',
      allyCompanyId: null,
      paymentMethod: '',
      paymentBrand: null,
      status: null,
      subStatus: null,
      from: null,
      to: null,
      pickUpDate: null,
      pickUpDateFrom: null,
      dropOffDate: null,
      dropOffDateTo: null,
    });
    onReset();
  };

  // Clear a single field
  const clearField = (name: string) => {
    // Handle date fields differently
    if (['pickUpDateFrom', 'pickUpDateTo', 'dropOffDateFrom', 'dropOffDateTo'].includes(name)) {
      setFilters((prev) => ({ ...prev, [name]: null }));
    } else {
      setFilters((prev) => ({ ...prev, [name]: '' }));
    }
  };

  return (
    <Box component="form" onSubmit={handleSubmit} noValidate>
      <Grid container spacing={{ xs: 1, sm: 2 }} sx={{ maxWidth: '100%', overflow: 'hidden' }}>
        {/* Search Fields */}
        <Grid item xs={12}>
          <Typography variant="subtitle1" fontWeight={500} gutterBottom>
            {t('Search')}
          </Typography>
        </Grid>

        <Grid item xs={12} sm={6} md={4} lg={3}>
          <TextField
            fullWidth
            id="acrissCode"
            name="acrissCode"
            label={t('Acriss Code')}
            InputLabelProps={{
              sx: { padding: '0 20px' },
            }}
            value={filters.acrissCode}
            onChange={handleInputChange}
            variant="outlined"
            size="small"
            InputProps={{
              endAdornment: filters.acrissCode ? (
                <InputAdornment position="end">
                  <IconButton
                    aria-label="clear acriss Code "
                    onClick={() => clearField('acrissCode')}
                    edge="end"
                    size="small"
                  >
                    <ClearIcon fontSize="small" />
                  </IconButton>
                </InputAdornment>
              ) : null,
            }}
          />
        </Grid>

     

        <Grid item xs={12} sm={6} md={3}>
          <TextField
            fullWidth
            id="plateNo"
            name="plateNo"
            label={t('Plate Number')}
            InputLabelProps={{
              sx: { padding: '0 20px' },
            }}
            value={filters.plateNo}
            onChange={handleInputChange}
            variant="outlined"
            size="small"
            InputProps={{
              endAdornment: filters.plateNo ? (
                <InputAdornment position="end">
                  <IconButton
                    aria-label="clear plate number"
                    onClick={() => clearField('plateNo')}
                    edge="end"
                    size="small"
                  >
                    <ClearIcon fontSize="small" />
                  </IconButton>
                </InputAdornment>
              ) : null,
            }}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <TextField
            fullWidth
            id="dailyPrice"
            name="dailyPrice"
            label={t('daily Price')}
            InputLabelProps={{
              sx: { padding: '0 20px' },
            }}
            type='number'
            value={filters.dailyPrice}
            onChange={handleInputChange}
            variant="outlined"
            size="small"
            InputProps={{
              endAdornment: filters.customerMobile ? (
                <InputAdornment position="end">
                  <IconButton
                    aria-label="clear customer mobile"
                    onClick={() => clearField('customerMobile')}
                    edge="end"
                    size="small"
                  >
                    <ClearIcon fontSize="small" />
                  </IconButton>
                </InputAdornment>
              ) : null,
            }}
          />
        </Grid>

        {/* Dropdown Filters */}
        <Grid item xs={12} mt={2}>
          <Typography variant="subtitle1" fontWeight={500} gutterBottom>
            {t('Filters')}
          </Typography>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
         <AlliesDropdown 
            filters={filters}
            handleAutocompleteChange={handleAutocompleteChange}
            />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
         <BranchesDropdown 
            filters={filters}
            handleAutocompleteChange={handleAutocompleteChange}
            />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
        <CitiesDropdown 
        filters={filters}
        handleAutocompleteChange={handleAutocompleteChange}

        />
        </Grid>

     

        <Grid item xs={12} sm={6} md={3}>
          <MakesDropdown 
            filters={filters}
            handleAutocompleteChange={handleAutocompleteChange}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <CarModelsDropdown 
            filters={filters}
            handleAutocompleteChange={handleAutocompleteChange}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <VehicleTypesDropdown 
            filters={filters}
            handleAutocompleteChange={handleAutocompleteChange}
          />
          
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <RentType 
            filters={filters}
            handleAutocompleteChange={handleAutocompleteChange}
          />
          
        </Grid>
     
        <Grid item xs={12} sm={6} md={3}>
          <Transmission
            filters={filters}
            handleAutocompleteChange={handleAutocompleteChange}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <FormControl fullWidth size="small">
            <InputLabel id="paymentMethod-label" sx={{ padding: '0 20px' }}>
              {t('Payment Method')}
            </InputLabel>
            <Select
              labelId="paymentMethod-label"
              id="paymentMethod"
              name="paymentMethod"
              value={filters.paymentMethod}
              onChange={handleSelectChange}
              label={t('Payment Method')}
            >
              <MenuItem value="">{t('All')}</MenuItem>
              {PAYMENT_METHODS.map((method) => (
                <MenuItem key={method.id} value={method.id}>
                  {isRTL ? method.arName : method.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
      
        <Grid item xs={12} sm={6} md={3}>
          <FormControl fullWidth size="small">
            <InputLabel id="paymentBrand-label" sx={{ padding: '0 20px' }}>
              {t('Payment Brand')}
            </InputLabel>
            <Select
              labelId="paymentBrand-label"
              id="paymentBrand"
              name="paymentBrand"
              value={
                filters.paymentBrand && filters.paymentBrand.length > 0
                  ? filters.paymentBrand[0]
                  : ''
              }
              onChange={handleSelectChange}
              label={t('Payment Brand')}
            >
              <MenuItem value="">{t('All')}</MenuItem>
              {PAYMENT_BRANDS.map((brand) => (
                <MenuItem key={brand.id} value={brand.id}>
                  {isRTL ? brand.arName : brand.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Date Filters */}
     

      

        {/* Action Buttons */}
        <Grid
          item
          xs={12}
          mt={2}
          sx={{
            display: 'flex',
            justifyContent: { xs: 'center', sm: 'flex-end' },
            gap: 2,
            flexDirection: { xs: 'column', sm: 'row' },
            width: '100%',
          }}
        >
          <Button
            variant="outlined"
            onClick={handleReset}
            sx={{ minWidth: 100, width: { xs: '100%', sm: 'auto' } }}
          >
            {t('Reset')}
          </Button>
          <Button
            type="submit"
            variant="contained"
            color="primary"
            startIcon={<SearchIcon />}
            sx={{ minWidth: 100, width: { xs: '100%', sm: 'auto' } }}
          >
            {t('Apply')}
          </Button>
        </Grid>
      </Grid>
    </Box>
  );
}
