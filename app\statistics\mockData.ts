// Mock data for testing the statistics page

// Customer statistics by month
export const mockCustomerStatistics = {
  customerStatistics: [
    { month: 'Jan', count: 120 },
    { month: 'Feb', count: 150 },
    { month: 'Mar', count: 180 },
    { month: 'Apr', count: 210 },
    { month: 'May', count: 250 },
    { month: 'Jun', count: 280 },
    { month: 'Jul', count: 320 },
    { month: 'Aug', count: 350 },
    { month: 'Sep', count: 380 },
    { month: 'Oct', count: 410 },
    { month: 'Nov', count: 450 },
    { month: 'Dec', count: 500 },
  ],
};

// Rentals per month
export const mockRentalsPerMonth = {
  rentalsPerMonth: {
    months: {
      Jan: { rentals: 85, rent_to_own: 15 },
      Feb: { rentals: 95, rent_to_own: 20 },
      Mar: { rentals: 110, rent_to_own: 25 },
      Apr: { rentals: 130, rent_to_own: 30 },
      May: { rentals: 150, rent_to_own: 35 },
      Jun: { rentals: 170, rent_to_own: 40 },
      Jul: { rentals: 190, rent_to_own: 45 },
      Aug: { rentals: 210, rent_to_own: 50 },
      Sep: { rentals: 230, rent_to_own: 55 },
      Oct: { rentals: 250, rent_to_own: 60 },
      Nov: { rentals: 270, rent_to_own: 65 },
      Dec: { rentals: 290, rent_to_own: 70 },
    },
  },
};

// Rental status counts
export const mockRentalCounts = {
  rentalsCount: {
    all: [
      ['Pending', 120],
      ['Confirmed', 250],
      ['Car received', 140],
      ['Invoiced', 90],
      ['Closed', 310],
      ['Cancelled', 90],
    ],
  },
};

// Arabic version of rental status counts
export const mockRentalCountsAr = {
  rentalsCount: {
    all: [
      ['قيد الانتظار', 120],
      ['مؤكد', 250],
      ['تم الاستلام', 140],
      ['مفوترة', 90],
      ['مغلق', 310],
      ['ملغي', 90],
    ],
  },
};
