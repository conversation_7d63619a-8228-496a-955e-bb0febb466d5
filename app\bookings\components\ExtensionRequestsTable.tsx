'use client';
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation, useQuery, gql } from '@apollo/client';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Select,
  MenuItem,
  FormControl,
  Button,
  IconButton,
  Chip,
  Box,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Tooltip,
  Alert,
  SelectChangeEvent,
} from '@mui/material';
import {
  Edit as EditIcon,
  Check as CheckIcon,
  Cancel as CancelIcon,
  Send as SendIcon,
  Refresh as RefreshIcon,
  Add as AddIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';

// GraphQL mutations - matching old dashboard exactly
const CONFIRM_RENTAL_DATE_EXTENSION_REQUEST = gql`
  mutation ConfirmRentalDateExtensionRequest($rentalExtensionId: ID!) {
    confirmRentalDateExtensionRequest(rentalExtensionId: $rentalExtensionId) {
      success
      message
    }
  }
`;

const CREATE_RENTAL_DATE_EXTENSION_REQUEST = gql`
  mutation CreateRentalDateExtensionRequest(
    $rentalId: ID!
    $dropOffDate: String!
    $dropOffTime: String!
    $paymentMethod: String
    $paymentStatus: String
    $usedPrice: Float
  ) {
    createRentalDateExtensionRequest(
      rentalId: $rentalId
      dropOffDate: $dropOffDate
      dropOffTime: $dropOffTime
      paymentMethod: $paymentMethod
      paymentStatus: $paymentStatus
      usedPrice: $usedPrice
    ) {
      success
      message
    }
  }
`;

const UPDATE_RENTAL_DATE_EXTENSION_REQUEST = gql`
  mutation UpdateRentalDateExtensionRequest(
    $rentalExtensionId: ID!
    $dropOffDate: String
    $dropOffTime: String
    $paymentMethod: String
    $paymentStatus: String
    $usedPrice: Float
  ) {
    updateRentalDateExtensionRequest(
      rentalExtensionId: $rentalExtensionId
      dropOffDate: $dropOffDate
      dropOffTime: $dropOffTime
      paymentMethod: $paymentMethod
      paymentStatus: $paymentStatus
      usedPrice: $usedPrice
    ) {
      success
      message
    }
  }
`;

const REJECT_RENTAL_DATE_EXTENSION_REQUEST = gql`
  mutation RejectRentalDateExtensionRequest($rentalExtensionId: ID!) {
    rejectRentalDateExtensionRequest(rentalExtensionId: $rentalExtensionId) {
      success
      message
    }
  }
`;

const RESEND_RENTAL_EXTENSION_INTEGRATION = gql`
  mutation ResendRentalExtensionIntegration($rentalExtensionId: ID!) {
    resendRentalExtensionIntegration(rentalExtensionId: $rentalExtensionId) {
      success
      message
    }
  }
`;

const RENTAL_EXTENSION_REQUEST_PRICE = gql`
  query RentalExtensionRequestPrice(
    $rentalId: ID!
    $dropOffDate: String!
    $dropOffTime: String!
    $usedPrice: Float
  ) {
    rentalExtensionRequestPrice(
      rentalId: $rentalId
      dropOffDate: $dropOffDate
      dropOffTime: $dropOffTime
      usedPrice: $usedPrice
    ) {
      extensionDays
      totalRemainingPrice
      originalPricePerDay
      pricePerDay
      packagePricePerDay
      discount
    }
  }
`;

interface ExtensionRequest {
  id: string;
  requestNo?: string;
  dropOffDate: string;
  dropOffTime: string;
  extensionDays: number;
  totalRemainingPrice: number;
  originalPricePerDay?: number;
  pricePerDay?: number;
  packagePricePerDay?: number;
  discount?: number;
  paymentMethod?: string;
  paymentBrand?: string;
  status: string;
  statusLocalized?: string;
  paymentStatus?: string;
  isPaid?: boolean;
  canSendExtensionToAlly?: boolean;
  refundable?: boolean;
  createdAt?: string;
  months?: number;
}

interface BookingDetails {
  id: string;
  status: string;
  paymentMethod: string;
  dropOffDate: string;
  dropOffTime: string;
  installments?: any[];
  mergedInstallments?: any[];
  lastConfirmedExtensionRequest?: ExtensionRequest;
}

interface ExtensionRequestsTableProps {
  extensionRequests: ExtensionRequest[];
  rentalId: string;
  refetchBooking: () => void;
  bookingDetails: BookingDetails;
}

// Month options for extension (matching old dashboard)
const MONTHS_EXTENSION = [
  { value: 1, label: 'bookings.extensionRequests.oneMonth' },
  { value: 2, label: 'bookings.extensionRequests.twoMonths' },
  { value: 3, label: 'bookings.extensionRequests.threeMonths' },
  { value: 6, label: 'bookings.extensionRequests.sixMonths' },
  { value: 12, label: 'bookings.extensionRequests.twelveMonths' },
];

export default function ExtensionRequestsTable({
  extensionRequests,
  rentalId,
  refetchBooking,
  bookingDetails,
}: ExtensionRequestsTableProps) {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  const dateLocale = isRTL ? ar : enUS;

  const [requests, setRequests] = useState<ExtensionRequest[]>(extensionRequests || []);
  const [showAddBtn, setShowAddBtn] = useState(true);
  const [currentRequestId, setCurrentRequestId] = useState<string | null>(null);
  const [paymentMethod, setPaymentMethod] = useState<string>(bookingDetails?.paymentMethod || '');
  const [paymentStatus, setPaymentStatus] = useState<string>('not_paid');

  // Mutations - matching old dashboard
  const [confirmExtensionRequest] = useMutation(CONFIRM_RENTAL_DATE_EXTENSION_REQUEST);
  const [createExtensionRequest] = useMutation(CREATE_RENTAL_DATE_EXTENSION_REQUEST);
  const [updateExtensionRequest] = useMutation(UPDATE_RENTAL_DATE_EXTENSION_REQUEST);
  const [rejectExtensionRequest] = useMutation(REJECT_RENTAL_DATE_EXTENSION_REQUEST);
  const [resendToAlly] = useMutation(RESEND_RENTAL_EXTENSION_INTEGRATION);

  useEffect(() => {
    setRequests(extensionRequests || []);
  }, [extensionRequests]);

  const handleConfirmExtension = (id: string) => {
    confirmExtensionRequest({
      variables: { rentalExtensionId: id },
    })
      .then(() => {
        refetchBooking();
        setShowAddBtn(true);
      })
      .catch((error) => {
        console.error('Error confirming extension:', error);
      });
  };

  const handleRejectExtension = (id: string) => {
    rejectExtensionRequest({
      variables: { rentalExtensionId: id },
    })
      .then(() => {
        refetchBooking();
      })
      .catch((error) => {
        console.error('Error rejecting extension:', error);
      });
  };

  const handleResendToAlly = (id: string) => {
    resendToAlly({
      variables: { rentalExtensionId: id },
    })
      .then(() => {
        refetchBooking();
      })
      .catch((error) => {
        console.error('Error resending to ally:', error);
      });
  };

  const handleMonthChange = (request: ExtensionRequest, months: number) => {
    const updatedRequests = requests.map((req) =>
      req.id === request.id ? { ...req, months } : req
    );
    setRequests(updatedRequests);
    setCurrentRequestId(request.id);
  };

  const addNewRequest = () => {
    const newRequest: ExtensionRequest = {
      id: `temp_${Date.now()}`,
      dropOffDate: '',
      dropOffTime: '',
      extensionDays: 0,
      totalRemainingPrice: 0,
      status: 'pending',
      months: 1,
    };
    setRequests([newRequest, ...requests]);
    setShowAddBtn(false);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '-';
    try {
      return format(new Date(dateString), 'dd/MM/yyyy', { locale: dateLocale });
    } catch {
      return '-';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(isRTL ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR',
    }).format(amount || 0);
  };

  const getPaymentMethodOptions = () => [
    { value: 'CASH', label: t('bookings.extensionRequests.cash') },
    { value: 'ONLINE', label: t('bookings.extensionRequests.online') },
    { value: 'WALLET', label: t('bookings.extensionRequests.wallet') },
  ];

  const getPaymentStatusOptions = () => [
    { value: 'paid', label: t('bookings.extensionRequests.paid') },
    { value: 'not_paid', label: t('bookings.extensionRequests.notPaid') },
  ];

  const shouldShowPricingColumns =
    !bookingDetails?.installments?.length && !bookingDetails?.mergedInstallments?.length;

  if (!requests?.length && showAddBtn) {
    return (
      <Box>
        <Typography variant="h6" sx={{ mb: 2 }}>
          {t('bookings.extensionRequests.title')}
        </Typography>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Alert severity="info">{t('bookings.extensionRequests.noExtensionRequestsFound')}</Alert>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={addNewRequest}
            sx={{ ml: 2 }}
          >
            {t('bookings.extensionRequests.addRequest')}
          </Button>
        </Box>
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2 }}>
        {t('bookings.extensionRequests.title')}
      </Typography>

      {showAddBtn && (
        <Box sx={{ mb: 2 }}>
          <Button variant="contained" startIcon={<AddIcon />} onClick={addNewRequest}>
            {t('bookings.extensionRequests.addRequest')}
          </Button>
        </Box>
      )}

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>{t('bookings.extensionRequests.requestNo')}</TableCell>
              <TableCell sx={{ minWidth: 150 }}>
                {t('bookings.extensionRequests.newReturnTime')}
              </TableCell>
              <TableCell>{t('bookings.extensionRequests.extensionDays')}</TableCell>
              <TableCell sx={{ minWidth: 100 }}>
                {t('bookings.extensionRequests.dueValue')}
              </TableCell>

              {shouldShowPricingColumns && (
                <>
                  <TableCell sx={{ minWidth: 100 }}>
                    {t('bookings.extensionRequests.originalPricePerDay')}
                  </TableCell>
                  <TableCell sx={{ minWidth: 100 }}>
                    {t('bookings.extensionRequests.pricePerDay')}
                  </TableCell>
                  <TableCell sx={{ minWidth: 100 }}>
                    {t('bookings.extensionRequests.recommendedPricePerDay')}
                  </TableCell>
                  <TableCell sx={{ minWidth: 100 }}>
                    {t('bookings.extensionRequests.discountRate')}
                  </TableCell>
                  <TableCell sx={{ minWidth: 100 }}>
                    {t('bookings.extensionRequests.paymentMethod')}
                  </TableCell>
                  <TableCell sx={{ minWidth: 100 }}>
                    {t('bookings.extensionRequests.paymentBrand')}
                  </TableCell>
                </>
              )}

              <TableCell>{t('bookings.extensionRequests.requestStatus')}</TableCell>
              <TableCell sx={{ minWidth: 140 }}>
                {t('bookings.extensionRequests.paymentStatus')}
              </TableCell>
              <TableCell>{t('bookings.extensionRequests.actions')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {requests.map((request, index) => (
              <TableRow key={request.id}>
                <TableCell>{request.requestNo || '-'}</TableCell>

                {/* New Return Time - Month Selection for installment bookings */}
                <TableCell>
                  {bookingDetails?.installments?.length ? (
                    <FormControl size="small" sx={{ minWidth: 120 }}>
                      <Select
                        value={String(request.months || 1)}
                        onChange={(e: SelectChangeEvent) =>
                          handleMonthChange(request, parseInt(e.target.value as string))
                        }
                        disabled={request.status !== 'pending'}
                      >
                        {MONTHS_EXTENSION.map((option) => (
                          <MenuItem key={option.value} value={String(option.value)}>
                            {t(option.label)}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  ) : (
                    formatDate(request.dropOffDate)
                  )}
                </TableCell>

                <TableCell>
                  {request.extensionDays
                    ? `${request.extensionDays} ${t('bookings.extensionRequests.days')}`
                    : '-'}
                </TableCell>

                <TableCell>{formatCurrency(request.totalRemainingPrice)}</TableCell>

                {shouldShowPricingColumns && (
                  <>
                    <TableCell>{formatCurrency(request.originalPricePerDay || 0)}</TableCell>
                    <TableCell>{formatCurrency(request.pricePerDay || 0)}</TableCell>
                    <TableCell>{formatCurrency(request.packagePricePerDay || 0)}</TableCell>
                    <TableCell>{request.discount || '-'}</TableCell>

                    {/* Payment Method */}
                    <TableCell>
                      {(request.status === 'pending' || !request.status) &&
                      !request.requestNo &&
                      bookingDetails?.paymentMethod !== 'CASH' ? (
                        <FormControl size="small" sx={{ minWidth: 120 }}>
                          <Select
                            value={paymentMethod}
                            onChange={(e: SelectChangeEvent) => {
                              setPaymentMethod(e.target.value);
                              setCurrentRequestId(request.id);
                            }}
                          >
                            {getPaymentMethodOptions().map((option) => (
                              <MenuItem key={option.value} value={option.value}>
                                {option.label}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      ) : (
                        t(
                          `bookings.extensionRequests.${
                            request.paymentMethod?.toLowerCase() ||
                            bookingDetails?.paymentMethod?.toLowerCase()
                          }`
                        )
                      )}
                    </TableCell>

                    {/* Payment Brand */}
                    <TableCell>{request.paymentBrand || '-'}</TableCell>
                  </>
                )}

                {/* Request Status */}
                <TableCell>
                  <Chip
                    label={request.statusLocalized || t('bookings.extensionRequests.pending')}
                    size="small"
                    color={
                      request.status === 'confirmed'
                        ? 'success'
                        : request.status === 'rejected'
                        ? 'error'
                        : 'warning'
                    }
                  />
                </TableCell>

                {/* Payment Status */}
                <TableCell>
                  {(request.status === 'pending' || !request.status) && paymentMethod === 'CASH' ? (
                    <FormControl size="small" sx={{ minWidth: 120 }}>
                      <Select
                        value={paymentStatus}
                        onChange={(e: SelectChangeEvent) => {
                          setPaymentStatus(e.target.value);
                          setCurrentRequestId(request.id);
                        }}
                      >
                        {getPaymentStatusOptions().map((option) => (
                          <MenuItem key={option.value} value={option.value}>
                            {option.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  ) : (
                    <Chip
                      label={
                        request.isPaid
                          ? t('bookings.extensionRequests.paid')
                          : t('bookings.extensionRequests.notPaid')
                      }
                      size="small"
                      color={request.isPaid ? 'success' : 'error'}
                    />
                  )}
                </TableCell>

                {/* Actions */}
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    {/* Refund Action */}
                    {request.refundable && (
                      <Tooltip title={t('bookings.extensionRequests.refund')}>
                        <IconButton size="small" color="secondary">
                          <RefreshIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}

                    {/* Edit Action */}
                    {request.requestNo && request.id === currentRequestId && (
                      <Tooltip title={t('bookings.extensionRequests.edit')}>
                        <IconButton size="small" color="primary">
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}

                    {/* Confirm Action */}
                    {request.requestNo &&
                      request.status === 'pending' &&
                      bookingDetails?.status === 'car_received' && (
                        <Tooltip title={t('bookings.extensionRequests.confirm')}>
                          <IconButton
                            size="small"
                            color="success"
                            onClick={() => handleConfirmExtension(request.id)}
                          >
                            <CheckIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}

                    {/* Reject Action */}
                    {request.requestNo &&
                      request.status === 'pending' &&
                      bookingDetails?.status === 'car_received' && (
                        <Tooltip title={t('bookings.extensionRequests.reject')}>
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleRejectExtension(request.id)}
                          >
                            <CancelIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}

                    {/* Resend to Ally Action */}
                    {request.canSendExtensionToAlly && (
                      <Tooltip title={t('bookings.extensionRequests.resendToAlly')}>
                        <IconButton
                          size="small"
                          color="info"
                          onClick={() => handleResendToAlly(request.id)}
                        >
                          <SendIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
}
