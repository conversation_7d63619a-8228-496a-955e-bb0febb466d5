'use client';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme, Box, Typography, Stack } from '@mui/material';
import { Pie<PERSON>hart, Pie, Cell, ResponsiveContainer, Tooltip } from 'recharts';

interface RentalStatusPieChartProps {
  labels: string[];
  data: number[];
  colors: string[];
}

export default function RentalStatusPieChart({ labels, data, colors }: RentalStatusPieChartProps) {
  // Define custom colors that match the image
  const defaultColors = [
    '#F44336', // Red for Pending
    '#FF9800', // Orange for Confirmed
    '#4CAF50', // Green for Car received
    '#FFC107', // Amber for Invoiced
    '#2196F3', // Blue for Closed
    '#9E9E9E', // Grey for Cancelled
  ];

  // Use provided colors or default to our custom colors
  const chartColors = colors && colors.length > 0 ? colors : defaultColors;
  const { t } = useTranslation();
  const theme = useTheme();
  const [chartData, setChartData] = useState<any[]>([]);

  useEffect(() => {
    if (labels.length > 0 && data.length > 0) {
      // Calculate total for percentages
      const total = data.reduce((sum, value) => sum + value, 0);

      const processedData = labels.map((label, index) => ({
        name: label,
        value: data[index],
        percentage: ((data[index] / total) * 100).toFixed(0),
      }));
      setChartData(processedData);
    }
  }, [labels, data]);

  if (chartData.length === 0) {
    return <div>No data available</div>;
  }

  // Custom tooltip
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <Box
          sx={{
            backgroundColor: theme.palette.background.paper,
            border: `1px solid ${theme.palette.divider}`,
            p: 1.5,
            borderRadius: 1,
            boxShadow: theme.shadows[2],
          }}
        >
          <Typography variant="body2" color="text.primary">
            <strong>{payload[0].name}</strong>
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {t('Count')}: {payload[0].value}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {t('Percentage')}:{' '}
            {((payload[0].value / data.reduce((a, b) => a + b, 0)) * 100).toFixed(1)}%
          </Typography>
        </Box>
      );
    }
    return null;
  };

  // Create a custom legend component to match the image with stretched color bars
  const CustomLegend = () => {
    return (
      <Stack
        direction="column"
        spacing={1.5}
        justifyContent="center"
        alignItems="center"
        sx={{ mt: 3, width: '100%', px: 2 }}
      >
        {chartData.map((entry, index) => (
          <Box
            key={`legend-${index}`}
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'flex-start',
              position: 'relative',
              mb: 0.5,
              gap: 1,
            }}
          >
            <Box
              sx={{
                width: '8% !important',
                height: 6,
                borderRadius: 10,
                backgroundColor: chartColors[index % chartColors.length],
                opacity: 0.9,
              }}
            />
            <Typography
              variant="body2"
              sx={{
                color:
                  theme.palette.mode === 'dark'
                    ? '#ffffff'
                    : chartColors[index % chartColors.length],
                fontWeight: 'bold',
                // position: 'absolute',
                right: 'auto',
                left: '5%',
                textAlign: 'left',
              }}
            >
              {entry.name}
            </Typography>
          </Box>
        ))}
      </Stack>
    );
  };

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        '& text': {
          // Ensure text is readable in both light and dark modes
          fill: theme.palette.mode === 'dark' ? '#ffffff' : '#666666',
        },
      }}
      role="img"
      aria-label={t('Pie chart showing rental distribution by status')}
    >
      <ResponsiveContainer width="100%" height="70%">
        <PieChart margin={{ top: 10, right: 10, bottom: 10, left: 10 }}>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            labelLine={false}
            innerRadius={5}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
            nameKey="name"
            label={({ cx, cy, midAngle, innerRadius, outerRadius, index }) => {
              const RADIAN = Math.PI / 180;
              // Position the labels further out from the pie
              const radius = 40 + innerRadius + (outerRadius - innerRadius);
              const x1 = cx + radius * Math.cos(-midAngle * RADIAN);
              const y1 = cy + radius * Math.sin(-midAngle * RADIAN);

              return (
                <text
                  x={x1}
                  y={y1}
                  fill={theme.palette.mode === 'dark' ? '#ffffff' : '#666666'} // Ensure good contrast in both modes
                  textAnchor={x1 > cx ? 'start' : 'end'}
                  dominantBaseline="central"
                  style={{
                    fontSize: '14px',
                    fontWeight: 'normal',
                    opacity: 0.8,
                  }}
                >
                  {`${chartData[index].percentage}%`}
                </text>
              );
            }}
          >
            {chartData.map((_, index) => (
              <Cell key={`cell-${index}`} fill={chartColors[index % chartColors.length]} />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
        </PieChart>
      </ResponsiveContainer>
      <CustomLegend />
    </Box>
  );
}
