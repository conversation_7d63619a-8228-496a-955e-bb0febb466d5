'use client';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import BannerList from './bannerList';
import { getAuthToken } from '../utils/cookies';

export default function Banners() {
  const router = useRouter();

  useEffect(() => {
    // Check for token in cookies
    const token = getAuthToken();
    if (!token) {
      router.replace('/login');
    }
  }, [router]);

  return (
    <div>
      <BannerList />
    </div>
  );
}
