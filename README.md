This is a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/basic-features/font-optimization) to automatically optimize and load Inter, a custom Google Font.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js/) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/deployment) for more details.

## Environment Setup

1. Copy `.env.example` to create your local environment file:

```bash
cp .env.example .env.local
```

2. Update the values in `.env.local` according to your development environment.

3. Available environment variables:
- `NEXT_PUBLIC_API_URL`: GraphQL API endpoint
- `NEXT_PUBLIC_ENV`: Current environment (development/staging/production)

Note: Never commit `.env.local` to version control as it may contain sensitive information.

## Docker Setup

This project can be run using Docker for consistent development and deployment environments.

For detailed Docker instructions, please see the [Docker documentation](DOCKER.md).

### Quick Start

1. Create your environment file:
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your values
   ```

2. Build and start the container:
   ```bash
   docker-compose up -d
   ```

3. Access the application at http://localhost:3000

4. Stop the container:
   ```bash
   docker-compose down
   ```

The Docker setup is configured to use environment variables exclusively from your `.env.local` file.
