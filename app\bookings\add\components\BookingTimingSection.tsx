'use client';
import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import dayjs, { Dayjs } from 'dayjs';

const MONTHS_OF_RENT = [
  { value: '1', label: '1 month' },
  { value: '3', label: '3 months' },
  { value: '6', label: '6 months' },
  { value: '12', label: '12 months' },
];

interface BookingTimingSectionProps {
  pickUpDate: Dayjs;
  dropOffDate: Dayjs;
  bookingType: string;
  months: string;
  monthTime: string;
  plan?: any;
  onPickUpDateChange: (date: Dayjs | null) => void;
  onDropOffDateChange: (date: Dayjs | null) => void;
  onMonthsChange: (months: string) => void;
  onMonthTimeChange: (time: string) => void;
  onChanged: () => void;
}

export default function BookingTimingSection({
  pickUpDate,
  dropOffDate,
  bookingType,
  months,
  monthTime,
  plan,
  onPickUpDateChange,
  onDropOffDateChange,
  onMonthsChange,
  onMonthTimeChange,
  onChanged,
}: BookingTimingSectionProps) {
  const { t } = useTranslation();

  const handlePickUpDateChange = (val: Dayjs | null) => {
    if (val) {
      onChanged();
      onPickUpDateChange(val);
    }
  };

  const handleDropOffDateChange = (val: Dayjs | null) => {
    if (val) {
      onChanged();
      onDropOffDateChange(val);
    }
  };

  const handleMonthsChange = (selectedMonths: string) => {
    onMonthsChange(selectedMonths);
    onDropOffDateChange(pickUpDate.add(parseInt(selectedMonths) * 30, 'day'));
    onChanged();
  };

  return (
    <Box sx={{ mb: 3 }}>
      <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
        {t('rental.bookingTiming')}
      </Typography>

      <Grid container spacing={2}>
        <Grid item xs={12} md={6}>
          <DateTimePicker
            label={t('rental.pickupDateTime')}
            value={pickUpDate}
            onChange={handlePickUpDateChange}
            slotProps={{
              textField: {
                fullWidth: true,
                required: true,
              },
            }}
          />
        </Grid>

        {bookingType !== 'monthly' && bookingType !== 'rent-to-own' && (
          <Grid item xs={12} md={6}>
            <DateTimePicker
              label={t('rental.dropoffDateTime')}
              value={dropOffDate}
              onChange={handleDropOffDateChange}
              slotProps={{
                textField: {
                  fullWidth: true,
                  required: true,
                },
              }}
            />
          </Grid>
        )}

        {/* Monthly booking specific controls */}
        {bookingType === 'monthly' && (
          <>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>{t('months.count')}</InputLabel>
                <Select
                  value={months}
                  label={t('months.count')}
                  onChange={(e) => handleMonthsChange(e.target.value)}
                >
                  {MONTHS_OF_RENT.map((month) => (
                    <MenuItem key={month.value} value={month.value}>
                      {t(month.label)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="time"
                label={t('rental.dropoffTime')}
                value={monthTime || dropOffDate.format('HH:mm')}
                onChange={(e) => onMonthTimeChange(e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
          </>
        )}
      </Grid>
    </Box>
  );
}
