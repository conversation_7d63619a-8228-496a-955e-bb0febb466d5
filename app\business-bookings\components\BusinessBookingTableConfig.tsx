import React from 'react';
import { <PERSON>, Chip, Box, Typography } from '@mui/material';
import { format } from 'date-fns';
import { BusinessBooking, BusinessBookingTableColumn } from '../../models/businessBookings.model';

// Status color mapping function
const getStatusColor = (status: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
  switch (status?.toLowerCase()) {
    case 'confirmed':
      return 'success';
    case 'car_received':
      return 'info';
    case 'invoiced':
      return 'primary';
    case 'cancelled':
      return 'error';
    case 'closed':
      return 'secondary';
    case 'pending':
      return 'warning';
    default:
      return 'default';
  }
};

// Format date and time
const formatDateTime = (dateString: string, locale: string = 'en') => {
  if (!dateString) return '--';
  
  try {
    const date = new Date(dateString);
    const dateFormat = locale === 'ar' ? 'dd/MM/yyyy' : 'MM/dd/yyyy';
    const timeFormat = 'HH:mm';
    
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, minWidth: 'max-content' }}>
        <Typography variant="body2" sx={{ fontSize: 'small' }}>
          {format(date, dateFormat)}
        </Typography>
        <Typography variant="body2" sx={{ fontSize: 'small', color: 'text.secondary' }}>
          {format(date, timeFormat)}
        </Typography>
      </Box>
    );
  } catch (error) {
    return '--';
  }
};

// Business Booking Table Columns Configuration
export const businessBookingTableColumns = (t: (key: string) => string, locale: string = 'en', isAlly: boolean = false): BusinessBookingTableColumn[] => [
  {
    id: 'id',
    label: t('bookingId.placeholder'),
    minWidth: 100,
    sortable: true,
    format: (value: string, row?: BusinessBooking) => (
      <Link
        href={`/business-bookings/${row?.id}`}
        sx={{ 
          textDecoration: 'none',
          color: 'primary.main',
          fontWeight: 500,
          '&:hover': {
            textDecoration: 'underline'
          }
        }}
      >
        {value}
      </Link>
    ),
  },
  {
    id: 'bookingNo',
    label: t('bookingNo.placeholder'),
    minWidth: 120,
    format: (value: string, row?: BusinessBooking) => (
      <Link
        href={`/business-bookings/${row?.id}`}
        sx={{ 
          textDecoration: 'none',
          color: 'primary.main',
          fontWeight: 500,
          '&:hover': {
            textDecoration: 'underline'
          }
        }}
      >
        {value}
      </Link>
    ),
  },
  {
    id: 'customerName',
    label: t('bookings.list.customerName'),
    minWidth: 150,
    format: (value: string, row?: BusinessBooking) => (
      <Link
        href={`/customers/${row?.userId}`}
        sx={{ 
          textDecoration: 'none',
          color: 'primary.main',
          fontWeight: 500,
          '&:hover': {
            textDecoration: 'underline'
          }
        }}
      >
        {value}
      </Link>
    ),
  },
  {
    id: 'allyCompanyName',
    label: t('bookings.list.allyName'),
    minWidth: 150,
    format: (value: string, row?: BusinessBooking) => (
      row?.acceptedOffer?.allyCompanyId ? (
        <Link
          href={`/companies/${row.acceptedOffer.allyCompanyId}`}
          sx={{ 
            textDecoration: 'none',
            color: 'primary.main',
            fontWeight: 500,
            '&:hover': {
              textDecoration: 'underline'
            }
          }}
        >
          {row.acceptedOffer.allyCompanyName}
        </Link>
      ) : '--'
    ),
  },
  {
    id: 'insuranceName',
    label: t('rental.insuranceType'),
    minWidth: 120,
  },
  {
    id: 'pickUpCityName',
    label: t('bookings.list.pickupCity'),
    minWidth: 120,
  },
  {
    id: 'carInfo',
    label: t('car'),
    minWidth: 200,
    format: (value: any, row?: BusinessBooking) => {
      const makeName = locale === 'ar' ? row?.arMakeName : row?.enMakeName;
      const modelName = locale === 'ar' ? row?.arModelName : row?.enModelName;
      return `${makeName || row?.makeName || ''} - ${modelName || row?.modelName || ''} - ${row?.year || ''}`;
    },
  },
  {
    id: 'numberOfMonths',
    label: t('Duration in months'),
    minWidth: 120,
    align: 'center',
    sortable: true,
  },
  {
    id: 'numberOfCars',
    label: t('numberOfCars'),
    minWidth: 100,
    align: 'center',
  },
  {
    id: 'pickUpDatetime',
    label: t('bookings.list.pickup'),
    minWidth: 150,
    sortable: true,
    format: (value: string) => formatDateTime(value, locale),
  },
  {
    id: 'dropOffDatetime',
    label: t('bookings.list.delivery'),
    minWidth: 150,
    sortable: true,
    format: (value: string) => formatDateTime(value, locale),
  },
  {
    id: 'pricePerMonth',
    label: t('bookings.list.header.billingAmountMonth'),
    minWidth: 120,
    align: 'right',
    sortable: true,
    format: (value: number) => value ? `${value.toLocaleString()} ${t('SAR')}` : '--',
  },
  {
    id: 'totalBookingPrice',
    label: t('bookings.list.header.billingAmount'),
    minWidth: 120,
    align: 'right',
    sortable: true,
    format: (value: number) => value ? `${value.toLocaleString()} ${t('SAR')}` : '--',
  },
  {
    id: 'paidAmount',
    label: t('bookings.list.paidAmount'),
    minWidth: 120,
    align: 'right',
    format: (value: any, row?: BusinessBooking) => {
      const paidAmount = (row?.acceptedOffer?.carInsuranceFull || row?.acceptedOffer?.carInsuranceStandard || 0) * (row?.numberOfMonths || 0);
      return paidAmount ? `${paidAmount.toLocaleString()} ${t('SAR')}` : '--';
    },
  },
  {
    id: 'status',
    label: t('bookings.list.bookingStatus'),
    minWidth: 150,
    format: (value: string, row?: BusinessBooking) => (
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Chip
            label={t(value?.toUpperCase() || 'UNKNOWN')}
            color={getStatusColor(value)}
            size="small"
            variant="filled"
            sx={{ 
              fontWeight: 500,
              fontSize: '0.75rem',
              height: 24
            }}
          />
        </Box>
        {row?.statusLocalized && (
          <Typography variant="caption" color="text.secondary">
            {row.statusLocalized}
          </Typography>
        )}
      </Box>
    ),
  },
  {
    id: 'createdAt',
    label: t('createdAt'),
    minWidth: 150,
    sortable: true,
    format: (value: string) => formatDateTime(value, locale),
  },
  // Only show assignment column if not ally
  ...(isAlly ? [] : [{
    id: 'assignment',
    label: t('Assign'),
    minWidth: 120,
    format: (value: any, row?: BusinessBooking) => (
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
        {/* Assignment functionality will be implemented in the table component */}
        <Typography variant="caption" color="text.secondary">
          {row?.assignedTo ? t('Assigned') : t('Unassigned')}
        </Typography>
      </Box>
    ),
  }]),
  {
    id: 'actions',
    label: t('common.actions'),
    minWidth: 100,
    align: 'center',
    format: () => (
      <Box sx={{ display: 'flex', gap: 0.5 }}>
        {/* Actions will be implemented in the table component */}
      </Box>
    ),
  },
];

// Default sort configuration
export const defaultBusinessBookingSortConfig = {
  field: 'pick_up_datetime',
  direction: 'desc' as const,
};

// Status filter options based on old dashboard
export const businessBookingStatusOptions = [
  'all',
  'confirmed',
  'car_received', 
  'invoiced',
  'cancelled',
  'closed',
] as const;
