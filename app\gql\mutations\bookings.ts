import { gql } from '@apollo/client';

// Mutation to create a new booking
export const CREATE_BOOKING_MUTATION = gql`
  mutation CreateBooking(
    $couponId: ID
    $carId: ID!
    $deliveryPrice: Float
    $handoverPrice: Float
    $dropOffBranchId: ID
    $dropOffCityId: ID
    $dropOffDate: String
    $dropOffTime: String
    $insuranceId: ID
    $pickUpCityId: ID!
    $pickUpDate: String!
    $pickUpTime: String!
    $userId: ID!
    $deliverAddress: String
    $deliverLat: Float
    $deliverLng: Float
    $deliverType: String
    $paymentMethod: PaymentMethod!
    $suggestedPrice: Float
    $allyExtraServices: [ID!]
    $branchExtraServices: [ID!]
    $handoverLat: Float
    $handoverLng: Float
    $handoverAddress: String
    $isUnlimited: Boolean
    $withInstallment: Boolean
    $ownCarPlanId: ID
    $loyaltyType: LoyaltyType
  ) {
    createRental(
      couponId: $couponId
      deliveryPrice: $deliveryPrice
      handoverPrice: $handoverPrice
      carId: $carId
      deliverAddress: $deliverAddress
      deliverLat: $deliverLat
      deliverLng: $deliverLng
      deliverType: $deliverType
      dropOffBranchId: $dropOffBranchId
      dropOffCityId: $dropOffCityId
      dropOffDate: $dropOffDate
      dropOffTime: $dropOffTime
      insuranceId: $insuranceId
      paymentMethod: $paymentMethod
      pickUpCityId: $pickUpCityId
      pickUpDate: $pickUpDate
      pickUpTime: $pickUpTime
      userId: $userId
      suggestedPrice: $suggestedPrice
      allyExtraServices: $allyExtraServices
      branchExtraServices: $branchExtraServices
      handoverLat: $handoverLat
      handoverLng: $handoverLng
      handoverAddress: $handoverAddress
      isUnlimited: $isUnlimited
      ownCarPlanId: $ownCarPlanId
      withInstallment: $withInstallment
      loyaltyType: $loyaltyType
    ) {
      errors
      rental {
        allyCompanyId
        arAllyName
        arDropOffCityName
        arMakeName
        arModelName
        arPickUpCityName
        arVersionName
        branchId
        carId
        carImage
        dropOffCityId
        dropOffCityName
        dropOffDate
        dropOffTime
        enAllyName
        enDropOffCityName
        enMakeName
        enModelName
        enPickUpCityName
        enVersionName
        id
        makeName
        modelName
        numberOfDays
        paymentMethod
        pickUpCityId
        pickUpCityName
        pickUpDate
        pickUpTime
        priceBeforeInsurance
        pricePerDay
        status
        subStatus
        taxValue
        totalBookingPrice
        totalInsurancePrice
        userId
        valueAddedTaxPercentage
        versionName
        year
      }
      status
    }
  }
`;

// Mutation to edit an existing booking
export const EDIT_BOOKING_MUTATION = gql`
  mutation EditBooking(
    $couponId: ID
    $carId: ID!
    $handoverLat: Float
    $handoverLng: Float
    $handoverPrice: Float
    $dropOffBranchId: ID!
    $dropOffCityId: ID!
    $dropOffDate: String!
    $dropOffTime: String!
    $insuranceId: ID!
    $pickUpCityId: ID!
    $pickUpDate: String!
    $pickUpTime: String!
    $deliverAddress: String
    $deliverLat: Float
    $deliverLng: Float
    $deliverType: String
    $paymentMethod: PaymentMethod!
    $suggestedPrice: Float
    $rentalId: ID!
    $allyExtraServices: [ID!]
    $branchExtraServices: [ID!]
    $notes: String
    $handoverAddress: String
    $isUnlimited: Boolean
    $deliveryPrice: Float
  ) {
    editRental(
      couponId: $couponId
      carId: $carId
      deliverType: $deliverType
      deliverAddress: $deliverAddress
      deliverLat: $deliverLat
      deliverLng: $deliverLng
      dropOffBranchId: $dropOffBranchId
      dropOffCityId: $dropOffCityId
      dropOffDate: $dropOffDate
      dropOffTime: $dropOffTime
      insuranceId: $insuranceId
      paymentMethod: $paymentMethod
      pickUpCityId: $pickUpCityId
      pickUpDate: $pickUpDate
      pickUpTime: $pickUpTime
      suggestedPrice: $suggestedPrice
      rentalId: $rentalId
      allyExtraServices: $allyExtraServices
      branchExtraServices: $branchExtraServices
      notes: $notes
      handoverLat: $handoverLat
      handoverLng: $handoverLng
      handoverPrice: $handoverPrice
      handoverAddress: $handoverAddress
      isUnlimited: $isUnlimited
      deliveryPrice: $deliveryPrice
    ) {
      errors
      rental {
        allyCompanyId
        arAllyName
        arDropOffCityName
        arMakeName
        arModelName
        arPickUpCityName
        arVersionName
        branchId
        carId
        carImage
        dropOffCityId
        dropOffCityName
        dropOffDate
        dropOffTime
        enAllyName
        enDropOffCityName
        enMakeName
        enModelName
        enPickUpCityName
        enVersionName
        id
        makeName
        modelName
        numberOfDays
        paymentMethod
        pickUpCityId
        pickUpCityName
        pickUpDate
        pickUpTime
        priceBeforeInsurance
        pricePerDay
        status
        subStatus
        taxValue
        totalBookingPrice
        totalInsurancePrice
        userId
        valueAddedTaxPercentage
        versionName
        year
      }
      status
    }
  }
`;

// Query to get available cars
export const GET_AVAILABLE_CARS_QUERY = gql`
  query GetAvailableCars(
    $pickUpDate: String!
    $dropOffDate: String!
    $pickUpCityId: ID!
    $dropOffCityId: ID!
  ) {
    availableCars(
      pickUpDate: $pickUpDate
      dropOffDate: $dropOffDate
      pickUpCityId: $pickUpCityId
      dropOffCityId: $dropOffCityId
    ) {
      id
      arMakeName
      enMakeName
      arModelName
      enModelName
      arVersionName
      enVersionName
      year
      pricePerDay
      carImage
    }
  }
`;

// Query to get cities
export const GET_CITIES_QUERY = gql`
  query GetCities {
    cities {
      id
      name
      arName
      enName
    }
  }
`;

// Query to get branches by city
export const GET_BRANCHES_BY_CITY_QUERY = gql`
  query GetBranchesByCity($cityId: ID!) {
    branchesByCity(cityId: $cityId) {
      id
      name
      arName
      enName
      cityId
    }
  }
`;

// Query to search customers
export const SEARCH_CUSTOMERS_QUERY = gql`
  query SearchCustomers($search: String!) {
    searchCustomers(search: $search) {
      id
      name
      mobile
      email
    }
  }
`;

// Mutation to change booking duration
export const CHANGE_BOOKING_DURATION = gql`
  mutation ChangeBookingDuration(
    $bookingId: ID!
    $newStartDate: String!
    $newEndDate: String!
    $newTotalPrice: Float!
  ) {
    changeBookingDuration(
      bookingId: $bookingId
      newStartDate: $newStartDate
      newEndDate: $newEndDate
      newTotalPrice: $newTotalPrice
    ) {
      success
      message
      booking {
        id
        pickUpDate
        dropOffDate
        numberOfDays
        totalBookingPrice
      }
    }
  }
`;

// Mutation to assign a booking to a customer care user
export const ASSIGN_BOOKING_MUTATION = gql`
  mutation AssignRentalTo($rentalId: ID!, $userId: ID!) {
    assignRentalTo(rentalId: $rentalId, userId: $userId) {
      errors
      status
    }
  }
`;

// Mutation to assign a booking to current user
export const ASSIGN_BOOKING_TO_ME_MUTATION = gql`
  mutation AssignRentalToMe($rentalId: ID!) {
    assignRentalToMe(rentalId: $rentalId) {
      errors
      status
    }
  }
`;

// Mutation to unassign a booking from a customer care user
export const UNASSIGN_BOOKING_MUTATION = gql`
  mutation UnassignBooking($bookingId: ID!, $reason: String) {
    unassignBooking(bookingId: $bookingId, reason: $reason) {
      success
      message
    }
  }
`;

// Mutation to add a note to a booking/rental
export const ADD_RENTAL_NOTE_MUTATION = gql`
  mutation CustomerUpdateRentalNote($rentalId: ID!, $note: String) {
    customerUpdateRentalNote(rentalId: $rentalId, note: $note) {
      errors
      rental {
        notes
      }
      status
    }
  }
`;

// Mutation to update suggested price
export const EDIT_SUGGESTED_PRICE_MUTATION = gql`
  mutation EditSuggestedPrice($rentalId: ID!, $suggestedPrice: Float) {
    editSuggestedPrice(rentalId: $rentalId, suggestedPrice: $suggestedPrice) {
      errors
      rental {
        suggestedPrice
      }
      status
    }
  }
`;

// Mutation to edit rental duration
export const EDIT_RENTAL_DURATION_MUTATION = gql`
  mutation EditRentalDuration(
    $dropOffDate: String!
    $dropOffTime: String!
    $pickUpDate: String!
    $pickUpTime: String!
    $rentalId: ID!
  ) {
    editRentalDuration(
      dropOffDate: $dropOffDate
      dropOffTime: $dropOffTime
      pickUpDate: $pickUpDate
      pickUpTime: $pickUpTime
      rentalId: $rentalId
    ) {
      errors
      rental {
        dropOffDate
        dropOffTime
        pickUpDate
        pickUpTime
        id
        year
      }
      status
    }
  }
`;

// Mutation to update extra services
export const UPDATE_RENTAL_EXTRA_SERVICES_MUTATION = gql`
  mutation CustomerUpdateRentalExtraServices(
    $allyExtraServices: [ID!]
    $branchExtraServices: [ID!]
    $rentalId: ID!
  ) {
    customerUpdateRentalExtraServices(
      allyExtraServices: $allyExtraServices
      branchExtraServices: $branchExtraServices
      rentalId: $rentalId
    ) {
      errors
      rental {
        id
      }
      status
    }
  }
`;

// Mutation to accept rental
export const ACCEPT_RENTAL_MUTATION = gql`
  mutation AcceptRent($rentalId: ID!) {
    acceptRental(rentalId: $rentalId) {
      errors
      status
    }
  }
`;

// Mutation to reject rental
export const REJECT_RENTAL_MUTATION = gql`
  mutation RejectRental($rentalId: ID!, $reason: String, $rejectedReasons: [ID!]) {
    rejectRental(rentalId: $rentalId, reason: $reason, rejectedReasons: $rejectedReasons) {
      errors
      status
    }
  }
`;

// Mutation to close rental
export const CLOSE_RENTAL_MUTATION = gql`
  mutation CloseRental($rentalId: ID!, $closeReasonId: Int!, $note: String) {
    closeRental(rentalId: $rentalId, closeReasonId: $closeReasonId, note: $note) {
      errors
      status
    }
  }
`;

// Mutation for car received
export const CAR_RECEIVED_MUTATION = gql`
  mutation CarReceived($rentalId: ID!) {
    carReceived(rentalId: $rentalId) {
      errors
      status
    }
  }
`;

// Mutation for ally receive car (invoice)
export const ALLY_RECEIVE_CAR_MUTATION = gql`
  mutation AllyReceiveCar($rentalId: ID!, $invoicePic: String, $newGrandTotal: Float) {
    allyReceiveCar(rentalId: $rentalId, invoicePic: $invoicePic, newGrandTotal: $newGrandTotal) {
      errors
      status
    }
  }
`;

// Mutation to reject extension request
export const REJECT_EXTENSION_REQUEST_MUTATION = gql`
  mutation RejectRentalDateExtensionRequest($rentalExtensionId: ID!) {
    rejectRentalDateExtensionRequest(rentalExtensionId: $rentalExtensionId) {
      errors
      status
    }
  }
`;

export const CREATE_RENTAL_DATE_EXTENSION_REQUEST_MUTATION = gql`
  mutation CreateRentalDateExtensionRequest(
    $rentalId: ID!
    $dropOffDate: String!
    $dropOffTime: String!
    $paymentMethod: PaymentMethod!
    $totalPrice: Float
    $isPaid: Boolean
    $usedPrice: Float
    $withInstallment: Boolean
  ) {
    createRentalDateExtensionRequest(
      rentalId: $rentalId
      dropOffDate: $dropOffDate
      dropOffTime: $dropOffTime
      paymentMethod: $paymentMethod
      isPaid: $isPaid
      totalPrice: $totalPrice
      usedPrice: $usedPrice
      withInstallment: $withInstallment
    ) {
      status
      errors
    }
  }
`;

export const UPDATE_RENTAL_DATE_EXTENSION_REQUEST_MUTATION = gql`
  mutation UpdateRentalDateExtensionRequest(
    $id: ID!
    $dropOffDate: String!
    $dropOffTime: String!
    $isPaid: Boolean
    $totalPrice: Float
    $usedPrice: Float
    $withInstallment: Boolean
  ) {
    updateRentalDateExtensionRequest(
      id: $id
      dropOffDate: $dropOffDate
      dropOffTime: $dropOffTime
      isPaid: $isPaid
      totalPrice: $totalPrice
      usedPrice: $usedPrice
      withInstallment: $withInstallment
    ) {
      status
      errors
    }
  }
`;

export const RESEND_RENTAL_EXTENSION_INTEGRATION_MUTATION = gql`
  mutation ResendRentalExtensionIntegration($rentalExtensionId: ID!) {
    resendRentalExtensionIntegration(rentalExtensionId: $rentalExtensionId) {
      errors
      rentalDateExtensionRequest {
        id
      }
      status
    }
  }
`;
