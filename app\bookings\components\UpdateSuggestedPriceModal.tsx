'use client';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation } from '@apollo/client';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Alert,
  CircularProgress,
  Snackbar,
  Typography,
} from '@mui/material';
import { EDIT_SUGGESTED_PRICE_MUTATION } from '../../gql/mutations/bookings';

interface UpdateSuggestedPriceModalProps {
  open: boolean;
  onClose: () => void;
  bookingId: string;
  currentPrice: number;
  originalPrice: number;
  onPriceUpdated: () => void;
}

export default function UpdateSuggestedPriceModal({
  open,
  onClose,
  bookingId,
  currentPrice,
  originalPrice,
  onPriceUpdated,
}: UpdateSuggestedPriceModalProps) {
  const { t } = useTranslation();
  const [suggestedPrice, setSuggestedPrice] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [showSuccess, setShowSuccess] = useState<boolean>(false);

  const [editSuggestedPrice] = useMutation(EDIT_SUGGESTED_PRICE_MUTATION);

  const handleSubmit = async () => {
    const price = parseFloat(suggestedPrice);

    if (!suggestedPrice?.trim() || isNaN(price) || price <= 0) {
      setError(t('bookings.price.validation.validPriceRequired'));
      return;
    }

    setLoading(true);
    setError('');

    try {
      const result = await editSuggestedPrice({
        variables: {
          rentalId: bookingId,
          suggestedPrice: price,
        },
      });

      if (!result?.data?.editSuggestedPrice?.errors?.length) {
        setShowSuccess(true);
        setTimeout(() => {
          onPriceUpdated();
          handleClose();
        }, 1500);
      } else {
        setError(
          result.data.editSuggestedPrice.errors[0] || t('bookings.price.errors.failedToUpdate')
        );
      }
    } catch (err: any) {
      console.error('Error updating price:', err);
      setError(err.message || t('bookings.price.errors.generalError'));
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setSuggestedPrice('');
    setError('');
    setLoading(false);
    setShowSuccess(false);
    onClose();
  };

  const handlePriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Allow only numbers and decimal point
    if (/^[0-9]*\.?[0-9]*$/.test(value) || value === '') {
      setSuggestedPrice(value);
      setError('');
    }
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>{t('bookings.price.updatePrice.title')}</DialogTitle>

      <DialogContent>
        <Box sx={{ mt: 2 }}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {t('bookings.price.currentPrice')}: {currentPrice} {t('common.currency')}
          </Typography>

          <TextField
            fullWidth
            label={t('bookings.price.updatePrice.newPrice')}
            value={suggestedPrice}
            onChange={handlePriceChange}
            error={!!error}
            placeholder={t('bookings.price.updatePrice.placeholder')}
            variant="outlined"
            type="text"
            inputProps={{
              inputMode: 'decimal',
              pattern: '[0-9]*\\.?[0-9]*',
            }}
          />

          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}
        </Box>
      </DialogContent>

      <DialogActions>
        <Button
          onClick={handleClose}
          disabled={loading}
          sx={{
            backgroundColor: '#f44336',
            color: 'white',
            '&:hover': {
              backgroundColor: '#d32f2f',
            },
            '&:disabled': {
              backgroundColor: '#cccccc',
              color: '#666666',
            },
          }}
        >
          {t('common.cancel')}
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={loading || !suggestedPrice?.trim()}
          sx={{
            backgroundColor: '#f44336',
            color: 'white',
            '&:hover': {
              backgroundColor: '#d32f2f',
            },
            '&:disabled': {
              backgroundColor: '#cccccc',
              color: '#666666',
            },
          }}
        >
          {loading ? <CircularProgress size={20} /> : t('bookings.price.update')}
        </Button>
      </DialogActions>

      <Snackbar
        open={showSuccess}
        autoHideDuration={1500}
        onClose={() => setShowSuccess(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert severity="success" onClose={() => setShowSuccess(false)}>
          {t('bookings.price.priceUpdatedSuccessfully')}
        </Alert>
      </Snackbar>
    </Dialog>
  );
}
