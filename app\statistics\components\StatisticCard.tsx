'use client';
import { Box, Card, CardContent, Typography, Avatar, useTheme } from '@mui/material';

interface StatisticCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  color: string;
}

export default function StatisticCard({ title, value, icon, color }: StatisticCardProps) {
  const theme = useTheme();

  return (
    <Card
      elevation={2}
      sx={{
        height: '100%',
        borderRadius: 2,
        transition: theme.transitions.create(['transform', 'box-shadow'], {
          duration: theme.transitions.duration.standard,
        }),
        '&:hover': {
          transform: theme.palette.mode === 'dark' ? 'none' : 'translateY(-5px)',
          boxShadow: theme.shadows[8],
        },
        // Ensure good contrast in all modes
        backgroundColor: theme.palette.background.paper,
        color: theme.palette.text.primary,
        // Add focus styles for keyboard navigation
        '&:focus-within': {
          outline: `2px solid ${theme.palette.primary.main}`,
          outlineOffset: '2px',
        },
      }}
      tabIndex={0}
      role="group"
      aria-label={`${title} statistics: ${value.toLocaleString()}`}
    >
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, gap: 2 }}>
          <Avatar
            sx={{
              bgcolor: theme.palette.mode === 'dark' ? `${color}40` : `${color}20`,
              color: theme.palette.mode === 'dark' && color === '#ffffff' ? '#000000' : color,
              mr: 2,
              width: 48,
              height: 48,
              // Ensure icon is visible in all modes
              '& svg': {
                fontSize: '1.5rem',
              },
            }}
            aria-hidden="true"
          >
            {icon}
          </Avatar>
          <Box>
            <Typography
              variant="body2"
              sx={{
                color:
                  theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.9)' : 'text.secondary',
                mb: 0.5,
                fontWeight: theme.palette.mode === 'dark' ? 'medium' : 'normal',
              }}
            >
              {title}
            </Typography>
            <Typography
              variant="h4"
              component="div"
              fontWeight="bold"
              sx={{
                color: theme.palette.mode === 'dark' ? '#fff' : 'text.primary',
              }}
            >
              {value.toLocaleString()}
            </Typography>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
}
