import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Paper,
  FormControlLabel,
  Checkbox,
  TextField,
  Grid,
  Alert,
  Divider,
} from '@mui/material';

interface DeliverySectionProps {
  isDelivery: boolean;
  isTwoWays: boolean;
  deliverAddress: string;
  handoverAddress: string;
  deliverLat: number;
  deliverLng: number;
  handoverLat: number;
  handoverLng: number;
  deliveryPrice: number;
  handoverPrice: number;
  onDeliveryChange: (checked: boolean) => void;
  onTwoWaysChange: (checked: boolean) => void;
  onDeliverAddressChange: (address: string) => void;
  onHandoverAddressChange: (address: string) => void;
  onDeliverLocationChange: (lat: number, lng: number) => void;
  onHandoverLocationChange: (lat: number, lng: number) => void;
  disabled?: boolean;
  isPickSameReturn: boolean;
  clicked?: boolean;
}

export default function DeliverySection({
  isDelivery,
  isTwoWays,
  deliverAddress,
  handoverAddress,
  deliverLat,
  deliverLng,
  handoverLat,
  handoverLng,
  deliveryPrice,
  handoverPrice,
  onDeliveryChange,
  onTwoWaysChange,
  onDeliverAddressChange,
  onHandoverAddressChange,
  onDeliverLocationChange,
  onHandoverLocationChange,
  disabled = false,
  isPickSameReturn,
  clicked,
}: DeliverySectionProps) {
  const { t } = useTranslation();

  const handleLocationShare = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          onDeliverLocationChange(latitude, longitude);
          // You could also reverse geocode to get address
        },
        (error) => {
          console.error('Error getting location:', error);
        }
      );
    }
  };

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        {t('delivery.options')}
      </Typography>

      {/* Delivery Checkbox */}
      <FormControlLabel
        control={
          <Checkbox
            checked={isDelivery}
            onChange={(e) => onDeliveryChange(e.target.checked)}
            disabled={disabled}
            color="primary"
          />
        }
        label={
          <Box>
            <Typography variant="body1">{t('car.delivery')}</Typography>
            <Typography variant="body2" color="textSecondary">
              {t('deliver.car.to.your.location')}
            </Typography>
          </Box>
        }
      />

      {isDelivery && (
        <Box sx={{ mt: 3 }}>
          <Grid container spacing={3}>
            {/* Delivery Address */}
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" gutterBottom>
                {t('delivery.location')}
              </Typography>
              <TextField
                fullWidth
                label={t('delivery.address')}
                value={deliverAddress}
                onChange={(e) => onDeliverAddressChange(e.target.value)}
                placeholder={t('enter.delivery.address')}
                multiline
                rows={3}
                disabled={disabled}
              />

              {/* Location sharing button */}
              <Box sx={{ mt: 1 }}>
                <Typography
                  variant="body2"
                  color="primary"
                  sx={{ cursor: 'pointer', textDecoration: 'underline' }}
                  onClick={handleLocationShare}
                >
                  📍 {t('share.current.location')}
                </Typography>
              </Box>

              {/* Map placeholder */}
              <Box
                sx={{
                  mt: 2,
                  height: 300,
                  border: 1,
                  borderColor: 'divider',
                  borderRadius: 1,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  bgcolor: 'background.paper',
                }}
              >
                <Typography variant="body2" color="textSecondary">
                  {t('map.will.be.displayed.here')}
                  <br />
                  {deliverLat && deliverLng
                    ? `${t('coordinates')}: ${deliverLat.toFixed(6)}, ${deliverLng.toFixed(6)}`
                    : ''}
                </Typography>
              </Box>

              {deliveryPrice > 0 && (
                <Alert severity="info" sx={{ mt: 2 }}>
                  {t('delivery.price')}: {deliveryPrice} {t('currency.sr')}
                </Alert>
              )}
            </Grid>

            {/* Two-way delivery option */}
            {!isPickSameReturn && (
              <Grid item xs={12} md={6}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={isTwoWays}
                      onChange={(e) => onTwoWaysChange(e.target.checked)}
                      disabled={disabled}
                      color="primary"
                    />
                  }
                  label={
                    <Box>
                      <Typography variant="body1">{t('two.way.delivery')}</Typography>
                      <Typography variant="body2" color="textSecondary">
                        {t('pick.up.car.from.dropoff.location')}
                      </Typography>
                    </Box>
                  }
                />

                {isTwoWays && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="subtitle1" gutterBottom>
                      {t('handover.location')}
                    </Typography>
                    <TextField
                      fullWidth
                      label={t('handover.address')}
                      value={handoverAddress}
                      onChange={(e) => onHandoverAddressChange(e.target.value)}
                      placeholder={t('enter.handover.address')}
                      multiline
                      rows={3}
                      disabled={disabled}
                    />

                    {/* Handover Map placeholder */}
                    <Box
                      sx={{
                        mt: 2,
                        height: 200,
                        border: 1,
                        borderColor: 'divider',
                        borderRadius: 1,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        bgcolor: 'background.paper',
                      }}
                    >
                      <Typography variant="body2" color="textSecondary">
                        {t('handover.map.will.be.displayed.here')}
                        <br />
                        {handoverLat && handoverLng
                          ? `${t('coordinates')}: ${handoverLat.toFixed(6)}, ${handoverLng.toFixed(
                              6
                            )}`
                          : ''}
                      </Typography>
                    </Box>

                    {handoverPrice > 0 && (
                      <Alert severity="info" sx={{ mt: 2 }}>
                        {t('handover.price')}: {handoverPrice} {t('currency.sr')}
                      </Alert>
                    )}
                  </Box>
                )}
              </Grid>
            )}
          </Grid>

          {/* Delivery Summary */}
          <Box sx={{ mt: 3 }}>
            <Divider sx={{ mb: 2 }} />
            <Typography variant="subtitle2" gutterBottom>
              {t('delivery.summary')}:
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2">
                  <strong>{t('delivery.location')}:</strong> {deliverAddress || t('not.specified')}
                </Typography>
              </Grid>
              {isTwoWays && !isPickSameReturn && (
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2">
                    <strong>{t('handover.location')}:</strong>{' '}
                    {handoverAddress || t('not.specified')}
                  </Typography>
                </Grid>
              )}
              <Grid item xs={12}>
                <Typography variant="body2" color="primary">
                  <strong>{t('total.delivery.cost')}:</strong>{' '}
                  {deliveryPrice + (isTwoWays ? handoverPrice : 0)} {t('currency.sr')}
                </Typography>
              </Grid>
            </Grid>
          </Box>
        </Box>
      )}
    </Paper>
  );
}
