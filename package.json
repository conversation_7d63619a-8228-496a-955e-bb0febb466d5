{"name": "carwah_dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@apollo/client": "^3.11.4", "@emotion/react": "11.11.3", "@emotion/styled": "11.11.0", "@hookform/resolvers": "^3.3.4", "@mui/icons-material": "^5.15.10", "@mui/lab": "^7.0.0-beta.11", "@mui/material": "5.15.11", "@mui/styles": "^6.4.7", "@mui/x-data-grid": "^6.19.4", "@mui/x-date-pickers": "^6.19.4", "@reduxjs/toolkit": "^2.6.1", "@types/apollo-upload-client": "^18.0.0", "@types/uuid": "^10.0.0", "apollo-upload-client": "^18.0.1", "dayjs": "^1.11.13", "graphql": "^16.9.0", "i18next": "^24.2.2", "next": "^15.1.5", "next-redux-wrapper": "^8.1.0", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.50.1", "react-i18next": "^15.4.1", "react-redux": "^9.2.0", "react-toastify": "^11.0.5", "recharts": "^2.15.2", "uuid": "^11.1.0", "yup": "^1.3.3", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "sass": "^1.77.8", "typescript": "^5"}}