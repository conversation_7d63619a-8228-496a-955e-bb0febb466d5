'use client';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { Link, Chip, Box, Typography } from '@mui/material';
import {
  Edit as EditIcon,
  Print as PrintIcon,
  History as HistoryIcon,
  Assignment as AssignmentIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import GenericTable, {
  GenericTableColumn,
  GenericTableAction,
} from '../../shared/components/GenericTable';
import { BusinessBooking } from '../../models/businessBookings.model';

interface BusinessBookingsTableProps {
  bookings: BusinessBooking[];
  page: number;
  limit: number;
  total: number;
  onPageChange: (page: number) => void;
  onLimitChange: (limit: number) => void;
  onSortChange: (field: string, direction: 'asc' | 'desc') => void;
  orderBy: string;
  sortBy: string;
  loading?: boolean;
  onAssign?: (booking: BusinessBooking) => void;
  onEdit?: (booking: BusinessBooking) => void;
  onPrint?: (booking: BusinessBooking) => void;
  onViewTimeline?: (booking: BusinessBooking) => void;
}

export default function BusinessBookingsTable({
  bookings,
  page,
  limit,
  total,
  onPageChange,
  onLimitChange,
  onSortChange,
  orderBy,
  sortBy,
  loading = false,
  onAssign,
  onEdit,
  onPrint,
  onViewTimeline,
}: BusinessBookingsTableProps) {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  const dateLocale = isRTL ? ar : enUS;
  const { user } = useSelector((state: any) => state.auth);
  const isAlly = user?.ally_id;

  // Status color mapping
  const getStatusColor = (
    status: string
  ): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    switch (status?.toLowerCase()) {
      case 'confirmed':
        return 'success';
      case 'car_received':
        return 'info';
      case 'invoiced':
        return 'primary';
      case 'cancelled':
        return 'error';
      case 'closed':
        return 'secondary';
      case 'pending':
        return 'warning';
      default:
        return 'default';
    }
  };

  // Format date and time
  const formatDateTime = (dateString: string) => {
    if (!dateString) return '--';

    try {
      const date = new Date(dateString);
      return (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, minWidth: 'max-content' }}>
          <Typography variant="body2" sx={{ fontSize: 'small' }}>
            {format(date, 'dd/MM/yyyy', { locale: dateLocale })}
          </Typography>
          <Typography variant="body2" sx={{ fontSize: 'small', color: 'text.secondary' }}>
            {format(date, 'HH:mm')}
          </Typography>
        </Box>
      );
    } catch (error) {
      return '--';
    }
  };

  // Define table columns
  const columns: GenericTableColumn[] = [
    {
      id: 'id',
      label: t('ID'),
      minWidth: 100,
      sortable: true,
      align: (isRTL ? 'right' : 'left') as 'right' | 'left' | 'center',
      format: (value: string, row: BusinessBooking) => (
        <Link
          href={`/business-bookings/${row.id}`}
          sx={{
            textDecoration: 'none',
            color: 'primary.main',
            fontWeight: 500,
            '&:hover': {
              textDecoration: 'underline',
            },
          }}
        >
          {value}
        </Link>
      ),
    },
    {
      id: 'bookingNo',
      label: t('Booking No.'),
      minWidth: 120,
      align: (isRTL ? 'right' : 'left') as 'right' | 'left' | 'center',
      format: (value: string, row: BusinessBooking) => (
        <Link
          href={`/business-bookings/${row.id}`}
          sx={{
            textDecoration: 'none',
            color: 'primary.main',
            fontWeight: 500,
            '&:hover': {
              textDecoration: 'underline',
            },
          }}
        >
          {value}
        </Link>
      ),
    },
    {
      id: 'customerName',
      label: t('bookings.list.customerName'),
      minWidth: 150,
      align: (isRTL ? 'right' : 'left') as 'right' | 'left' | 'center',
      format: (value: string, row: BusinessBooking) => (
        <Link
          href={`/customers/${row.userId}`}
          sx={{
            textDecoration: 'none',
            color: 'primary.main',
            fontWeight: 500,
            '&:hover': {
              textDecoration: 'underline',
            },
          }}
        >
          {value}
        </Link>
      ),
    },
    {
      id: 'allyCompanyName',
      label: t('bookings.list.allyName'),
      minWidth: 150,
      align: (isRTL ? 'right' : 'left') as 'right' | 'left' | 'center',
      format: (value: string, row: BusinessBooking) =>
        row?.acceptedOffer?.allyCompanyId ? (
          <Link
            href={`/companies/${row.acceptedOffer.allyCompanyId}`}
            sx={{
              textDecoration: 'none',
              color: 'primary.main',
              fontWeight: 500,
              '&:hover': {
                textDecoration: 'underline',
              },
            }}
          >
            {row.acceptedOffer.allyCompanyName}
          </Link>
        ) : (
          '--'
        ),
    },
    {
      id: 'insuranceName',
      label: t('rental.insuranceType'),
      minWidth: 120,
      align: (isRTL ? 'right' : 'left') as 'right' | 'left' | 'center',
      format: (value: string) => t(value) || '--',
    },
    {
      id: 'pickUpCityName',
      label: t('bookings.list.pickupCity'),
      minWidth: 120,
      align: (isRTL ? 'right' : 'left') as 'right' | 'left' | 'center',
      format: (value: string, row: BusinessBooking) => {
        const cityName = isRTL ? row?.arPickUpCityName : row?.enPickUpCityName;
        return cityName || value || '--';
      },
    },
    {
      id: 'carInfo',
      label: t('car'),
      minWidth: 200,
      align: (isRTL ? 'right' : 'left') as 'right' | 'left' | 'center',
      format: (value: any, row: BusinessBooking) => {
        const makeName = isRTL ? row?.arMakeName : row?.enMakeName;
        const modelName = isRTL ? row?.arModelName : row?.enModelName;
        return `${makeName || row?.makeName || ''} - ${modelName || row?.modelName || ''} - ${
          row?.year || ''
        }`;
      },
    },
    {
      id: 'numberOfMonths',
      label: t('Duration in months'),
      minWidth: 120,
      align: 'center',
      sortable: true,
    },
    {
      id: 'numberOfCars',
      label: t('numberOfCars'),
      minWidth: 100,
      align: 'center',
    },
    {
      id: 'pickUpDatetime',
      label: t('bookings.list.pickup'),
      minWidth: 150,
      sortable: true,
      align: (isRTL ? 'right' : 'left') as 'right' | 'left' | 'center',
      format: (value: string) => formatDateTime(value),
    },
    {
      id: 'dropOffDatetime',
      label: t('bookings.list.delivery'),
      minWidth: 150,
      sortable: true,
      align: (isRTL ? 'right' : 'left') as 'right' | 'left' | 'center',
      format: (value: string) => formatDateTime(value),
    },
    {
      id: 'pricePerMonth',
      label: t('bookings.list.header.billingAmountMonth'),
      minWidth: 120,
      align: 'right',
      sortable: true,
      format: (value: number) => (value ? `${value.toLocaleString()} ${t('SAR')}` : '--'),
    },
    {
      id: 'totalBookingPrice',
      label: t('bookings.list.header.billingAmount'),
      minWidth: 120,
      align: 'right',
      sortable: true,
      format: (value: number) => (value ? `${value.toLocaleString()} ${t('SAR')}` : '--'),
    },
    {
      id: 'paidAmount',
      label: t('bookings.list.paidAmount'),
      minWidth: 120,
      align: 'right',
      format: (_value: any, row: BusinessBooking) => {
        const paidAmount =
          (row?.acceptedOffer?.carInsuranceFull || row?.acceptedOffer?.carInsuranceStandard || 0) *
          (row?.numberOfMonths || 0);
        return paidAmount ? `${paidAmount.toLocaleString()} ${t('SAR')}` : '--';
      },
    },
    {
      id: 'status',
      label: t('bookings.list.bookingStatus'),
      minWidth: 150,
      align: (isRTL ? 'right' : 'left') as 'right' | 'left' | 'center',
      format: (value: string, row: BusinessBooking) => {
        // Try different localization keys for status
        const statusKey = value?.toLowerCase();
        let localizedStatus = t(statusKey);

        // If the key doesn't exist, try uppercase
        if (localizedStatus === statusKey) {
          localizedStatus = t(value?.toUpperCase() || 'UNKNOWN');
        }

        // If still not found, try with underscores
        if (localizedStatus === value?.toUpperCase()) {
          localizedStatus = t(statusKey?.replace(/ /g, '_'));
        }

        return (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
            <Chip
              label={localizedStatus}
              color={getStatusColor(value)}
              size="small"
              variant="filled"
              sx={{
                fontWeight: 500,
                fontSize: '0.75rem',
                height: 24,
              }}
            />
            {/* {row?.statusLocalized && (
              <Typography variant="caption" color="text.secondary">
                {row.statusLocalized}
              </Typography>
            )} */}
          </Box>
        );
      },
    },
    {
      id: 'createdAt',
      label: t('createdAt'),
      minWidth: 150,
      sortable: true,
      align: (isRTL ? 'right' : 'left') as 'right' | 'left' | 'center',
      format: (value: string) => formatDateTime(value),
    },
    // Assignment column for non-ally users
    ...(isAlly
      ? []
      : [
          {
            id: 'assignment',
            label: t('Assign'),
            minWidth: 120,
            align: (isRTL ? 'right' : 'left') as 'right' | 'left' | 'center',
            format: (_value: any, row: BusinessBooking) => (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                <Typography variant="caption" color="text.secondary">
                  {row?.assignedTo ? t('Assigned') : t('Unassigned')}
                </Typography>
              </Box>
            ),
          },
        ]),
  ];

  // Define table actions
  const actions: GenericTableAction[] = [
    ...(onEdit
      ? [
          {
            id: 'edit',
            label: t('common.edit'),
            icon: <EditIcon />,
            onClick: onEdit,
            color: 'primary' as const,
          },
        ]
      : []),
    ...(onPrint
      ? [
          {
            id: 'print',
            label: t('print.bookingDetails'),
            icon: <PrintIcon />,
            onClick: onPrint,
            color: 'primary' as const,
          },
        ]
      : []),
    ...(onViewTimeline
      ? [
          {
            id: 'timeline',
            label: t('bookings.timeline.title'),
            icon: <HistoryIcon />,
            onClick: onViewTimeline,
            color: 'info' as const,
          },
        ]
      : []),
    ...(onAssign && !isAlly
      ? [
          {
            id: 'assign',
            label: t('assignment.assignBooking'),
            icon: <AssignmentIcon />,
            onClick: onAssign,
            color: 'secondary' as const,
          },
        ]
      : []),
  ];

  return (
    <GenericTable
      data={bookings}
      columns={columns}
      actions={actions}
      page={page}
      limit={limit}
      total={total}
      onPageChange={onPageChange}
      onLimitChange={onLimitChange}
      onSortChange={onSortChange}
      orderBy={orderBy}
      sortBy={sortBy}
      loading={loading}
      emptyMessage={t('No business bookings found')}
      detailsPath="/business-bookings"
      idField="id"
    />
  );
}
