/**
 * Network utility functions
 */
import { i18n } from 'i18next';
import { useTranslation } from 'react-i18next';

// Add i18n to window global for typing
declare global {
  interface Window {
    i18n?: i18n;
  }
}

// Rate limiting settings
export const THROTTLE_MS = 1000; // 1 second between similar requests
export const REQUEST_TIMEOUT = 30000; // 30 seconds timeout
export const MAX_RETRIES = 2; // Maximum number of retries

// Store last request timestamps by operation name
const lastRequestTimestamps: Record<string, number> = {};

/**
 * Check if the network is online
 * @returns {boolean} True if the network is online
 */
export const isNetworkOnline = (): boolean => {
  if (typeof navigator === 'undefined') return true; // SSR environment, assume online
  return navigator.onLine;
};

/**
 * Check if a request should be throttled
 * @param {string} operationName - Name of the operation
 * @returns {boolean} True if request should be throttled (too frequent)
 */
export const shouldThrottleRequest = (operationName: string): boolean => {
  if (!operationName) return false; // Don't throttle unnamed operations

  const now = Date.now();
  const lastTimestamp = lastRequestTimestamps[operationName] || 0;

  // Check if we should throttle
  if (now - lastTimestamp < THROTTLE_MS) {
    return true;
  }

  // Update timestamp and allow request
  lastRequestTimestamps[operationName] = now;
  return false;
};

/**
 * Get localized error message
 * @param {string} key - Translation key
 * @param {string} fallback - Fallback message if translation fails
 * @returns {string} Localized error message or fallback
 */
export const getLocalizedErrorMessage = (key: string, fallback: string): string => {
  try {
    // Try to get translation if i18next is available
    if (typeof window !== 'undefined' && window.i18n) {
      const translated = window.i18n.t(key);
      if (translated && translated !== key) {
        return translated;
      }
    }
  } catch (e) {
    console.error('Translation error:', e);
  }
  return fallback;
};

/**
 * Get appropriate error message for network error
 * @param {any} error - The error object
 * @returns {string} Human-readable error message
 */
export const getNetworkErrorMessage = (error: any): string => {
  if (!isNetworkOnline()) {
    return getLocalizedErrorMessage(
      'login.networkOffline',
      'You appear to be offline. Please check your internet connection.'
    );
  } else if (error?.statusCode === 429) {
    return getLocalizedErrorMessage(
      'login.tooManyRequests',
      'Too many requests. Please try again later.'
    );
  } else {
    return getLocalizedErrorMessage(
      'login.networkError',
      'Network error. Please check your connection and try again.'
    );
  }
};

/**
 * Get throttled request error message
 * @returns {string} Throttled request error message
 */
export const getThrottledErrorMessage = (): string => {
  return getLocalizedErrorMessage(
    'login.tooManyAttempts',
    'Too many requests. Please wait a moment and try again.'
  );
};
