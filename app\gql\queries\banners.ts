import { gql } from "@apollo/client";

export const Banners_Query = gql`
  query Banners($page: Int!, $limit: Int!) {
    banners(page: $page, limit: $limit) {
      collection {
        createdAt
        createdById
        displayOrder
        id
        imgAr
        imgEn
        isActive
        updatedById
      }
      metadata {
        currentPage
        limitValue
        totalCount
        totalPages
      }
    }
  }
`;
