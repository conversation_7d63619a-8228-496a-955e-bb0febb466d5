'use client';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLazyQuery } from '@apollo/client';
import { GET_CUSTOMER_BY_PHONE } from '../../../gql/queries/customers';
import {
  Box,
  TextField,
  Button,
  Typography,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import PersonIcon from '@mui/icons-material/Person';

interface CustomerDetails {
  id: string;
  name: string;
  mobile: string;
  email?: string;
  isActive?: boolean;
  createdAt?: string;
}

interface GettingCustomerDetailsProps {
  setCustomerDetails: (customer: any) => void;
  setCustomerId: (id: string) => void;
  setFursanChecked?: (checked: boolean) => void;
  setFursanVerified?: (verified: boolean) => void;
  customerDetails?: any;
  mode?: 'add' | 'edit';
}

// Common country codes for the region
const COUNTRY_CODES = [
  { code: '966', country: 'Saudi Arabia', flag: '🇸🇦' },
  { code: '971', country: 'UAE', flag: '🇦🇪' },
  { code: '973', country: 'Bahrain', flag: '🇧🇭' },
  { code: '965', country: 'Kuwait', flag: '🇰🇼' },
  { code: '974', country: 'Qatar', flag: '🇶🇦' },
  { code: '968', country: 'Oman', flag: '🇴🇲' },
  { code: '962', country: 'Jordan', flag: '🇯🇴' },
  { code: '20', country: 'Egypt', flag: '🇪🇬' },
  { code: '961', country: 'Lebanon', flag: '🇱🇧' },
  { code: '964', country: 'Iraq', flag: '🇮🇶' },
  { code: '963', country: 'Syria', flag: '🇸🇾' },
];

export default function GettingCustomerDetails({
  setCustomerDetails,
  setCustomerId,
  setFursanChecked,
  setFursanVerified,
  customerDetails,
  mode = 'add',
}: GettingCustomerDetailsProps) {
  const { t } = useTranslation();
  const [phoneNumber, setPhoneNumber] = useState('');
  const [countryCode, setCountryCode] = useState('966'); // Default to Saudi Arabia
  const [searchError, setSearchError] = useState('');
  const [foundCustomer, setFoundCustomer] = useState<CustomerDetails | null>(null);

  // Initialize component with existing customer data in edit mode
  React.useEffect(() => {
    if (mode === 'edit' && customerDetails) {
      const customerData = {
        id: customerDetails.id,
        name: customerDetails.name,
        mobile: customerDetails.mobile,
        email: customerDetails.email || '',
        isActive: true,
      };

      setFoundCustomer(customerData);

      // Extract phone number and country code from mobile
      if (customerDetails.mobile) {
        const mobile = customerDetails.mobile.toString();
        // Find matching country code
        const countryCodeMatch = COUNTRY_CODES.find((country) => mobile.startsWith(country.code));

        if (countryCodeMatch) {
          setCountryCode(countryCodeMatch.code);
          setPhoneNumber(mobile.substring(countryCodeMatch.code.length));
        } else {
          // Default handling if no country code matches
          setPhoneNumber(mobile);
        }
      }
    }
  }, [mode, customerDetails]);

  const [searchCustomer, { loading: searchingCustomer }] = useLazyQuery(GET_CUSTOMER_BY_PHONE, {
    onCompleted: (data) => {
      if (data?.users?.collection?.length > 0) {
        const customer = data.users.collection[0];
        setFoundCustomer(customer);
        setCustomerDetails(data);
        setCustomerId(customer.id);
        setSearchError('');

        // Reset Fursan status
        if (setFursanChecked) setFursanChecked(false);
        if (setFursanVerified) setFursanVerified(false);
      } else {
        setSearchError(t('customerNotFound'));
        setFoundCustomer(null);
        setCustomerDetails(null);
        setCustomerId('');
      }
    },
    onError: (error) => {
      setSearchError(error.message || t('errorSearchingCustomer'));
      setFoundCustomer(null);
      setCustomerDetails(null);
      setCustomerId('');
    },
  });

  const getPlaceholderByCountry = () => {
    switch (countryCode) {
      case '+966': // Saudi Arabia
        return '5xxxxxxxx';
      case '+971': // UAE
        return '5xxxxxxxx';
      case '+973': // Bahrain
        return '3xxxxxxx';
      case '+965': // Kuwait
        return '5xxxxxxx';
      case '+974': // Qatar
        return '5xxxxxxx';
      case '+968': // Oman
        return '7xxxxxxx';
      case '+962': // Jordan
        return '7xxxxxxxx';
      case '+20': // Egypt
        return '10xxxxxxxx';
      case '+961': // Lebanon
        return '3xxxxxxx';
      case '+964': // Iraq
        return '75xxxxxxxx';
      case '+963': // Syria
        return '9xxxxxxxx';
      default:
        return 'xxxxxxxxx';
    }
  };

  const handleSearch = () => {
    if (!phoneNumber.trim()) {
      setSearchError(t('pleaseEnterPhoneNumber'));
      return;
    }

    // Validate phone number format based on country
    const cleanPhone = phoneNumber.replace(/\D/g, '');
    let minLength = 8;

    // Adjust minimum length based on country code
    switch (countryCode) {
      case '+966': // Saudi Arabia
        minLength = 9; // 5xxxxxxxx
        break;
      case '+971': // UAE
        minLength = 9; // 5xxxxxxxx
        break;
      case '+20': // Egypt
        minLength = 10; // 10xxxxxxxx or 11xxxxxxxx
        break;
      default:
        minLength = 8; // Default minimum
    }

    if (cleanPhone.length < minLength) {
      setSearchError(t('pleaseEnterValidPhoneNumber'));
      return;
    }

    // Concatenate country code with mobile number
    const fullMobileNumber = `${countryCode}${phoneNumber.trim()}`;

    setSearchError('');
    searchCustomer({
      variables: {
        mobile: fullMobileNumber,
        type: 'customers',
      },
    });
  };

  const handlePhoneChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    // Allow only numbers and common phone formatting characters
    const cleanValue = value.replace(/[^\d+\-\s()]/g, '');
    setPhoneNumber(cleanValue);

    // Clear previous search results when phone changes
    if (foundCustomer) {
      setFoundCustomer(null);
      setCustomerDetails(null);
      setCustomerId('');
    }
    if (searchError) {
      setSearchError('');
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <Box>
      {/* Phone Number Search */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3, alignItems: 'flex-start' }}>
        <FormControl sx={{ minWidth: 120 }}>
          <InputLabel>{t('countryCode')}</InputLabel>
          <Select
            value={countryCode}
            label={t('countryCode')}
            onChange={(e) => setCountryCode(e.target.value)}
            disabled={searchingCustomer}
          >
            {COUNTRY_CODES.map((country) => (
              <MenuItem key={country.code} value={country.code}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <span>{country.flag}</span>
                  <span>{country.code}</span>
                </Box>
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <TextField
          fullWidth
          label={t('customerMobile')}
          value={phoneNumber}
          onChange={handlePhoneChange}
          onKeyPress={handleKeyPress}
          placeholder={getPlaceholderByCountry()}
          variant="outlined"
          error={!!searchError}
          helperText={searchError}
          disabled={searchingCustomer}
          InputProps={{
            dir: 'ltr', // Always show phone numbers left-to-right
          }}
        />
        <Button
          variant="contained"
          onClick={handleSearch}
          disabled={searchingCustomer || !phoneNumber.trim()}
          startIcon={searchingCustomer ? <CircularProgress size={20} /> : <SearchIcon />}
          sx={{ minWidth: 120, height: 56 }}
        >
          {searchingCustomer ? t('searching') : t('search')}
        </Button>
      </Box>

      {/* Display full phone number */}
      {phoneNumber && (
        <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
          {t('fullPhoneNumber')}: {countryCode} {phoneNumber}
        </Typography>
      )}

      {/* Search Results */}
      {foundCustomer && (
        <Card sx={{ mb: 3, border: '2px solid', borderColor: 'success.main' }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <PersonIcon color="success" />
              <Typography variant="h6" color="success.main">
                {t('customerFound')}
              </Typography>
            </Box>

            <Box
              sx={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                gap: 2,
              }}
            >
              <Box>
                <Typography variant="caption" color="textSecondary">
                  {t('customerName')}
                </Typography>
                <Typography variant="body1" fontWeight="medium">
                  {foundCustomer.name || t('notSpecified')}
                </Typography>
              </Box>

              <Box>
                <Typography variant="caption" color="textSecondary">
                  {t('customerMobile')}
                </Typography>
                <Typography variant="body1" fontWeight="medium">
                  {foundCustomer.mobile}
                </Typography>
              </Box>

              {foundCustomer.email && (
                <Box>
                  <Typography variant="caption" color="textSecondary">
                    {t('customerEmail')}
                  </Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {foundCustomer.email}
                  </Typography>
                </Box>
              )}

              <Box>
                <Typography variant="caption" color="textSecondary">
                  {t('accountStatus')}
                </Typography>
                <Typography
                  variant="body1"
                  fontWeight="medium"
                  color={foundCustomer.isActive ? 'success.main' : 'error.main'}
                >
                  {foundCustomer.isActive ? t('active') : t('inactive')}
                </Typography>
              </Box>
            </Box>

            {/* Fursan Verification Section */}
            <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                {t('loyaltyProgramStatus')}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {t('fursanVerificationWillBeImplemented')}
              </Typography>
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      {!foundCustomer && !searchError && (
        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="body2">{t('enterCustomerPhoneInstructions')}</Typography>
        </Alert>
      )}

      {/* Customer Creation Option */}
      {searchError === t('customerNotFound') && phoneNumber.trim() && (
        <Alert severity="warning" sx={{ mb: 2 }}>
          <Typography variant="body2" gutterBottom>
            {t('customerNotFoundWithPhone', { phone: phoneNumber })}
          </Typography>
          <Button variant="outlined" size="small" sx={{ mt: 1 }}>
            {t('createNewCustomer')}
          </Button>
        </Alert>
      )}
    </Box>
  );
}
