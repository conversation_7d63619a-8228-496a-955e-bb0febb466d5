'use client';
import React, { useState } from 'react';
import {
  <PERSON>,
  CardContent,
  Card<PERSON><PERSON>er,
  Collapse,
  IconButton,
  Typography,
  Box,
} from '@mui/material';
import { ExpandMore as ExpandMoreIcon, ExpandLess as ExpandLessIcon } from '@mui/icons-material';

interface RctCollapsibleCardProps {
  colClasses?: string;
  heading: React.ReactNode;
  collapsible?: boolean;
  fullBlock?: boolean;
  customClasses?: string;
  children: React.ReactNode;
  InInstallment?: boolean;
}

export default function RctCollapsibleCard({
  colClasses,
  heading,
  collapsible = false,
  fullBlock = false,
  customClasses = '',
  children,
  InInstallment = false,
}: RctCollapsibleCardProps) {
  const [expanded, setExpanded] = useState(true);

  const handleToggle = () => {
    if (collapsible) {
      setExpanded(!expanded);
    }
  };

  return (
    <Box className={colClasses} sx={{ mb: 2 }}>
      <Card
        className={customClasses}
        sx={{
          width: fullBlock ? '100%' : 'auto',
          overflow: customClasses.includes('overflow-hidden') ? 'hidden' : 'visible',
        }}
      >
        <CardHeader
          title={
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Typography variant="h6" component="div">
                {heading}
              </Typography>
              {collapsible && (
                <IconButton onClick={handleToggle} size="small">
                  {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
              )}
            </Box>
          }
          sx={{
            pb: collapsible ? 1 : 2,
            '& .MuiCardHeader-title': {
              width: '100%',
            },
          }}
        />

        <Collapse in={expanded}>
          <CardContent sx={{ pt: collapsible ? 0 : 1 }}>{children}</CardContent>
        </Collapse>
      </Card>
    </Box>
  );
}
