import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  InputAdornment,
  IconButton,
  Autocomplete,
  Alert,
  CircularProgress,
} from '@mui/material';
import { Search, LocationOn, MyLocation, Place } from '@mui/icons-material';

interface Location {
  lat: number;
  lng: number;
  address?: string;
  placeId?: string;
}

interface MapSectionProps {
  location: Location | null;
  onLocationChange: (location: Location | null) => void;
  disabled?: boolean;
  clicked?: boolean;
  showDelivery: boolean;
  deliveryType?: 'one-way' | 'two-way';
}

export default function MapSection({
  location,
  onLocationChange,
  disabled = false,
  clicked = false,
  showDelivery,
  deliveryType,
}: MapSectionProps) {
  const { t } = useTranslation();
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);
  const markerRef = useRef<any>(null);
  const autocompleteRef = useRef<HTMLInputElement>(null);

  const [searchValue, setSearchValue] = useState('');
  const [searchOptions, setSearchOptions] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isMapLoaded, setIsMapLoaded] = useState(false);
  const [currentLocation, setCurrentLocation] = useState<Location | null>(null);

  // Google Maps API key - should be in environment variables
  const GOOGLE_MAPS_API_KEY = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || 'your-api-key-here';

  // Don't show if delivery is not enabled
  if (!showDelivery) {
    return null;
  }

  const isRequired = showDelivery;
  const hasError = clicked && isRequired && !location;

  // Load Google Maps API
  useEffect(() => {
    if ((window as any).google) {
      setIsMapLoaded(true);
      return;
    }

    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${GOOGLE_MAPS_API_KEY}&libraries=places&callback=initMap`;
    script.async = true;
    script.defer = true;

    (window as any).initMap = () => {
      setIsMapLoaded(true);
    };

    document.head.appendChild(script);

    return () => {
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
      if ((window as any).initMap) {
        delete (window as any).initMap;
      }
    };
  }, [GOOGLE_MAPS_API_KEY]);

  // Initialize map
  useEffect(() => {
    if (!isMapLoaded || !mapRef.current) return;

    const google = (window as any).google;
    if (!google) return;

    const defaultLocation = { lat: 24.7136, lng: 46.6753 }; // Riyadh default
    const initialLocation = location || currentLocation || defaultLocation;

    mapInstanceRef.current = new google.maps.Map(mapRef.current, {
      center: initialLocation,
      zoom: 15,
      mapTypeControl: false,
      streetViewControl: false,
      fullscreenControl: false,
    });

    // Add marker
    markerRef.current = new google.maps.Marker({
      position: initialLocation,
      map: mapInstanceRef.current,
      draggable: true,
      title: t('delivery.location'),
    });

    // Handle marker drag
    markerRef.current.addListener('dragend', (event: any) => {
      const newLocation = {
        lat: event.latLng.lat(),
        lng: event.latLng.lng(),
      };
      reverseGeocode(newLocation);
    });

    // Handle map click
    mapInstanceRef.current.addListener('click', (event: any) => {
      const newLocation = {
        lat: event.latLng.lat(),
        lng: event.latLng.lng(),
      };
      markerRef.current.setPosition(newLocation);
      reverseGeocode(newLocation);
    });

    // Setup autocomplete
    if (autocompleteRef.current) {
      const autocomplete = new google.maps.places.Autocomplete(autocompleteRef.current, {
        componentRestrictions: { country: 'sa' }, // Restrict to Saudi Arabia
        fields: ['place_id', 'geometry', 'name', 'formatted_address'],
      });

      autocomplete.addListener('place_changed', () => {
        const place = autocomplete.getPlace();
        if (place.geometry && place.geometry.location) {
          const newLocation = {
            lat: place.geometry.location.lat(),
            lng: place.geometry.location.lng(),
            address: place.formatted_address,
            placeId: place.place_id,
          };
          updateLocation(newLocation);
        }
      });
    }
  }, [isMapLoaded, location, currentLocation]);

  // Reverse geocoding function
  const reverseGeocode = async (coords: { lat: number; lng: number }) => {
    const google = (window as any).google;
    if (!google) return;

    const geocoder = new google.maps.Geocoder();

    try {
      const response = await geocoder.geocode({ location: coords });
      if (response.results && response.results.length > 0) {
        const address = response.results[0].formatted_address;
        const placeId = response.results[0].place_id;

        const newLocation: Location = {
          ...coords,
          address,
          placeId,
        };

        onLocationChange(newLocation);
        setSearchValue(address);
      }
    } catch (error) {
      console.error('Reverse geocoding failed:', error);
    }
  };

  // Update location function
  const updateLocation = (newLocation: Location) => {
    onLocationChange(newLocation);
    setSearchValue(newLocation.address || '');

    if (mapInstanceRef.current && markerRef.current) {
      const position = { lat: newLocation.lat, lng: newLocation.lng };
      mapInstanceRef.current.setCenter(position);
      markerRef.current.setPosition(position);
    }
  };

  // Get current location
  const getCurrentLocation = () => {
    if (!navigator.geolocation) {
      alert(t('geolocation.not.supported'));
      return;
    }

    setIsLoading(true);
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const newLocation = {
          lat: position.coords.latitude,
          lng: position.coords.longitude,
        };
        setCurrentLocation(newLocation);
        reverseGeocode(newLocation);
        setIsLoading(false);
      },
      (error) => {
        console.error('Error getting location:', error);
        alert(t('error.getting.location'));
        setIsLoading(false);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0,
      }
    );
  };

  // Search places
  const searchPlaces = async (query: string) => {
    const google = (window as any).google;
    if (!google || !query) {
      setSearchOptions([]);
      return;
    }

    const service = new google.maps.places.PlacesService(mapInstanceRef.current);
    const request = {
      query,
      location: new google.maps.LatLng(24.7136, 46.6753), // Riyadh
      radius: 50000,
      fields: ['place_id', 'geometry', 'name', 'formatted_address'],
    };

    service.textSearch(request, (results: any[], status: any) => {
      if (status === google.maps.places.PlacesServiceStatus.OK && results) {
        setSearchOptions(results.slice(0, 5)); // Limit to 5 results
      } else {
        setSearchOptions([]);
      }
    });
  };

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        {deliveryType === 'two-way' ? t('delivery.pickup.location') : t('delivery.location')}
      </Typography>

      <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
        {t('select.delivery.location.description')}
      </Typography>

      {/* Search Input */}
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          label={t('search.location')}
          value={searchValue}
          onChange={(e) => {
            setSearchValue(e.target.value);
            searchPlaces(e.target.value);
          }}
          disabled={disabled}
          error={hasError}
          helperText={hasError ? t('please.select.delivery.location') : ''}
          inputRef={autocompleteRef}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
            endAdornment: (
              <InputAdornment position="end">
                <IconButton
                  onClick={getCurrentLocation}
                  disabled={disabled || isLoading}
                  size="small"
                >
                  {isLoading ? <CircularProgress size={20} /> : <MyLocation />}
                </IconButton>
              </InputAdornment>
            ),
          }}
        />
      </Box>

      {/* Map Container */}
      {!isMapLoaded ? (
        <Box
          sx={{
            height: 400,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'grey.100',
            borderRadius: 1,
          }}
        >
          <CircularProgress />
        </Box>
      ) : (
        <Box
          ref={mapRef}
          sx={{
            height: 400,
            width: '100%',
            borderRadius: 1,
            border: hasError ? '2px solid' : '1px solid',
            borderColor: hasError ? 'error.main' : 'divider',
          }}
        />
      )}

      {/* Selected Location Display */}
      {location && (
        <Box
          sx={{
            mt: 2,
            p: 2,
            bgcolor: 'background.paper',
            border: 1,
            borderColor: 'divider',
            borderRadius: 1,
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
            <Place color="primary" />
            <Typography variant="subtitle2">{t('selected.location')}</Typography>
          </Box>
          <Typography variant="body2">
            {location.address || `${location.lat.toFixed(6)}, ${location.lng.toFixed(6)}`}
          </Typography>
          <Typography variant="caption" color="textSecondary">
            {t('coordinates')}: {location.lat.toFixed(6)}, {location.lng.toFixed(6)}
          </Typography>
        </Box>
      )}

      {/* Instructions */}
      <Box sx={{ mt: 2 }}>
        <Alert severity="info" variant="outlined">
          <Typography variant="body2">• {t('click.on.map.to.set.location')}</Typography>
          <Typography variant="body2">• {t('drag.marker.to.adjust.position')}</Typography>
          <Typography variant="body2">• {t('use.search.to.find.specific.place')}</Typography>
          <Typography variant="body2">• {t('use.my.location.for.current.position')}</Typography>
        </Alert>
      </Box>

      {/* Delivery Type Info */}
      {deliveryType === 'two-way' && (
        <Box
          sx={{
            mt: 2,
            p: 2,
            bgcolor: 'warning.50',
            border: 1,
            borderColor: 'warning.main',
            borderRadius: 1,
          }}
        >
          <Typography variant="subtitle2" gutterBottom color="warning.main">
            {t('two.way.delivery.note')}
          </Typography>
          <Typography variant="body2" color="textSecondary">
            {t('car.will.be.delivered.to.this.location.and.picked.up.from.same.location')}
          </Typography>
        </Box>
      )}
    </Paper>
  );
}
