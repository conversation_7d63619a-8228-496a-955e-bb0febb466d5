'use client';
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Button,
  Grid,
  CircularProgress,
  useTheme,
  useMediaQuery,
  Card,
  CardHeader,
  CardContent,
  Snackbar,
  Paper,
  Tab,
  Tabs,
  AppBar,
  Alert,
  Stepper,
  Step,
  StepLabel,
  Divider,
} from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import SaveIcon from '@mui/icons-material/Save';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import CheckIcon from '@mui/icons-material/Check';

// Import custom hook and components
import { useBookingForm } from './hooks/useBookingForm';
import GettingCustomerDetails from './components/GettingCustomerDetails';
import BookingTypeSection from './components/BookingTypeSection';
import BookingTimingSection from './components/BookingTimingSection';
import LocationSection from './components/LocationSection';
import CompanySelectionSection from './components/CompanySelectionSection';
import BranchSelectionSection from './components/BranchSelectionSection';
import CarSelectionSection from './components/CarSelectionSection';
import InsuranceSelectionSection from './components/InsuranceSelectionSection';
import ExtraServicesSection from './components/ExtraServicesSection';
import DeliverySection from './components/DeliverySection';
import MapSection from './components/MapSection';
import PaymentMethodSection from './components/PaymentMethodSection';
import FursanVerificationSection from './components/FursanVerificationSection';
import CouponSection from './components/CouponSection';
import RentToOwnPlansSection from './components/RentToOwnPlansSection';
import BookingFormSummary from './components/BookingFormSummary';
import NotesSection from './components/NotesSection';
import PriceSummary from './components/PriceSummary';

// Types
interface AddEditBookingContentProps {
  mode: 'add' | 'edit';
  bookingId?: string;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`booking-tabpanel-${index}`}
      aria-labelledby={`booking-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

// Step definitions
const getSteps = (t: any) => [
  t('customer.information'),
  t('booking.details'),
  t('vehicle.selection'),
  t('services.payment'),
  t('summary.confirmation'),
];

export default function AddEditBookingContent({ mode, bookingId }: AddEditBookingContentProps) {
  const { t, i18n } = useTranslation();
  const locale = i18n.language;
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const router = useRouter();

  // Tab state
  const [tabValue, setTabValue] = useState(0);
  const [activeStep, setActiveStep] = useState(0);

  // Snackbar state
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error',
  });

  // Use the custom hook
  const bookingForm = useBookingForm({ mode, bookingId });

  const steps = getSteps(t);

  // Validation for each step
  const isStepValid = (step: number): boolean => {
    switch (step) {
      case 0: // Customer information
        return !!bookingForm.customerDetails;
      case 1: // Booking details
        return !!(
          bookingForm.bookingType &&
          bookingForm.pickUpDate &&
          bookingForm.dropOffDate &&
          bookingForm.pickUpCity &&
          (bookingForm.isPickSameReturn || bookingForm.dropOffCity) &&
          bookingForm.selectedCompany &&
          bookingForm.selectedBranch
        );
      case 2: // Vehicle selection
        return !!bookingForm.selectedCar;
      case 3: // Services & Payment
        // More comprehensive validation for services & payment
        const hasRequiredPayment = !!bookingForm.paymentMethod;
        const hasRequiredInsurance = !!bookingForm.selectedInsurance;

        // If delivery is selected, ensure location is set
        const hasValidDelivery =
          !bookingForm.hasDelivery || (bookingForm.hasDelivery && !!bookingForm.deliveryLocation);

        return hasRequiredPayment && hasRequiredInsurance && hasValidDelivery;
      case 4: // Summary - in edit mode, allow access even if not fully valid
        return mode === 'edit' ? true : bookingForm.isFormValid();
      default:
        return false;
    }
  };

  // Handle next step
  const handleNext = () => {
    if (isStepValid(activeStep)) {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    } else {
      bookingForm.setClicked(true);
      setSnackbar({
        open: true,
        message: t('please.complete.required.fields'),
        severity: 'error',
      });
    }
  };

  // Handle back step
  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  // Handle form submission
  const handleSubmit = async () => {
    bookingForm.setLoading(true);
    bookingForm.setClicked(true);

    try {
      // Validate required fields
      if (!bookingForm.isFormValid()) {
        throw new Error(t('Please fill in all required fields'));
      }

      const variables = {
        carId: bookingForm.selectedCar?.id,
        pickUpDate: bookingForm.pickUpDate?.format('DD/MM/YYYY'),
        pickUpTime: bookingForm.pickUpDate?.format('HH:mm:ss'),
        dropOffDate: bookingForm.dropOffDate?.format('DD/MM/YYYY'),
        dropOffTime: bookingForm.dropOffDate?.format('HH:mm:ss'),
        pickUpCityId: bookingForm.selectedBranch?.cityId || bookingForm.pickUpCity?.id,
        dropOffCityId:
          bookingForm.selectedDropoffBranch?.cityId ||
          bookingForm.dropOffCity?.id ||
          bookingForm.selectedBranch?.cityId,
        pickUpBranchId: parseInt(bookingForm.selectedBranch?.id || '0'),
        dropOffBranchId: parseInt(
          bookingForm.selectedDropoffBranch?.id || bookingForm.selectedBranch?.id || '0'
        ),
        paymentMethod: bookingForm.paymentMethod,
        deliverType: bookingForm.hasDelivery ? bookingForm.deliveryType || 'one-way' : null,
        deliverAddress: bookingForm.deliveryLocation?.address,
        deliverLat: bookingForm.deliveryLocation?.lat,
        deliverLng: bookingForm.deliveryLocation?.lng,
        deliveryPrice: bookingForm.deliveryPrice || 0,
        handoverAddress:
          bookingForm.deliveryType === 'two-way' ? bookingForm.deliveryLocation?.address : null,
        handoverLat:
          bookingForm.deliveryType === 'two-way' ? bookingForm.deliveryLocation?.lat : null,
        handoverLng:
          bookingForm.deliveryType === 'two-way' ? bookingForm.deliveryLocation?.lng : null,
        handoverPrice: bookingForm.deliveryType === 'two-way' ? bookingForm.deliveryPrice : 0,
        suggestedPrice:
          bookingForm.useSuggestedPrice && bookingForm.suggestedPrice
            ? bookingForm.suggestedPrice
            : bookingForm.totalPrice,
        isUnlimited: bookingForm.unlimited,
        withInstallment: bookingForm.withInstallment,
        notes: bookingForm.notes,
        userId: parseInt(bookingForm.customerDetails?.id || '0'),
        branchId: parseInt(bookingForm.selectedBranch?.id || '0'),
        isRentToOwn: bookingForm.bookingType === 'rent-to-own',
        bookingType: bookingForm.bookingType,
        isTwoWays: bookingForm.deliveryType === 'two-way',
        extraServices: bookingForm.selectedExtraServices.map((service) => service.id),
        insuranceId: bookingForm.selectedInsurance?.id,
        carPlanId: bookingForm.selectedPlan?.id,
        couponId: bookingForm.appliedCoupon?.id,
        fursanMemberId: bookingForm.fursanMemberId,
      };

      let result: any;
      if (mode === 'add') {
        result = await bookingForm.createBooking({ variables });
        if (result.data?.createRental?.rental) {
          setSnackbar({
            open: true,
            message: t('success.create.rental'),
            severity: 'success',
          });
          setTimeout(() => {
            router.push(`/bookings/${result.data.createRental.rental.id}`);
          }, 2000);
        } else if (result.data?.createRental?.errors) {
          throw new Error(result.data.createRental.errors.join(', '));
        }
      } else {
        result = await bookingForm.editBooking({
          variables: {
            ...variables,
            rentalId: parseInt(bookingId!),
          },
        });
        if (result.data?.editRental?.rental) {
          setSnackbar({
            open: true,
            message: t('success.edit.rental'),
            severity: 'success',
          });
          setTimeout(() => {
            router.push(`/bookings/${bookingId}`);
          }, 2000);
        } else if (result.data?.editRental?.errors) {
          throw new Error(result.data.editRental.errors.join(', '));
        }
      }
    } catch (error: any) {
      setSnackbar({
        open: true,
        message: error.message || t('error.submitting.booking'),
        severity: 'error',
      });
    } finally {
      bookingForm.setLoading(false);
    }
  };

  if (bookingForm.bookingLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box sx={{ p: { xs: 1, sm: 2, md: 3 }, maxWidth: '100%' }}>
        {/* Header */}
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <Button
              variant="outlined"
              onClick={() => router.back()}
              startIcon={<ArrowBackIcon />}
              size="small"
            >
              {t('common.back')}
            </Button>
            <Typography variant="h5" component="h1">
              {mode === 'add' ? t('addBooking') : t('EditBooking')}
            </Typography>
          </Box>

          {/* Progress Stepper */}
          <Paper sx={{ p: 2, mb: 3 }}>
            <Stepper activeStep={activeStep} alternativeLabel={isMobile}>
              {steps.map((label, index) => (
                <Step key={label} completed={isStepValid(index)}>
                  <StepLabel
                    icon={isStepValid(index) ? <CheckIcon /> : index + 1}
                    sx={{
                      '& .MuiStepLabel-iconContainer': {
                        color: isStepValid(index) ? 'success.main' : 'default',
                      },
                    }}
                  >
                    {label}
                  </StepLabel>
                </Step>
              ))}
            </Stepper>
          </Paper>
        </Box>

        {/* Main Content */}
        <Grid container spacing={3}>
          <Grid item xs={12} lg={8}>
            {/* Step 0: Customer Information */}
            {activeStep === 0 && (
              <Box>
                <GettingCustomerDetails
                  setCustomerDetails={bookingForm.setCustomerDetails}
                  setCustomerId={bookingForm.setCustomerId}
                  setFursanChecked={bookingForm.setFursanChecked}
                  setFursanVerified={bookingForm.setFursanVerified}
                  customerDetails={bookingForm.customerDetails}
                  mode={mode}
                />
              </Box>
            )}

            {/* Step 1: Booking Details */}
            {activeStep === 1 && (
              <Box>
                {/* Booking Type Selection */}
                <BookingTypeSection
                  bookingType={bookingForm.bookingType}
                  withInstallment={bookingForm.withInstallment}
                  bookingId={bookingId}
                  pickUpDate={bookingForm.pickUpDate}
                  onBookingTypeChange={(type) => {
                    bookingForm.setBookingType(type);
                    // Handle booking type specific logic
                    switch (type) {
                      case 'daily':
                        bookingForm.setMonths('0');
                        break;
                      case 'monthly':
                        bookingForm.setMonths('3');
                        bookingForm.setDropOffDate(bookingForm.pickUpDate?.add(30 * 3, 'day'));
                        break;
                      default:
                        break;
                    }
                    // Clear selections that depend on booking type
                    bookingForm.setSelectedCompany(null);
                    bookingForm.setSelectedBranch(null);
                    bookingForm.setSelectedCar(null);
                  }}
                  onInstallmentChange={(checked) => {
                    bookingForm.setWithInstallment(checked);
                    if (checked) {
                      bookingForm.setDropOffDate(bookingForm.pickUpDate?.add(30 * 3, 'day'));
                    }
                  }}
                  onChanged={() => bookingForm.setChanged(true)}
                />

                {/* Booking Timing */}
                <BookingTimingSection
                  pickUpDate={bookingForm.pickUpDate}
                  dropOffDate={bookingForm.dropOffDate}
                  bookingType={bookingForm.bookingType}
                  months={bookingForm.months}
                  monthTime=""
                  plan={bookingForm.selectedPlan}
                  onPickUpDateChange={(date) => {
                    if (date) {
                      bookingForm.setPickUpDate(date);
                      bookingForm.setPickUpCity(null);
                      if (bookingForm.bookingType === 'rent-to-own' && bookingForm.selectedPlan) {
                        bookingForm.setDropOffDate(
                          date.add(bookingForm.selectedPlan.noOfMonths, 'month')
                        );
                      }
                    }
                  }}
                  onDropOffDateChange={(date) => {
                    if (date) {
                      bookingForm.setDropOffDate(date);
                    }
                  }}
                  onMonthsChange={bookingForm.setMonths}
                  onMonthTimeChange={() => {}}
                  onChanged={() => bookingForm.setChanged(true)}
                />

                {/* Location Section */}
                <LocationSection
                  areasData={bookingForm.areasData || []}
                  pickUpCity={bookingForm.pickUpCity}
                  dropOffCity={bookingForm.dropOffCity}
                  isPickSameReturn={bookingForm.isPickSameReturn}
                  isDelivery={bookingForm.hasDelivery}
                  gettingAreas={bookingForm.gettingAreas}
                  customerId={bookingForm.customerId}
                  mode={mode}
                  clicked={bookingForm.clicked}
                  locale={locale}
                  onPickUpCityChange={(city) => {
                    bookingForm.setPickUpCity(city);
                    bookingForm.setSelectedCompany(null);
                    bookingForm.setSelectedBranch(null);
                    bookingForm.setSelectedCar(null);
                    if (bookingForm.isPickSameReturn) {
                      bookingForm.setDropOffCity(city);
                    }
                  }}
                  onDropOffCityChange={(city) => {
                    bookingForm.setDropOffCity(city);
                    bookingForm.setSelectedCompany(null);
                    bookingForm.setSelectedBranch(null);
                    bookingForm.setSelectedCar(null);
                  }}
                  onIsPickSameReturnChange={(checked) => {
                    bookingForm.setIsPickSameReturn(checked);
                    if (checked) {
                      bookingForm.setDropOffCity(bookingForm.pickUpCity);
                    }
                  }}
                  onChanged={() => bookingForm.setChanged(true)}
                />

                {/* Company Selection */}
                <CompanySelectionSection
                  companies={bookingForm.companiesData}
                  selectedCompany={bookingForm.selectedCompany}
                  onCompanyChange={(company) => {
                    bookingForm.setSelectedCompany(company);
                    bookingForm.setSelectedBranch(null);
                    bookingForm.setSelectedCar(null);
                  }}
                  disabled={bookingForm.gettingCompanies}
                  clicked={bookingForm.clicked}
                  isLoading={bookingForm.gettingCompanies}
                />

                {/* Branch Selection - Pickup Only */}
                <BranchSelectionSection
                  branches={bookingForm.branchesData}
                  selectedPickupBranch={bookingForm.selectedBranch}
                  selectedDropoffBranch={null}
                  onPickupBranchChange={(branch) => {
                    bookingForm.setSelectedBranch(branch);
                    bookingForm.setSelectedCar(null);
                  }}
                  onDropoffBranchChange={() => {}}
                  disabled={bookingForm.gettingBranches}
                  clicked={bookingForm.clicked}
                  isLoading={bookingForm.gettingBranches}
                  showDropoffBranch={false}
                  showPickupBranch={true}
                  hasSelectedCompany={!!bookingForm.selectedCompany}
                />
              </Box>
            )}

            {/* Step 2: Vehicle Selection */}
            {activeStep === 2 && (
              <Box>
                {/* Car Selection */}
                <CarSelectionSection
                  cars={bookingForm.carsData}
                  selectedCar={bookingForm.selectedCar}
                  onCarChange={(car) => {
                    bookingForm.setSelectedCar(car);
                    bookingForm.setSelectedInsurance(null);
                    bookingForm.setSelectedPlan(null);
                  }}
                  disabled={bookingForm.gettingCars}
                  clicked={bookingForm.clicked}
                  isLoading={bookingForm.gettingCars}
                  hasSelectedBranch={!!bookingForm.selectedBranch}
                />

                {/* Rent-to-Own Plans */}
                <RentToOwnPlansSection
                  carPlans={bookingForm.selectedCar?.plans || []}
                  selectedPlan={bookingForm.selectedPlan}
                  onPlanChange={bookingForm.setSelectedPlan}
                  clicked={bookingForm.clicked}
                  bookingType={bookingForm.bookingType}
                  hasSelectedCar={!!bookingForm.selectedCar}
                />

                {/* Dropoff Branch Selection - Only if different from pickup */}
                {!bookingForm.isPickSameReturn && bookingForm.selectedCar && (
                  <BranchSelectionSection
                    branches={bookingForm.branchesData}
                    selectedPickupBranch={null}
                    selectedDropoffBranch={bookingForm.selectedDropoffBranch}
                    onPickupBranchChange={() => {}}
                    onDropoffBranchChange={bookingForm.setSelectedDropoffBranch}
                    disabled={bookingForm.gettingBranches}
                    clicked={bookingForm.clicked}
                    isLoading={bookingForm.gettingBranches}
                    showDropoffBranch={true}
                    showPickupBranch={false}
                    hasSelectedCompany={!!bookingForm.selectedCompany}
                  />
                )}
              </Box>
            )}

            {/* Step 3: Services & Payment */}
            {activeStep === 3 && (
              <Box>
                {/* Insurance Selection */}
                <InsuranceSelectionSection
                  insurances={bookingForm.insurancesData}
                  selectedInsurance={bookingForm.selectedInsurance}
                  onInsuranceChange={bookingForm.setSelectedInsurance}
                  disabled={bookingForm.gettingInsurances}
                  clicked={bookingForm.clicked}
                  isLoading={bookingForm.gettingInsurances}
                  hasSelectedCar={!!bookingForm.selectedCar}
                />

                {/* Extra Services */}
                <ExtraServicesSection
                  extraServices={[
                    ...(bookingForm.branchExtraServices || []),
                    ...(bookingForm.allyExtraServices || []),
                  ]}
                  selectedServices={bookingForm.selectedExtraServices}
                  onServicesChange={bookingForm.setSelectedExtraServices}
                  disabled={false}
                  clicked={bookingForm.clicked}
                  selectedCar={bookingForm.selectedCar}
                  unlimited={bookingForm.unlimited}
                  onUnlimitedChange={bookingForm.setUnlimited}
                />

                {/* Price Summary with Suggested Price */}
                <PriceSummary
                  bookingPriceData={bookingForm.bookingPriceData}
                  carData={bookingForm.selectedCar}
                  totalPrice={bookingForm.totalPrice}
                  suggestedPrice={bookingForm.suggestedPrice || 0}
                  onSuggestedPriceChange={bookingForm.setSuggestedPrice}
                  useSuggestedPrice={bookingForm.useSuggestedPrice}
                  onUseSuggestedPriceChange={bookingForm.setUseSuggestedPrice}
                  isEditMode={mode === 'edit'}
                />

                {/* Notes Section (only in edit mode) */}
                <NotesSection
                  notes={bookingForm.notes}
                  onNotesChange={bookingForm.setNotes}
                  disabled={false}
                  isEditMode={mode === 'edit'}
                />

                {/* Delivery Section */}
                <DeliverySection
                  isDelivery={bookingForm.hasDelivery}
                  isTwoWays={bookingForm.deliveryType === 'two-way'}
                  deliverAddress={bookingForm.deliveryAddress}
                  handoverAddress=""
                  deliverLat={bookingForm.deliveryLocation?.lat || 0}
                  deliverLng={bookingForm.deliveryLocation?.lng || 0}
                  handoverLat={0}
                  handoverLng={0}
                  deliveryPrice={bookingForm.deliveryPrice}
                  handoverPrice={bookingForm.handoverPrice}
                  onDeliveryChange={bookingForm.setHasDelivery}
                  onTwoWaysChange={(checked) =>
                    bookingForm.setDeliveryType(checked ? 'two-way' : 'one-way')
                  }
                  onDeliverAddressChange={bookingForm.setDeliveryAddress}
                  onHandoverAddressChange={() => {}}
                  onDeliverLocationChange={(lat, lng) => {
                    bookingForm.setDeliveryLocation({
                      lat,
                      lng,
                      address: bookingForm.deliveryAddress,
                    });
                  }}
                  onHandoverLocationChange={() => {}}
                  isPickSameReturn={bookingForm.isPickSameReturn}
                  clicked={bookingForm.clicked}
                />

                {/* Map Section */}
                <MapSection
                  location={bookingForm.deliveryLocation}
                  onLocationChange={bookingForm.setDeliveryLocation}
                  clicked={bookingForm.clicked}
                  showDelivery={bookingForm.hasDelivery}
                  deliveryType={bookingForm.deliveryType}
                />

                {/* Payment Method */}
                <PaymentMethodSection
                  paymentMethod={bookingForm.paymentMethod}
                  onPaymentMethodChange={bookingForm.setPaymentMethod}
                  clicked={bookingForm.clicked}
                />

                {/* Fursan Verification */}
                <FursanVerificationSection
                  fursanChecked={bookingForm.fursanChecked}
                  fursanVerified={bookingForm.fursanVerified}
                  fursanMemberId={bookingForm.fursanMemberId}
                  onFursanCheckedChange={bookingForm.setFursanChecked}
                  onFursanMemberIdChange={bookingForm.setFursanMemberId}
                  onFursanVerify={async (memberId: string) => {
                    // Handle Fursan verification logic - return true if verified
                    bookingForm.setFursanMemberId(memberId);
                    bookingForm.setFursanVerified(true);
                    return true;
                  }}
                  paymentMethod={bookingForm.paymentMethod}
                  clicked={bookingForm.clicked}
                />

                {/* Coupon Section */}
                <CouponSection
                  couponCode={bookingForm.couponCode}
                  couponAvailability={
                    bookingForm.couponAvailability
                      ? {
                          id: bookingForm.appliedCoupon?.id || '',
                          code: bookingForm.couponCode,
                          isValid: true,
                          discountType: 'FIXED' as const,
                          discountValue: bookingForm.discountAmount || 0,
                          message: 'Coupon applied successfully',
                        }
                      : null
                  }
                  onCouponCodeChange={bookingForm.setCouponCode}
                  onCouponValidate={async (code: string, carId: string) => {
                    // Handle coupon validation logic
                    bookingForm.validateCoupon();
                    return {
                      id: '1',
                      code,
                      isValid: true,
                      discountType: 'FIXED' as const,
                      discountValue: 50,
                      message: 'Coupon applied successfully',
                    };
                  }}
                  selectedCarId={bookingForm.selectedCar?.id}
                />
              </Box>
            )}

            {/* Step 4: Summary & Confirmation */}
            {activeStep === 4 && (
              <Box>
                <BookingFormSummary
                  customer={
                    bookingForm.customerDetails
                      ? {
                          id: bookingForm.customerDetails.id,
                          fullName: bookingForm.customerDetails.name,
                          phoneNumber: bookingForm.customerDetails.mobile,
                          email: bookingForm.customerDetails.email,
                          nationalId: bookingForm.customerDetails.nationalId,
                        }
                      : null
                  }
                  bookingType={bookingForm.bookingType}
                  startDate={bookingForm.pickUpDate?.toDate() || null}
                  endDate={bookingForm.dropOffDate?.toDate() || null}
                  startTime={bookingForm.pickUpDate?.format('HH:mm') || ''}
                  endTime={bookingForm.dropOffDate?.format('HH:mm') || ''}
                  selectedCompany={
                    bookingForm.selectedCompany
                      ? {
                          id: bookingForm.selectedCompany.id || bookingForm.selectedCompany.value,
                          name:
                            bookingForm.selectedCompany.enName || bookingForm.selectedCompany.label,
                          arName:
                            bookingForm.selectedCompany.arName || bookingForm.selectedCompany.label,
                        }
                      : null
                  }
                  pickupBranch={
                    bookingForm.selectedBranch
                      ? {
                          id: bookingForm.selectedBranch.id || bookingForm.selectedBranch.value,
                          name:
                            bookingForm.selectedBranch.enName || bookingForm.selectedBranch.label,
                          arName:
                            bookingForm.selectedBranch.arName || bookingForm.selectedBranch.label,
                          address: bookingForm.selectedBranch.address || '',
                        }
                      : null
                  }
                  dropoffBranch={
                    bookingForm.selectedDropoffBranch
                      ? {
                          id:
                            bookingForm.selectedDropoffBranch.id ||
                            bookingForm.selectedDropoffBranch.value,
                          name:
                            bookingForm.selectedDropoffBranch.enName ||
                            bookingForm.selectedDropoffBranch.label,
                          arName:
                            bookingForm.selectedDropoffBranch.arName ||
                            bookingForm.selectedDropoffBranch.label,
                          address: bookingForm.selectedDropoffBranch.address || '',
                        }
                      : null
                  }
                  selectedCar={
                    bookingForm.selectedCar
                      ? {
                          id: bookingForm.selectedCar.id,
                          name: `${bookingForm.selectedCar.make?.enName || ''} ${
                            bookingForm.selectedCar.carModel?.enName || ''
                          }`,
                          model: bookingForm.selectedCar.carModel?.enName || '',
                          year: bookingForm.selectedCar.year || 0,
                          plateNumber: bookingForm.selectedCar.plateNumber || '',
                          color: bookingForm.selectedCar.color || '',
                          dailyPrice: bookingForm.selectedCar.dailyPrice || 0,
                        }
                      : null
                  }
                  selectedInsurance={
                    bookingForm.selectedInsurance
                      ? {
                          id: bookingForm.selectedInsurance.id,
                          name:
                            bookingForm.selectedInsurance.insuranceName ||
                            bookingForm.selectedInsurance.name,
                          price: bookingForm.selectedInsurance.price || 0,
                          coverage: bookingForm.selectedInsurance.coverage || '',
                        }
                      : null
                  }
                  selectedExtraServices={bookingForm.selectedExtraServices.map((service) => ({
                    id: service.id,
                    name:
                      service.extraService?.enTitle ||
                      service.enTitle ||
                      service.title ||
                      service.name ||
                      'Unknown Service',
                    price: service.price || 0,
                  }))}
                  hasDelivery={bookingForm.hasDelivery}
                  deliveryType={bookingForm.deliveryType}
                  deliveryLocation={bookingForm.deliveryLocation}
                  paymentMethod={bookingForm.paymentMethod}
                  selectedPlan={bookingForm.selectedPlan}
                  appliedCoupon={
                    bookingForm.appliedCoupon
                      ? {
                          id: bookingForm.appliedCoupon.id,
                          code: bookingForm.couponCode,
                          discount: bookingForm.discountAmount || 0,
                          discountType: 'fixed' as const,
                        }
                      : null
                  }
                  basePrice={bookingForm.basePrice || 0}
                  totalPrice={bookingForm.totalPrice || 0}
                  discountAmount={bookingForm.discountAmount || 0}
                  fursanMemberId={bookingForm.fursanMemberId}
                />
              </Box>
            )}

            {/* Navigation Buttons */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4, gap: 2 }}>
              <Button disabled={activeStep === 0} onClick={handleBack} variant="outlined">
                {t('common.back')}
              </Button>

              <Box sx={{ display: 'flex', gap: 2 }}>
                {activeStep < steps.length - 1 ? (
                  <Button
                    variant="contained"
                    onClick={handleNext}
                    disabled={!isStepValid(activeStep)}
                  >
                    {t('common.next')}
                  </Button>
                ) : (
                  <Button
                    variant="contained"
                    color="primary"
                    size="large"
                    onClick={handleSubmit}
                    disabled={
                      !bookingForm.isFormValid() ||
                      bookingForm.loading ||
                      bookingForm.creatingBooking ||
                      bookingForm.editingBooking
                    }
                    startIcon={
                      bookingForm.loading ||
                      bookingForm.creatingBooking ||
                      bookingForm.editingBooking ? (
                        <CircularProgress size={20} />
                      ) : (
                        <SaveIcon />
                      )
                    }
                  >
                    {bookingForm.loading ||
                    bookingForm.creatingBooking ||
                    bookingForm.editingBooking
                      ? t('common.saving')
                      : bookingId
                      ? t('button.save')
                      : t('Rent')}
                  </Button>
                )}
              </Box>
            </Box>
          </Grid>

          {/* Right Column - Customer & Price Details */}
          <Grid item xs={12} lg={4}>
            <Box sx={{ position: 'sticky', top: 20 }}>
              {/* Customer Details Card */}
              <Card sx={{ mb: 3 }}>
                <CardHeader title={t('rental.customer.details')} />
                <CardContent>
                  {bookingForm.customerDetails ? (
                    <Box>
                      <Typography variant="body1" gutterBottom>
                        <strong>{t('customerName')}:</strong> {bookingForm.customerDetails.name}
                      </Typography>
                      <Typography variant="body1" gutterBottom>
                        <strong>{t('customerMobile')}:</strong> {bookingForm.customerDetails.mobile}
                      </Typography>
                      {bookingForm.customerDetails.email && (
                        <Typography variant="body1" gutterBottom>
                          <strong>{t('customerEmail')}:</strong> {bookingForm.customerDetails.email}
                        </Typography>
                      )}
                    </Box>
                  ) : (
                    <Typography variant="body2" color="textSecondary">
                      {t('customerDetailsWillBeDisplayedHere')}
                    </Typography>
                  )}
                </CardContent>
              </Card>

              {/* Price Summary Card */}
              <Card>
                <CardHeader title={t('price.summary')} />
                <CardContent>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    {bookingForm.selectedCar && bookingForm.bookingPriceData?.aboutRentPrice && (
                      <>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2">{t('car.rental')}:</Typography>
                          <Typography variant="body2">
                            {bookingForm.bookingPriceData.aboutRentPrice.priceBeforeInsurance || 0}{' '}
                            {t('currency.sr')}
                          </Typography>
                        </Box>

                        {bookingForm.bookingPriceData.aboutRentPrice.insuranceValue > 0 && (
                          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Typography variant="body2">{t('insurance')}:</Typography>
                            <Typography variant="body2">
                              {bookingForm.bookingPriceData.aboutRentPrice.insuranceValue}{' '}
                              {t('currency.sr')}
                            </Typography>
                          </Box>
                        )}

                        {bookingForm.bookingPriceData.aboutRentPrice.totalExtraServicesPrice >
                          0 && (
                          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Typography variant="body2">{t('extra.services')}:</Typography>
                            <Typography variant="body2">
                              {bookingForm.bookingPriceData.aboutRentPrice.totalExtraServicesPrice}{' '}
                              {t('currency.sr')}
                            </Typography>
                          </Box>
                        )}

                        {bookingForm.bookingPriceData.aboutRentPrice.deliveryPrice > 0 && (
                          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Typography variant="body2">{t('delivery')}:</Typography>
                            <Typography variant="body2">
                              {bookingForm.bookingPriceData.aboutRentPrice.deliveryPrice}{' '}
                              {t('currency.sr')}
                            </Typography>
                          </Box>
                        )}

                        {bookingForm.bookingPriceData.aboutRentPrice.handoverPrice > 0 && (
                          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Typography variant="body2">{t('handover')}:</Typography>
                            <Typography variant="body2">
                              {bookingForm.bookingPriceData.aboutRentPrice.handoverPrice}{' '}
                              {t('currency.sr')}
                            </Typography>
                          </Box>
                        )}

                        {bookingForm.bookingPriceData.aboutRentPrice.totalUnlimitedFee > 0 && (
                          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Typography variant="body2">{t('unlimited.km')}:</Typography>
                            <Typography variant="body2">
                              {bookingForm.bookingPriceData.aboutRentPrice.totalUnlimitedFee}{' '}
                              {t('currency.sr')}
                            </Typography>
                          </Box>
                        )}

                        {bookingForm.bookingPriceData.aboutRentPrice.taxValue > 0 && (
                          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Typography variant="body2">
                              {t('vat')} (
                              {bookingForm.bookingPriceData.aboutRentPrice.valueAddedTaxPercentage}
                              %):
                            </Typography>
                            <Typography variant="body2">
                              {bookingForm.bookingPriceData.aboutRentPrice.taxValue}{' '}
                              {t('currency.sr')}
                            </Typography>
                          </Box>
                        )}

                        {(bookingForm.bookingPriceData.aboutRentPrice.couponDiscount > 0 ||
                          bookingForm.bookingPriceData.aboutRentPrice.discountValue > 0) && (
                          <Box
                            sx={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              color: 'success.main',
                            }}
                          >
                            <Typography variant="body2">{t('discount')}:</Typography>
                            <Typography variant="body2">
                              -
                              {bookingForm.bookingPriceData.aboutRentPrice.couponDiscount ||
                                bookingForm.bookingPriceData.aboutRentPrice.discountValue ||
                                0}{' '}
                              {t('currency.sr')}
                            </Typography>
                          </Box>
                        )}

                        <Divider />

                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="h6" color="primary">
                            {t('total')}:
                          </Typography>
                          <Typography variant="h6" color="primary">
                            {bookingForm.bookingPriceData.aboutRentPrice.totalAmountDue || 0}{' '}
                            {t('currency.sr')}
                          </Typography>
                        </Box>
                      </>
                    )}

                    {!bookingForm.selectedCar && (
                      <Typography variant="body2" color="textSecondary" textAlign="center">
                        {t('select.car.to.see.pricing')}
                      </Typography>
                    )}

                    {bookingForm.selectedCar && !bookingForm.bookingPriceData?.aboutRentPrice && (
                      <Typography variant="body2" color="textSecondary" textAlign="center">
                        {t('calculating.price')}...
                      </Typography>
                    )}
                  </Box>
                </CardContent>
              </Card>
            </Box>
          </Grid>
        </Grid>

        {/* Snackbar for notifications */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={() => setSnackbar({ ...snackbar, open: false })}
        >
          <Alert
            onClose={() => setSnackbar({ ...snackbar, open: false })}
            severity={snackbar.severity}
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </LocalizationProvider>
  );
}
