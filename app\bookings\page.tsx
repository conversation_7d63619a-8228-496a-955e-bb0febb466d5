'use client';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import BookingsContent from './BookingsContent';
import { getAuthToken } from '../utils/cookies';
import PageLayout from '../PageLayout';

export default function Bookings() {
  const router = useRouter();

  useEffect(() => {
    // Check for token in cookies
    const token = getAuthToken();
    if (!token) {
      router.replace('/login');
    }
  }, [router]);

  return (
    <PageLayout>
      <BookingsContent />
    </PageLayout>
  );
}
