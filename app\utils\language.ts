/**
 * Gets the current language from localStorage
 * @returns {string} - The current language code (default: 'en')
 */
export const getCurrentLanguage = (): string => {
  if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
    return 'en';
  }

  try {
    const localeStr = localStorage.getItem('locale');
    if (!localeStr) return 'en';

    const locale = JSON.parse(localeStr);
    return locale?.locale || 'en';
  } catch (error) {
    console.error('Error parsing locale from localStorage:', error);
    return 'en';
  }
};
