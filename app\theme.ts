import { createTheme, PaletteMode } from '@mui/material';

const useTheme = (mode: PaletteMode = 'light', highContrast: boolean = false) => {
  // Define colors for light and dark modes
  const lightColors = {
    primary: {
      main: '#f19223',
      light: '#f19223',
      dark: '#f19223',
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#f19223',
      light: '#f19223',
      dark: '#f19223',
      contrastText: '#ffffff',
    },
    success: {
      main: '#4caf50',
      light: '#80e27e',
      dark: '#087f23',
      contrastText: '#ffffff',
    },
    warning: {
      main: '#ff9800',
      light: '#ffc947',
      dark: '#c66900',
      contrastText: '#000000',
    },
    error: {
      main: '#f44336',
      light: '#ff7961',
      dark: '#ba000d',
      contrastText: '#ffffff',
    },
    info: {
      main: '#2196f3',
      light: '#2196f3',
      dark: '#0069c0',
      contrastText: '#ffffff',
    },
    text: {
      primary: '#333333',
      secondary: '#666666',
      disabled: '#999999',
    },
    background: {
      default: '#f7f8f7',
      paper: '#ffffff',
    },
    divider: 'rgba(0, 0, 0, 0.12)',
  };

  const darkColors = {
    primary: {
      main: '#f19223', // Keep consistent with light mode
      light: '#ffb151',
      dark: '#c67300',
      contrastText: '#f0f0f0', // Softer white text on orange for better contrast
    },
    secondary: {
      main: '#f19223', // Keep consistent with light mode
      light: '#ffb151',
      dark: '#c67300',
      contrastText: '#f0f0f0', // Softer white text for better contrast
    },
    success: {
      main: '#4caf50', // Green for dark mode
      light: '#81c784',
      dark: '#388e3c',
      contrastText: '#f0f0f0', // Softer white text for better contrast
    },
    warning: {
      main: '#ff9800', // Amber for dark mode
      light: '#ffb74d',
      dark: '#f57c00',
      contrastText: '#000000', // Black text on amber
    },
    error: {
      main: '#f44336', // Red for dark mode
      light: '#e57373',
      dark: '#d32f2f',
      contrastText: '#f0f0f0', // Softer white text for better contrast
    },
    info: {
      main: '#29b6f6', // Light blue for dark mode
      light: '#4fc3f7',
      dark: '#0288d1',
      contrastText: '#f0f0f0', // Softer white text for better contrast
    },
    text: {
      primary: '#f0f0f0', // Softer white for better eye comfort
      secondary: '#b0b0b0',
      disabled: '#757575',
    },
    background: {
      default: '#1a1a1a', // Slightly lighter for better contrast
      paper: '#242424', // Slightly lighter for better contrast
    },
    divider: 'rgba(255, 255, 255, 0.12)',
  };

  // High contrast mode colors
  const highContrastLightColors = {
    ...lightColors,
    text: {
      primary: '#000000',
      secondary: '#000000',
      disabled: '#555555',
    },
    background: {
      default: '#ffffff',
      paper: '#ffffff',
    },
  };

  const highContrastDarkColors = {
    ...darkColors,
    text: {
      primary: '#f0f0f0', // Softer white for better eye comfort
      secondary: '#e0e0e0', // Slightly dimmer for secondary text
      disabled: '#cccccc',
    },
    background: {
      default: '#000000',
      paper: '#181818',
    },
  };

  // Select the appropriate color palette based on mode and high contrast setting
  let colors;
  if (mode === 'dark') {
    colors = highContrast ? highContrastDarkColors : darkColors;
  } else {
    colors = highContrast ? highContrastLightColors : lightColors;
  }

  return createTheme({
    palette: {
      mode,
      ...colors,
    },
    typography: {
      fontFamily: '"Helvetica", "Arial", sans-serif',
      h1: {
        fontSize: '2rem',
        fontWeight: 600,
      },
      h5: {
        fontSize: '1.25rem',
        fontWeight: 600,
      },
    },
    components: {
      MuiButton: {
        styleOverrides: {
          root: ({ theme }) => ({
            textTransform: 'none',
            borderRadius: 8,
            '&.MuiButtonBase-root': {
              color: theme.palette.mode === 'dark' ? '#f0f0f0' : '#ffffff',
            },
            '&.MuiButton-outlined': {
              color: theme.palette.mode === 'dark' ? '#f0f0f0' : '#f19223',
            },
          }),
        },
      },
      MuiFormLabel: {
        styleOverrides: {
          root: {
            width: '100%',
            '&.MuiInputLabel-shrink': {
              width: 'auto',
            },
            '&.Mui-focused': {
              textAlign: 'left',
            },
          },
        },
      },
      MuiTextField: {
        styleOverrides: {
          root: {
            '& .MuiOutlinedInput-root': {
              borderRadius: 8,
            },
          },
        },
      },
      MuiTableCell: {
        styleOverrides: {
          root: ({ theme }) => ({
            borderColor:
              theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.12)',
            padding: '8px 16px',
          }),
          head: ({ theme }) => ({
            backgroundColor: theme.palette.mode === 'dark' ? '#2c2c2c' : '#f5f5f5',
            color: theme.palette.mode === 'dark' ? '#f0f0f0' : '#333333',
            fontWeight: 600,
            padding: '4px 16px', // Reduced vertical padding
            height: '40px', // Fixed smaller height
            whiteSpace: 'nowrap', // Prevent text wrapping
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            fontSize: '0.875rem', // Slightly smaller font size
            lineHeight: '1.2', // Tighter line height
          }),
        },
      },
      MuiTableRow: {
        styleOverrides: {
          root: ({ theme }) => ({
            '&:hover': {
              backgroundColor:
                theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.04)' : 'rgba(0, 0, 0, 0.04)',
            },
          }),
        },
      },
      MuiPaper: {
        styleOverrides: {
          root: () => ({
            backgroundImage: 'none',
          }),
        },
      },
      MuiChip: {
        styleOverrides: {
          root: ({ theme }) => ({
            '&.MuiChip-colorDefault': {
              backgroundColor:
                theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.08)',
            },
          }),
        },
      },
    },
  });
};

export default useTheme;
