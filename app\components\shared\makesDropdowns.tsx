import React from 'react';
import { Autocomplete, TextField, Chip, CircularProgress } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@apollo/client';
import { GetMakes } from '../../gql/queries/makes';

interface Make {
  id: number | string;
  name: string;
  arName: string;
}

interface MakesDropdownProps {
  filters: {
    makeName?: string[] | null;
    [key: string]: any;
  };
  handleAutocompleteChange: (field: string, value: string | null) => void;
}

const MakesDropdown: React.FC<MakesDropdownProps> = ({ filters, handleAutocompleteChange }) => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';

  const { loading, error, data } = useQuery(GetMakes, {
    fetchPolicy: 'cache-and-network',
    variables: {
      limit: 1000,
    },
  });

  const makes = data?.makes?.collection || [];
  return (
    <Autocomplete
      id="makeId"
      options={makes}
      loading={loading}
      getOptionLabel={(option) => (isRTL ? option.arName : option.name)}
      value={
        filters.makeId && filters.makeId.length > 0
          ? makes.find((make: any) => make.id.toString() === filters.makeId) || null
          : null
      }
      onChange={(_, newValue) =>
        handleAutocompleteChange('makeId', newValue?.id?.toString() || null)
      }
      renderInput={(params) => (
        <TextField
          {...params}
          label={t('Car Make')}
          variant="outlined"
          size="small"
          InputLabelProps={{
            sx: { padding: '0 20px' },
          }}
          InputProps={{
            ...params.InputProps,
            endAdornment: (
              <>
                {loading ? <CircularProgress color="inherit" size={20} /> : null}
                {params.InputProps.endAdornment}
              </>
            ),
          }}
          error={!!error}
          helperText={error ? t('Error loading makes') : ''}
        />
      )}
      renderTags={(value, getTagProps) =>
        value.map((option, index) => (
          <Chip
            label={isRTL ? option.arName : option.name}
            {...getTagProps({ index })}
            size="small"
          />
        ))
      }
    />
  );
};

export default MakesDropdown;
