import React from 'react';
import { Autocomplete, TextField, Chip, CircularProgress } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@apollo/client';
import { GET_VEHICLES } from '../../gql/queries/vehicles';
import { RentTypeList } from './constants';



interface RentTypeProps {
  filters: {
    rentType?: string | null;
    [key: string]: any;
  };
  handleAutocompleteChange: (field: string, value: string | null) => void;
}

const RentType: React.FC<RentTypeProps> = ({ 
  filters,
  handleAutocompleteChange,
}) => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  


  return (
    <Autocomplete 
      id="rentType" 
      options={RentTypeList(t)} 
      getOptionLabel={(option) => (isRTL ? option.label : option.label)} 

      value={ 
        filters.rentType 
          ? RentTypeList(t).find((ally:any) => ally.value.toString() === filters.rentType) || null 
          : null 
      } 
      onChange={(_, newValue) => 
        handleAutocompleteChange('rentType', newValue?.value?.toString() || null) 
      } 
      renderInput={(params) => ( 
        <TextField 
          {...params} 
          label={t('rentType')} 
          variant="outlined" 
          size="small" 
          InputLabelProps={{ 
            sx: { padding: '0 20px' }, 
          }}
          InputProps={{
            ...params.InputProps,
            endAdornment: (
              <>
                {params.InputProps.endAdornment}
              </>
            ),
          }}
        /> 
      )} 
      renderTags={(value, getTagProps) => 
        value.map((option, index) => ( 
          <Chip 
            label={isRTL ? option.label : option.label} 
            {...getTagProps({ index })} 
            size="small" 
          /> 
        )) 
      }
    />
  );
};

export default RentType;