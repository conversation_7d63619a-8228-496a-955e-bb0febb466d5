import { gql } from "@apollo/client";

export const CustomerStatistics_Query = gql`
  query CustomerStatistics {
    customerStatistics {
      count
      month
    }
  }
`;

export const RentalsPerMonth_Query = gql`
  query RentalsPerMonth {
    rentalsPerMonth {
      months
    }
  }
`;

export const RentalsCount_Query = gql`
  query RentalsCount($userId: ID) {
    rentalsCount(userId: $userId) {
      all
    }
  }
`;
