'use client';
import { ApolloClient, InMemoryCache, HttpLink, from, ApolloLink } from '@apollo/client';
import { onError } from '@apollo/client/link/error';
import { v4 as uuidv4 } from 'uuid';
import { getAuthToken, getCookie } from '../utils/cookies';
import { getCurrentLanguage } from '../utils/language';
import { showErrorToast, showGraphQLErrorToast } from '../utils/toast';
import { isNetworkOnline, shouldThrottleRequest, getThrottledErrorMessage } from '../utils/network';
import { signatureGenerator } from '../utils/signature';

// List of error messages that shouldn't be shown as toasts
const ignoreErrorMessages = [
  'token_blacklisted',
  'Failed to fetch', // Common fetch error that will be handled by the network error display
];

// List of operations that shouldn't show error toasts
const silentOperations = [
  'login', // Login errors are handled separately in the login form
  'checkAuth', // Auth check errors should be silent
];

// Track retry attempts for operations
const retryAttempts: Record<string, number> = {};

/**
 * Determines if an error should be displayed as a toast
 */
const shouldShowErrorToast = (operation: any, error: any): boolean => {
  const operationName = operation?.operationName || '';

  // Don't show errors for silent operations
  if (silentOperations.includes(operationName)) {
    return false;
  }

  // Check for ignored error messages
  if (error.message && ignoreErrorMessages.some((msg) => error.message.includes(msg))) {
    return false;
  }

  return true;
};

export default function useApolloClient() {
  // Get auth token and language preference
  const token = getAuthToken();
  const language = getCurrentLanguage();

  // Error handling link
  const errorLink = onError(({ graphQLErrors, networkError, operation }) => {
    if (graphQLErrors) {
      graphQLErrors.forEach((error) => {
        // Handle specific errors
        if (error.message === 'token_blacklisted') {
          // Redirect to login if token is blacklisted
          window.location.href = `/login?from=${window.location.pathname}`;
          return; // Don't show toast for this error
        }

        // Show toast for other GraphQL errors if they should be displayed
        if (shouldShowErrorToast(operation, error)) {
          showGraphQLErrorToast(error);
        }
      });
    }

    if (networkError) {
      // Log for debugging purposes
      console.log(`[Network error in ${operation.operationName || 'anonymous'}]:`, networkError);

      // Show toast for network errors if they should be displayed
      if (shouldShowErrorToast(operation, networkError)) {
        showErrorToast(networkError, 'login.networkError');
      }
    }
  });

  // Network and throttling middleware
  const networkMiddleware = new ApolloLink((operation, forward) => {
    const operationName = operation.operationName || '';

    // Check if we're online
    if (!isNetworkOnline()) {
      showErrorToast(new Error('Network is offline'), 'login.networkOffline');
      return forward(operation);
    }

    // Check for throttling
    if (shouldThrottleRequest(operationName)) {
      console.log('Request throttled', getThrottledErrorMessage());
      return forward(operation);
    }

    return forward(operation);
  });

  // Auth header middleware
  const authMiddleware = new ApolloLink((operation, forward) => {
    // Get fresh token for each request
    const currentToken = getAuthToken();
    const currentLanguage = getCurrentLanguage();
    // Generate request metadata
    const date = new Date();
    const timestamp = date.toISOString();
    const nonce = uuidv4();

    // Generate signature for the request
    const signature = signatureGenerator({
      queryName: operation.operationName || '',
      variables: operation.variables,
      nonce,
      timestamp,
    });

    // Add headers to the request
    operation.setContext(({ headers = {} }) => {
      return {
        headers: {
          ...headers,
          'x-platform': 'web',
          'x-timestamp': timestamp,
          'x-nonce': nonce,
          'x-signature': signature,
          'x-operation-name': operation.operationName || '',
          Authorization: currentToken ? `Bearer ${currentToken}` : '',
          'accept-language': currentLanguage || 'en',
          'access-allow-origin': '*',
        },
      };
    });

    return forward(operation);
  });

  // HTTP link with base headers
  const httpLink = new HttpLink({
    uri: process.env.NEXT_PUBLIC_API_URL || 'https://prebeta.carwah.co/graphql',
  });

  // Create Apollo client with a full middleware stack
  const client = new ApolloClient({
    link: from([errorLink, networkMiddleware, authMiddleware, httpLink]),
    cache: new InMemoryCache(),
    defaultOptions: {
      query: {
        fetchPolicy: 'network-only',
        errorPolicy: 'all',
      },
      mutate: {
        fetchPolicy: 'network-only',
        errorPolicy: 'all',
      },
      watchQuery: {
        fetchPolicy: 'network-only',
        errorPolicy: 'all',
      },
    },
  });

  return { client };
}
