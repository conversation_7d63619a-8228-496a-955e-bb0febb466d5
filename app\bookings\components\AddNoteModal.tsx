'use client';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation } from '@apollo/client';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Alert,
  CircularProgress,
  Snackbar,
} from '@mui/material';
import { ADD_RENTAL_NOTE_MUTATION } from '../../gql/mutations/bookings';

interface AddNoteModalProps {
  open: boolean;
  onClose: () => void;
  bookingId: string;
  onNoteAdded: () => void;
}

export default function AddNoteModal({ open, onClose, bookingId, onNoteAdded }: AddNoteModalProps) {
  const { t } = useTranslation();
  const [note, setNote] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [showSuccess, setShowSuccess] = useState<boolean>(false);

  const [customerUpdateRentalNote] = useMutation(ADD_RENTAL_NOTE_MUTATION);

  const handleSubmit = async () => {
    if (!note?.trim()) {
      setError(t('bookings.notes.validation.contentRequired'));
      return;
    }

    setLoading(true);
    setError('');

    try {
      const result = await customerUpdateRentalNote({
        variables: {
          rentalId: bookingId,
          note: note.trim(),
        },
      });

      if (!result?.data?.customerUpdateRentalNote?.errors?.length) {
        setShowSuccess(true);
        setTimeout(() => {
          onNoteAdded();
          handleClose();
        }, 1500); // Show success message for 1.5 seconds before closing
      } else {
        setError(
          result.data.customerUpdateRentalNote.errors[0] || t('bookings.notes.errors.failedToAdd')
        );
      }
    } catch (err: any) {
      console.error('Error adding note:', err);
      setError(err.message || t('bookings.notes.errors.generalError'));
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setNote('');
    setError('');
    setLoading(false);
    setShowSuccess(false);
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>{t('bookings.notes.addNote.title')}</DialogTitle>

      <DialogContent>
        <Box sx={{ mt: 2 }}>
          <TextField
            fullWidth
            multiline
            rows={4}
            label={t('bookings.notes.addNote.content')}
            value={note}
            onChange={(e) => {
              setNote(e.target.value);
              setError('');
            }}
            error={!!error}
            placeholder={t('bookings.notes.addNote.placeholder')}
            variant="outlined"
          />

          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}
        </Box>
      </DialogContent>

      <DialogActions>
        <Button
          onClick={handleClose}
          disabled={loading}
          sx={{
            backgroundColor: '#f44336',
            color: 'white',
            '&:hover': {
              backgroundColor: '#d32f2f',
            },
            '&:disabled': {
              backgroundColor: '#cccccc',
              color: '#666666',
            },
          }}
        >
          {t('common.cancel')}
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={loading || !note?.trim()}
          sx={{
            backgroundColor: '#4caf50',
            color: 'white',
            '&:hover': {
              backgroundColor: '#388e3c',
            },
            '&:disabled': {
              backgroundColor: '#cccccc',
              color: '#666666',
            },
          }}
        >
          {loading ? <CircularProgress size={20} /> : t('bookings.notes.add')}
        </Button>
      </DialogActions>

      <Snackbar
        open={showSuccess}
        autoHideDuration={1500}
        onClose={() => setShowSuccess(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert severity="success" onClose={() => setShowSuccess(false)}>
          {t('bookings.notes.noteAddedSuccessfully')}
        </Alert>
      </Snackbar>
    </Dialog>
  );
}
