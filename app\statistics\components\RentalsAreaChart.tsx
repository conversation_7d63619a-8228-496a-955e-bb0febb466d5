'use client';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme, Box } from '@mui/material';
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from 'recharts';

interface RentalsAreaChartProps {
  labels: string[];
  rentals: number[];
  rentToOwn: number[];
}

export default function RentalsAreaChart({ labels, rentals, rentToOwn }: RentalsAreaChartProps) {
  const { t } = useTranslation();
  const theme = useTheme();
  const [chartData, setChartData] = useState<any[]>([]);

  useEffect(() => {
    if (labels.length > 0 && rentals.length > 0 && rentToOwn.length > 0) {
      const data = labels.map((month, index) => ({
        name: month,
        [t('Rentals')]: rentals[index],
        [t('Rent to Own')]: rentToOwn[index],
      }));
      setChartData(data);
    }
  }, [labels, rentals, rentToOwn, t]);

  if (chartData.length === 0) {
    return <div>No data available</div>;
  }

  return (
    <Box
      sx={{ width: '100%', height: '100%' }}
      role="img"
      aria-label={t('Area chart showing rentals over time')}
    >
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart
          data={chartData}
          margin={{
            top: 10,
            right: 30,
            left: 0,
            bottom: 0,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
          <XAxis
            dataKey="name"
            tick={{ fill: theme.palette.text.secondary }}
            axisLine={{ stroke: theme.palette.divider }}
          />
          <YAxis
            tick={{ fill: theme.palette.text.secondary }}
            axisLine={{ stroke: theme.palette.divider }}
          />
          <Tooltip
            contentStyle={{
              backgroundColor: theme.palette.background.paper,
              border: `1px solid ${theme.palette.divider}`,
              borderRadius: 8,
              color: theme.palette.text.primary,
            }}
            labelStyle={{
              color: theme.palette.text.primary,
              fontWeight: 'bold',
              marginBottom: '5px',
            }}
            itemStyle={{
              color: theme.palette.text.primary,
            }}
          />
          <Legend
            wrapperStyle={{
              paddingTop: '10px',
            }}
            formatter={(value) => {
              return <span style={{ color: theme.palette.text.primary }}>{value}</span>;
            }}
          />
          <Area
            type="monotone"
            dataKey={t('Rentals')}
            stackId="1"
            stroke={theme.palette.primary.main}
            fill={`${theme.palette.primary.main}80`}
          />
          <Area
            type="monotone"
            dataKey={t('Rent to Own')}
            stackId="1"
            stroke={theme.palette.warning.main}
            fill={`${theme.palette.warning.main}80`}
          />
        </AreaChart>
      </ResponsiveContainer>
    </Box>
  );
}
