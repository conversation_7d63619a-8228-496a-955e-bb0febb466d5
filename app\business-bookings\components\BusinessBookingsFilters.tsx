'use client';
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@apollo/client';
import {
  Box,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete,
  Typography,
  Divider,
  useTheme,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';
import dayjs, { Dayjs } from 'dayjs';
import { BusinessBookingFilters } from '../../models/businessBookings.model';
import { GET_ALL_AREAS } from '../../gql/queries/areas';
import { GET_ALL_MAKES } from '../../gql/queries/makes';
import { GET_COMPANIES_NAME } from '../../gql/queries/companies';

interface BusinessBookingsFiltersProps {
  filters: BusinessBookingFilters;
  onFiltersChange: (filters: BusinessBookingFilters) => void;
  onApply: () => void;
  onReset: () => void;
  loading?: boolean;
}

export default function BusinessBookingsFilters({
  filters,
  onFiltersChange,
  onApply,
  onReset,
  loading = false,
}: BusinessBookingsFiltersProps) {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  const theme = useTheme();

  // Local state for date pickers
  const [pickUpDateFrom, setPickUpDateFrom] = useState<Dayjs | null>(
    filters.pickUpDateFrom ? dayjs(filters.pickUpDateFrom) : null
  );
  const [pickUpDateTo, setPickUpDateTo] = useState<Dayjs | null>(
    filters.pickUpDate ? dayjs(filters.pickUpDate) : null
  );
  const [dropOffDateFrom, setDropOffDateFrom] = useState<Dayjs | null>(
    filters.dropOffDate ? dayjs(filters.dropOffDate) : null
  );
  const [dropOffDateTo, setDropOffDateTo] = useState<Dayjs | null>(
    filters.dropOffDateTo ? dayjs(filters.dropOffDateTo) : null
  );

  // Fetch dropdown data
  const { data: areasData } = useQuery(GET_ALL_AREAS);
  const { data: makesData } = useQuery(GET_ALL_MAKES);
  const { data: companiesData } = useQuery(GET_COMPANIES_NAME);

  const areas = areasData?.areas || [];
  const makes = makesData?.makes || [];
  const companies = companiesData?.companies || [];

  // Handle input changes
  const handleInputChange = (field: keyof BusinessBookingFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [field]: value,
    });
  };

  // Handle date changes
  const handleDateChange = (field: string, value: Dayjs | null) => {
    const dateString = value ? value.format('YYYY-MM-DD') : '';
    
    switch (field) {
      case 'pickUpDateFrom':
        setPickUpDateFrom(value);
        handleInputChange('pickUpDateFrom', dateString);
        break;
      case 'pickUpDateTo':
        setPickUpDateTo(value);
        handleInputChange('pickUpDate', dateString);
        break;
      case 'dropOffDateFrom':
        setDropOffDateFrom(value);
        handleInputChange('dropOffDate', dateString);
        break;
      case 'dropOffDateTo':
        setDropOffDateTo(value);
        handleInputChange('dropOffDateTo', dateString);
        break;
    }
  };

  // Handle reset
  const handleReset = () => {
    setPickUpDateFrom(null);
    setPickUpDateTo(null);
    setDropOffDateFrom(null);
    setDropOffDateTo(null);
    onReset();
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box sx={{ p: 3, backgroundColor: theme.palette.background.paper }}>
        <Typography variant="h6" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
          {t('Filters')}
        </Typography>

        <Grid container spacing={3}>
          {/* Search Fields */}
          <Grid item xs={12}>
            <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, color: 'primary.main' }}>
              {t('Search Fields')}
            </Typography>
            <Divider sx={{ mb: 2 }} />
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              label={t('Customer Name')}
              value={filters.customerName || ''}
              onChange={(e) => handleInputChange('customerName', e.target.value)}
              variant="outlined"
              size="small"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              label={t('nid.placeholder')}
              value={filters.customerNid || ''}
              onChange={(e) => handleInputChange('customerNid', e.target.value)}
              variant="outlined"
              size="small"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              label={t('bookingNo.')}
              value={filters.bookingNo || ''}
              onChange={(e) => handleInputChange('bookingNo', e.target.value)}
              variant="outlined"
              size="small"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              label={t('Customer Mobile')}
              value={filters.customerMobile || ''}
              onChange={(e) => handleInputChange('customerMobile', e.target.value)}
              variant="outlined"
              size="small"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              label={t('Ally Company Name')}
              value={filters.allyCompanyName || ''}
              onChange={(e) => handleInputChange('allyCompanyName', e.target.value)}
              variant="outlined"
              size="small"
            />
          </Grid>

          {/* Dropdown Filters */}
          <Grid item xs={12}>
            <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, color: 'primary.main', mt: 2 }}>
              {t('Filter Options')}
            </Typography>
            <Divider sx={{ mb: 2 }} />
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <Autocomplete
              multiple
              options={makes}
              getOptionLabel={(option) => isRTL ? option.arName || option.name : option.enName || option.name}
              value={makes.filter(make => filters.makeId?.includes(make.id)) || []}
              onChange={(_, newValue) => {
                handleInputChange('makeId', newValue.map(item => item.id));
              }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label={t('Car Make')}
                  variant="outlined"
                  size="small"
                />
              )}
              size="small"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <Autocomplete
              multiple
              options={areas}
              getOptionLabel={(option) => isRTL ? option.arName || option.name : option.enName || option.name}
              value={areas.filter(area => filters.pickUpCityId?.includes(area.id)) || []}
              onChange={(_, newValue) => {
                handleInputChange('pickUpCityId', newValue.map(item => item.id));
              }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label={t('Pickup City')}
                  variant="outlined"
                  size="small"
                />
              )}
              size="small"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <Autocomplete
              options={companies}
              getOptionLabel={(option) => option.name || ''}
              value={companies.find(company => company.id === filters.allyCompanyId) || null}
              onChange={(_, newValue) => {
                handleInputChange('allyCompanyId', newValue?.id || '');
              }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label={t('Ally Company')}
                  variant="outlined"
                  size="small"
                />
              )}
              size="small"
            />
          </Grid>

          {/* Date Filters */}
          <Grid item xs={12}>
            <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, color: 'primary.main', mt: 2 }}>
              {t('Date Filters')}
            </Typography>
            <Divider sx={{ mb: 2 }} />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <DatePicker
              label={t('Pickup Date From')}
              value={pickUpDateFrom}
              onChange={(value) => handleDateChange('pickUpDateFrom', value)}
              slotProps={{
                textField: {
                  size: 'small',
                  fullWidth: true,
                  variant: 'outlined',
                },
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <DatePicker
              label={t('Pickup Date To')}
              value={pickUpDateTo}
              onChange={(value) => handleDateChange('pickUpDateTo', value)}
              slotProps={{
                textField: {
                  size: 'small',
                  fullWidth: true,
                  variant: 'outlined',
                },
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <DatePicker
              label={t('Drop-off Date From')}
              value={dropOffDateFrom}
              onChange={(value) => handleDateChange('dropOffDateFrom', value)}
              slotProps={{
                textField: {
                  size: 'small',
                  fullWidth: true,
                  variant: 'outlined',
                },
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <DatePicker
              label={t('Drop-off Date To')}
              value={dropOffDateTo}
              onChange={(value) => handleDateChange('dropOffDateTo', value)}
              slotProps={{
                textField: {
                  size: 'small',
                  fullWidth: true,
                  variant: 'outlined',
                },
              }}
            />
          </Grid>

          {/* Action Buttons */}
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 2 }}>
              <Button
                variant="outlined"
                onClick={handleReset}
                startIcon={<ClearIcon />}
                disabled={loading}
              >
                {t('Reset')}
              </Button>
              <Button
                variant="contained"
                onClick={onApply}
                startIcon={<SearchIcon />}
                disabled={loading}
              >
                {t('Apply Filters')}
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Box>
    </LocalizationProvider>
  );
}
