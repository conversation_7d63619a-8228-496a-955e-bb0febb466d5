import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Divider,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from '@mui/material';
import {
  Person,
  Business,
  LocationOn,
  DirectionsCar,
  Security,
  Payment,
  LocalOffer,
  Schedule,
  Star,
  CheckCircle,
  Cancel,
} from '@mui/icons-material';

interface Customer {
  id: string;
  fullName: string;
  phoneNumber: string;
  email?: string;
  nationalId?: string;
}

interface Company {
  id: string;
  name: string;
  arName: string;
}

interface Branch {
  id: string;
  name: string;
  arName: string;
  address: string;
}

interface Car {
  id: string;
  name: string;
  model: string;
  year: number;
  plateNumber: string;
  color: string;
  dailyPrice: number;
}

interface Insurance {
  id: string;
  name: string;
  price: number;
  coverage: string;
}

interface ExtraService {
  id: string;
  name: string;
  price: number;
}

interface DeliveryLocation {
  lat: number;
  lng: number;
  address?: string;
  placeId?: string;
}

interface CarPlan {
  id: string;
  noOfMonths: number;
  firstInstallment: number;
  finalInstallment: number;
  monthlyInstallment?: number;
}

interface CouponData {
  id: string;
  code: string;
  discount: number;
  discountType: 'percentage' | 'fixed';
}

interface BookingFormSummaryProps {
  // Customer data
  customer: Customer | null;

  // Booking details
  bookingType: string;
  startDate: Date | null;
  endDate: Date | null;
  startTime: string;
  endTime: string;

  // Location data
  selectedCompany: Company | null;
  pickupBranch: Branch | null;
  dropoffBranch: Branch | null;

  // Car and services
  selectedCar: Car | null;
  selectedInsurance: Insurance | null;
  selectedExtraServices: ExtraService[];

  // Delivery
  hasDelivery: boolean;
  deliveryType?: 'one-way' | 'two-way';
  deliveryLocation: DeliveryLocation | null;

  // Payment
  paymentMethod: string;

  // Rent-to-own
  selectedPlan: CarPlan | null;

  // Coupon
  appliedCoupon: CouponData | null;

  // Pricing
  basePrice: number;
  totalPrice: number;
  discountAmount: number;

  // Fursan
  fursanMemberId?: string;
}

export default function BookingFormSummary({
  customer,
  bookingType,
  startDate,
  endDate,
  startTime,
  endTime,
  selectedCompany,
  pickupBranch,
  dropoffBranch,
  selectedCar,
  selectedInsurance,
  selectedExtraServices,
  hasDelivery,
  deliveryType,
  deliveryLocation,
  paymentMethod,
  selectedPlan,
  appliedCoupon,
  basePrice,
  totalPrice,
  discountAmount,
  fursanMemberId,
}: BookingFormSummaryProps) {
  const { t } = useTranslation();

  const formatDate = (date: Date | null) => {
    if (!date) return '-';
    return date.toLocaleDateString('ar-SA');
  };

  const formatCurrency = (amount: number) => {
    return `${amount.toFixed(2)} ${t('currency.sr')}`;
  };

  const calculateDays = () => {
    if (!startDate || !endDate) return 0;
    const timeDiff = endDate.getTime() - startDate.getTime();
    return Math.ceil(timeDiff / (1000 * 3600 * 24));
  };

  const days = calculateDays();

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Typography variant="h5" gutterBottom color="primary">
        {t('booking.summary')}
      </Typography>

      <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
        {t('review.booking.details.before.confirming')}
      </Typography>

      <Grid container spacing={3}>
        {/* Customer Information */}
        <Grid item xs={12} md={6}>
          <Card variant="outlined">
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <Person color="primary" />
                <Typography variant="h6">{t('customer.information')}</Typography>
              </Box>

              {customer ? (
                <List dense>
                  <ListItem>
                    <ListItemText primary={t('full.name')} secondary={customer.fullName} />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary={t('phone.number')} secondary={customer.phoneNumber} />
                  </ListItem>
                  {customer.email && (
                    <ListItem>
                      <ListItemText primary={t('email')} secondary={customer.email} />
                    </ListItem>
                  )}
                  {customer.nationalId && (
                    <ListItem>
                      <ListItemText primary={t('national.id')} secondary={customer.nationalId} />
                    </ListItem>
                  )}
                </List>
              ) : (
                <Typography variant="body2" color="error">
                  {t('no.customer.selected')}
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Booking Details */}
        <Grid item xs={12} md={6}>
          <Card variant="outlined">
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <Schedule color="primary" />
                <Typography variant="h6">{t('booking.details')}</Typography>
              </Box>

              <List dense>
                <ListItem>
                  <ListItemText primary={t('booking.type')} secondary={t(bookingType)} />
                </ListItem>
                <ListItem>
                  <ListItemText primary={t('pickup.date')} secondary={formatDate(startDate)} />
                </ListItem>
                <ListItem>
                  <ListItemText primary={t('return.date')} secondary={formatDate(endDate)} />
                </ListItem>
                <ListItem>
                  <ListItemText primary={t('pickup.time')} secondary={startTime} />
                </ListItem>
                <ListItem>
                  <ListItemText primary={t('return.time')} secondary={endTime} />
                </ListItem>
                <ListItem>
                  <ListItemText primary={t('duration')} secondary={`${days} ${t('days')}`} />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Company & Branch Information */}
        <Grid item xs={12} md={6}>
          <Card variant="outlined">
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <Business color="primary" />
                <Typography variant="h6">{t('company.branch.information')}</Typography>
              </Box>

              <List dense>
                {selectedCompany && (
                  <ListItem>
                    <ListItemText
                      primary={t('company')}
                      secondary={selectedCompany.arName || selectedCompany.name}
                    />
                  </ListItem>
                )}
                {pickupBranch && (
                  <ListItem>
                    <ListItemText
                      primary={t('pickup.branch')}
                      secondary={pickupBranch.arName || pickupBranch.name}
                    />
                  </ListItem>
                )}
                {dropoffBranch && (
                  <ListItem>
                    <ListItemText
                      primary={t('dropoff.branch')}
                      secondary={dropoffBranch.arName || dropoffBranch.name}
                    />
                  </ListItem>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Car Information */}
        <Grid item xs={12} md={6}>
          <Card variant="outlined">
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <DirectionsCar color="primary" />
                <Typography variant="h6">{t('vehicle.information')}</Typography>
              </Box>

              {selectedCar ? (
                <List dense>
                  <ListItem>
                    <ListItemText
                      primary={t('car.model')}
                      secondary={`${selectedCar.name} ${selectedCar.model} ${selectedCar.year}`}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary={t('plate.number')} secondary={selectedCar.plateNumber} />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary={t('color')} secondary={selectedCar.color} />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary={t('daily.price')}
                      secondary={formatCurrency(selectedCar.dailyPrice)}
                    />
                  </ListItem>
                </List>
              ) : (
                <Typography variant="body2" color="error">
                  {t('no.car.selected')}
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Insurance & Services */}
        <Grid item xs={12} md={6}>
          <Card variant="outlined">
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <Security color="primary" />
                <Typography variant="h6">{t('insurance.services')}</Typography>
              </Box>

              <List dense>
                {selectedInsurance ? (
                  <ListItem>
                    <ListItemText
                      primary={t('insurance')}
                      secondary={`${selectedInsurance.name} - ${formatCurrency(
                        selectedInsurance.price
                      )}`}
                    />
                  </ListItem>
                ) : (
                  <ListItem>
                    <ListItemText primary={t('insurance')} secondary={t('no.insurance.selected')} />
                  </ListItem>
                )}

                {selectedExtraServices.length > 0 ? (
                  <ListItem>
                    <ListItemText
                      primary={t('extra.services')}
                      secondary={
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>
                          {selectedExtraServices.map((service) => (
                            <Chip
                              key={service.id}
                              label={`${service.name} (${formatCurrency(service.price)})`}
                              size="small"
                              variant="outlined"
                            />
                          ))}
                        </Box>
                      }
                    />
                  </ListItem>
                ) : (
                  <ListItem>
                    <ListItemText
                      primary={t('extra.services')}
                      secondary={t('no.extra.services.selected')}
                    />
                  </ListItem>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Delivery Information */}
        {hasDelivery && (
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  <LocationOn color="primary" />
                  <Typography variant="h6">{t('delivery.information')}</Typography>
                </Box>

                <List dense>
                  <ListItem>
                    <ListItemText
                      primary={t('delivery.type')}
                      secondary={
                        deliveryType ? t(deliveryType + '.delivery') : t('one.way.delivery')
                      }
                    />
                  </ListItem>
                  {deliveryLocation && (
                    <ListItem>
                      <ListItemText
                        primary={t('delivery.location')}
                        secondary={deliveryLocation.address}
                      />
                    </ListItem>
                  )}
                </List>
              </CardContent>
            </Card>
          </Grid>
        )}

        {/* Rent-to-Own Plan */}
        {selectedPlan && bookingType === 'rent-to-own' && (
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  <Star color="primary" />
                  <Typography variant="h6">{t('rent.to.own.plan')}</Typography>
                </Box>

                <List dense>
                  <ListItem>
                    <ListItemText
                      primary={t('plan.duration')}
                      secondary={`${selectedPlan.noOfMonths} ${t('months')}`}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary={t('first.installment')}
                      secondary={formatCurrency(selectedPlan.firstInstallment)}
                    />
                  </ListItem>
                  {selectedPlan.monthlyInstallment && (
                    <ListItem>
                      <ListItemText
                        primary={t('monthly.installment')}
                        secondary={formatCurrency(selectedPlan.monthlyInstallment)}
                      />
                    </ListItem>
                  )}
                  <ListItem>
                    <ListItemText
                      primary={t('final.installment')}
                      secondary={formatCurrency(selectedPlan.finalInstallment)}
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
        )}

        {/* Payment Information */}
        <Grid item xs={12} md={6}>
          <Card variant="outlined">
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <Payment color="primary" />
                <Typography variant="h6">{t('payment.information')}</Typography>
              </Box>

              <List dense>
                <ListItem>
                  <ListItemText primary={t('payment.method')} secondary={t(paymentMethod)} />
                </ListItem>
                {fursanMemberId && (
                  <ListItem>
                    <ListItemText primary={t('fursan.member.id')} secondary={fursanMemberId} />
                  </ListItem>
                )}
                {appliedCoupon && (
                  <ListItem>
                    <ListItemText
                      primary={t('applied.coupon')}
                      secondary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Chip
                            label={appliedCoupon.code}
                            size="small"
                            color="success"
                            icon={<LocalOffer />}
                          />
                          <Typography variant="caption">
                            {appliedCoupon.discountType === 'percentage'
                              ? `${appliedCoupon.discount}%`
                              : formatCurrency(appliedCoupon.discount)}{' '}
                            {t('discount')}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Price Breakdown */}
        <Grid item xs={12}>
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom color="primary">
                {t('price.breakdown')}
              </Typography>

              <TableContainer>
                <Table>
                  <TableBody>
                    <TableRow>
                      <TableCell>{t('base.price')}</TableCell>
                      <TableCell align="right">{formatCurrency(basePrice)}</TableCell>
                    </TableRow>

                    {selectedInsurance && (
                      <TableRow>
                        <TableCell>{t('insurance.cost')}</TableCell>
                        <TableCell align="right">
                          {formatCurrency(selectedInsurance.price * days)}
                        </TableCell>
                      </TableRow>
                    )}

                    {selectedExtraServices.map((service) => (
                      <TableRow key={service.id}>
                        <TableCell>{service.name}</TableCell>
                        <TableCell align="right">{formatCurrency(service.price * days)}</TableCell>
                      </TableRow>
                    ))}

                    {discountAmount > 0 && (
                      <TableRow>
                        <TableCell sx={{ color: 'success.main' }}>{t('discount')}</TableCell>
                        <TableCell align="right" sx={{ color: 'success.main' }}>
                          -{formatCurrency(discountAmount)}
                        </TableCell>
                      </TableRow>
                    )}

                    <TableRow>
                      <TableCell colSpan={2}>
                        <Divider />
                      </TableCell>
                    </TableRow>

                    <TableRow>
                      <TableCell>
                        <Typography variant="h6" color="primary">
                          {t('total.amount')}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="h6" color="primary">
                          {formatCurrency(totalPrice)}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Terms and Conditions Notice */}
        <Grid item xs={12}>
          <Box
            sx={{ p: 2, bgcolor: 'info.50', border: 1, borderColor: 'info.main', borderRadius: 1 }}
          >
            <Typography variant="subtitle2" gutterBottom color="info.main">
              {t('important.notes')}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              • {t('please.review.all.details.carefully')}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              • {t('booking.confirmation.will.be.sent.via.sms.email')}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              • {t('cancellation.policy.applies')}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              • {t('terms.and.conditions.apply')}
            </Typography>
          </Box>
        </Grid>
      </Grid>
    </Paper>
  );
}
