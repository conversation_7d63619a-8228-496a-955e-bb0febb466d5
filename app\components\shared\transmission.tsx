import React from 'react';
import { Autocomplete, TextField, Chip, CircularProgress } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { transmissions } from './constants';



interface TransmissionProps {
  filters: {
    transmission?: string | null;
    [key: string]: any;
  };
  handleAutocompleteChange: (field: string, value: string | null) => void;
}

const Transmission: React.FC<TransmissionProps> = ({ 
  filters,
  handleAutocompleteChange,
}) => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  


  return (
    <Autocomplete 
      id="Transmission" 
      options={transmissions(t)} 
      getOptionLabel={(option) => (isRTL ? option.label : option.label)} 

      value={ 
        filters.transmission 
          ? transmissions(t).find((ally:any) => ally.value.toString() === filters.transmission) || null 
          : null 
      } 
      onChange={(_, newValue) => 
        handleAutocompleteChange('transmission', newValue?.value?.toString() || null) 
      } 
      renderInput={(params) => ( 
        <TextField 
          {...params} 
          label={t('transmission')} 
          variant="outlined" 
          size="small" 
          InputLabelProps={{ 
            sx: { padding: '0 20px' }, 
          }}
          InputProps={{
            ...params.InputProps,
            endAdornment: (
              <>
                {params.InputProps.endAdornment}
              </>
            ),
          }}
        /> 
      )} 
      renderTags={(value, getTagProps) => 
        value.map((option, index) => ( 
          <Chip 
            label={isRTL ? option.label : option.label} 
            {...getTagProps({ index })} 
            size="small" 
          /> 
        )) 
      }
    />
  );
};

export default Transmission;