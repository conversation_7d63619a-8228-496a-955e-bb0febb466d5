'use client';
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete,
  Typography,
  Divider,
  useTheme,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';
import dayjs, { Dayjs } from 'dayjs';

// Generic filter field types
export type FilterFieldType = 
  | 'text' 
  | 'select' 
  | 'multiselect' 
  | 'autocomplete' 
  | 'date' 
  | 'daterange';

export interface FilterOption {
  id: string | number;
  label: string;
  value: string | number;
  arLabel?: string; // For Arabic support
}

export interface FilterField {
  id: string;
  label: string;
  type: FilterFieldType;
  placeholder?: string;
  options?: FilterOption[];
  multiple?: boolean;
  required?: boolean;
  gridSize?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
  };
}

export interface FilterSection {
  title: string;
  fields: FilterField[];
}

interface GenericFiltersProps {
  sections: FilterSection[];
  filters: Record<string, any>;
  onFiltersChange: (filters: Record<string, any>) => void;
  onApply: () => void;
  onReset: () => void;
  loading?: boolean;
}

export default function GenericFilters({
  sections,
  filters,
  onFiltersChange,
  onApply,
  onReset,
  loading = false,
}: GenericFiltersProps) {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  const theme = useTheme();

  // Local state for date fields
  const [dateStates, setDateStates] = useState<Record<string, Dayjs | null>>({});

  // Initialize date states
  useEffect(() => {
    const initialDateStates: Record<string, Dayjs | null> = {};
    sections.forEach(section => {
      section.fields.forEach(field => {
        if (field.type === 'date' || field.type === 'daterange') {
          if (filters[field.id]) {
            initialDateStates[field.id] = dayjs(filters[field.id]);
          }
        }
      });
    });
    setDateStates(initialDateStates);
  }, [sections, filters]);

  // Handle input changes
  const handleInputChange = (fieldId: string, value: any) => {
    onFiltersChange({
      ...filters,
      [fieldId]: value,
    });
  };

  // Handle date changes
  const handleDateChange = (fieldId: string, value: Dayjs | null) => {
    setDateStates(prev => ({
      ...prev,
      [fieldId]: value,
    }));
    
    const dateString = value ? value.format('YYYY-MM-DD') : '';
    handleInputChange(fieldId, dateString);
  };

  // Handle reset
  const handleReset = () => {
    setDateStates({});
    onReset();
  };

  // Render field based on type
  const renderField = (field: FilterField) => {
    const gridProps = {
      xs: field.gridSize?.xs || 12,
      sm: field.gridSize?.sm || 6,
      md: field.gridSize?.md || 4,
      lg: field.gridSize?.lg || 3,
    };

    switch (field.type) {
      case 'text':
        return (
          <Grid item {...gridProps} key={field.id}>
            <TextField
              fullWidth
              label={field.label}
              placeholder={field.placeholder}
              value={filters[field.id] || ''}
              onChange={(e) => handleInputChange(field.id, e.target.value)}
              variant="outlined"
              size="small"
              required={field.required}
            />
          </Grid>
        );

      case 'select':
        return (
          <Grid item {...gridProps} key={field.id}>
            <FormControl fullWidth size="small">
              <InputLabel>{field.label}</InputLabel>
              <Select
                value={filters[field.id] || ''}
                onChange={(e) => handleInputChange(field.id, e.target.value)}
                label={field.label}
                required={field.required}
              >
                {field.options?.map((option) => (
                  <MenuItem key={option.id} value={option.value}>
                    {isRTL && option.arLabel ? option.arLabel : option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        );

      case 'multiselect':
      case 'autocomplete':
        return (
          <Grid item {...gridProps} key={field.id}>
            <Autocomplete
              multiple={field.multiple || field.type === 'multiselect'}
              options={field.options || []}
              getOptionLabel={(option) => 
                isRTL && option.arLabel ? option.arLabel : option.label
              }
              value={
                field.multiple || field.type === 'multiselect'
                  ? (field.options?.filter(opt => 
                      Array.isArray(filters[field.id]) && filters[field.id].includes(opt.value)
                    ) || [])
                  : (field.options?.find(opt => opt.value === filters[field.id]) || null)
              }
              onChange={(_, newValue) => {
                if (field.multiple || field.type === 'multiselect') {
                  const values = Array.isArray(newValue) 
                    ? newValue.map(item => item.value) 
                    : [];
                  handleInputChange(field.id, values);
                } else {
                  handleInputChange(field.id, newValue?.value || '');
                }
              }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label={field.label}
                  placeholder={field.placeholder}
                  variant="outlined"
                  size="small"
                  required={field.required}
                />
              )}
              size="small"
            />
          </Grid>
        );

      case 'date':
        return (
          <Grid item {...gridProps} key={field.id}>
            <DatePicker
              label={field.label}
              value={dateStates[field.id] || null}
              onChange={(value) => handleDateChange(field.id, value)}
              slotProps={{
                textField: {
                  size: 'small',
                  fullWidth: true,
                  variant: 'outlined',
                  required: field.required,
                },
              }}
            />
          </Grid>
        );

      default:
        return null;
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box sx={{ p: 3, backgroundColor: theme.palette.background.paper }}>
        <Typography variant="h6" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
          {t('Filters')}
        </Typography>

        {sections.map((section, sectionIndex) => (
          <Box key={sectionIndex} sx={{ mb: 3 }}>
            <Typography 
              variant="subtitle2" 
              gutterBottom 
              sx={{ fontWeight: 600, color: 'primary.main' }}
            >
              {section.title}
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            <Grid container spacing={3}>
              {section.fields.map(renderField)}
            </Grid>
          </Box>
        ))}

        {/* Action Buttons */}
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 3 }}>
          <Button
            variant="outlined"
            onClick={handleReset}
            startIcon={<ClearIcon />}
            disabled={loading}
          >
            {t('Reset')}
          </Button>
          <Button
            variant="contained"
            onClick={onApply}
            startIcon={<SearchIcon />}
            disabled={loading}
          >
            {t('Apply Filters')}
          </Button>
        </Box>
      </Box>
    </LocalizationProvider>
  );
}
