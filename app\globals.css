:root {
  --max-width: 1100px;
  --border-radius: 12px;
  --font-mono: ui-monospace, Menlo, Monaco, "Cascadia Mono", "Segoe UI Mono",
    "Roboto Mono", "Oxygen Mono", "Ubuntu Monospace", "Source Code Pro",
    "Fira Mono", "Droid Sans Mono", "Courier New", monospace;

  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;

  --primary-glow: conic-gradient(
    from 180deg at 50% 50%,
    #16abff33 0deg,
    #0885ff33 55deg,
    #54d6ff33 120deg,
    #0071ff33 160deg,
    transparent 360deg
  );
  --secondary-glow: radial-gradient(
    rgba(255, 255, 255, 1),
    rgba(255, 255, 255, 0)
  );

  --tile-start-rgb: 239, 245, 249;
  --tile-end-rgb: 228, 232, 233;
  --tile-border: conic-gradient(
    #00000080,
    #00000040,
    #00000030,
    #00000020,
    #00000010,
    #00000010,
    #00000080
  );

  --callout-rgb: 238, 240, 241;
  --callout-border-rgb: 172, 175, 176;
  --card-rgb: 180, 185, 188;
  --card-border-rgb: 131, 134, 135;
}


* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  color: rgb(var(--foreground-rgb));
  background: #f7f8f7
}

a {
  color: inherit;
  text-decoration: none;
}

/* Global scrollbar styling - applied to ALL elements */
*::-webkit-scrollbar,
::-webkit-scrollbar {
  width: 10px !important;
  height: 8px !important;
}

*::-webkit-scrollbar-track,
::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05) !important;
  border-radius: 4px !important;
}

*::-webkit-scrollbar-thumb,
::-webkit-scrollbar-thumb {
  background: rgba(241, 146, 35, 0.5) !important;
  border-radius: 4px !important;
  border: none !important;
  transition: all 0.3s ease !important;
}

*::-webkit-scrollbar-thumb:hover,
::-webkit-scrollbar-thumb:hover {
  background: rgba(241, 146, 35, 0.8) !important;
}

/* Dark mode scrollbar - applied to ALL elements */
html[data-theme-mode="dark"] *::-webkit-scrollbar-track,
html[data-theme-mode="dark"] ::-webkit-scrollbar-track,
[data-theme-mode="dark"] *::-webkit-scrollbar-track,
[data-theme-mode="dark"] ::-webkit-scrollbar-track {
  background: rgba(30, 30, 30, 0.9) !important;
}

html[data-theme-mode="dark"] *::-webkit-scrollbar-thumb,
html[data-theme-mode="dark"] ::-webkit-scrollbar-thumb,
[data-theme-mode="dark"] *::-webkit-scrollbar-thumb,
[data-theme-mode="dark"] ::-webkit-scrollbar-thumb {
  background: #f1922380 !important;
  border: none !important;
}

html[data-theme-mode="dark"] *::-webkit-scrollbar-thumb:hover,
html[data-theme-mode="dark"] ::-webkit-scrollbar-thumb:hover,
[data-theme-mode="dark"] *::-webkit-scrollbar-thumb:hover,
[data-theme-mode="dark"] ::-webkit-scrollbar-thumb:hover {
  background: #f19223a0 !important;
}

@font-face {
  font-family: "Helvetica";
  src: url("/fonts/HelveticaNeueLTArabic-Roman.eot");
  src: url("/fonts/HelveticaNeueLTArabic-Roman.eot?#iefix") format("embedded-opentype"),
    url("/fonts/HelveticaNeueLTArabic-Roman.woff2") format("woff2"),
    url("/fonts/HelveticaNeueLTArabic-Roman.woff") format("woff"),
    url("/fonts/HelveticaNeueLTArabic-Roman.ttf") format("truetype"),
    url("/fonts/HelveticaNeueLTArabic-Roman.svg#HelveticaNeueLTArabic-Roman")
      format("svg");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
