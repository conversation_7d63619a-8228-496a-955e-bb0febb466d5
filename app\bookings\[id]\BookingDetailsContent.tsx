'use client';
import { useState } from 'react';
import { useQuery } from '@apollo/client';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'next/navigation';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Chip,
  Button,
  IconButton,
  Divider,
  CircularProgress,
  Alert,
  useTheme,
  useMediaQuery,
  Avatar,
  Stack,
  Tooltip,
  Collapse,
} from '@mui/material';
import {
  ArrowBack,
  Timeline as TimelineIcon,
  Edit as EditIcon,
  Print as PrintIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  LocationOn as LocationIcon,
  CalendarToday as CalendarIcon,
  DirectionsCar as CarIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  Payment as PaymentIcon,
  Receipt as ReceiptIcon,
  Extension as ExtensionIcon,
  Assignment as AssignmentIcon,
  Note as NoteIcon,
  AttachMoney as PriceIcon,
  AccessTime as DurationIcon,
  Build as ServiceIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import { BOOKING_DETAILS_QUERY } from '../../gql/queries/bookings';
import { BRANCH_DETAILS_QUERY } from '../../gql/queries/bookings';
import RctCollapsibleCard from '../../../components/RctCollapsibleCard';

// Import the print utility
import { printBookingDetails } from '../../utils/print';

// Import all the missing components
import GoogleMapComponent from '../components/GoogleMapComponent';
import InstallmentsTable from '../components/InstallmentsTable';
import ExtensionRequestsTable from '../components/ExtensionRequestsTable';
import BookingAssignment from '../components/BookingAssignment';
import AddNoteModal from '../components/AddNoteModal';
import UpdateSuggestedPriceModal from '../components/UpdateSuggestedPriceModal';
import ChangeDurationModal from '../components/ChangeDurationModal';
import UpdateExtraServiceModal from '../components/UpdateExtraServiceModal';
import TimelineModal from '../components/TimelineModal';
import ChangeStatusModal from '../components/ChangeStatusModal';
import ExtendModal from '../components/ExtendModal';

interface BookingDetailsContentProps {
  bookingId: string;
}

export default function BookingDetailsContent({ bookingId }: BookingDetailsContentProps) {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  const dateLocale = isRTL ? ar : enUS;
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const router = useRouter();

  // Modal states
  const [timelineModalOpen, setTimelineModalOpen] = useState(false);
  const [statusModalOpen, setStatusModalOpen] = useState(false);
  const [extendModalOpen, setExtendModalOpen] = useState(false);
  const [addNoteModalOpen, setAddNoteModalOpen] = useState(false);
  const [updatePriceModalOpen, setUpdatePriceModalOpen] = useState(false);
  const [changeDurationModalOpen, setChangeDurationModalOpen] = useState(false);
  const [updateExtraServiceModalOpen, setUpdateExtraServiceModalOpen] = useState(false);
  const [extensionRequestsModalOpen, setExtensionRequestsModalOpen] = useState(false);

  // Collapsible sections
  const [collapsedSections, setCollapsedSections] = useState({
    aboutPrice: false,
    installments: false,
    extensionRequests: false,
    notes: false,
    assignment: false,
  });

  // Fetch booking details
  const { data, loading, error, refetch } = useQuery(BOOKING_DETAILS_QUERY, {
    variables: { id: bookingId },
    errorPolicy: 'all',
  });

  // Fetch branch details if branchId is available
  const { data: branchData, loading: branchLoading } = useQuery(BRANCH_DETAILS_QUERY, {
    variables: { id: data?.rentalDetails?.branchId },
    skip: !data?.rentalDetails?.branchId,
    errorPolicy: 'all',
  });

  const booking = data?.rentalDetails;
  const branch = branchData?.branch;

  const handleBack = () => {
    router.push('/bookings');
  };

  const handleModalSuccess = () => {
    refetch();
  };

  const toggleSection = (section: keyof typeof collapsedSections) => {
    setCollapsedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const formatDate = (date: string) => {
    if (!date) return '-';
    return format(new Date(date), 'PPp', { locale: dateLocale });
  };

  const formatCurrency = (amount: number) => {
    if (!amount) return '0';
    return new Intl.NumberFormat(isRTL ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR',
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'pending':
        return 'warning';
      case 'confirmed':
        return 'info';
      case 'car_received':
        return 'primary';
      case 'invoiced':
        return 'secondary';
      case 'closed':
        return 'success';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  const handlePrint = () => {
    const printableBooking = {
      bookingNo: booking.bookingNo,
      customerName: booking.customerName,
      customerMobile: booking.customerMobile,
      customerEmail: booking.customerEmail,
      carMake: booking.makeName,
      carModel: booking.modelName,
      carYear: booking.year?.toString(),
      pickUpDate: booking.pickUpDate,
      dropOffDate: booking.dropOffDate,
      numberOfDays: booking.numberOfDays,
      totalBookingPrice: booking.totalBookingPrice,
      status: booking.status,
      branchName: branch ? (isRTL ? branch.arName : branch.enName) : booking.branchName,
      createdAt: booking.createdAt,
    };

    printBookingDetails(printableBooking, t);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error || !booking) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          {t('Error loading booking details. Please try again later.')}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 1, sm: 2, md: 3 } }}>
      {/* Header */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          mb: 3,
          flexDirection: { xs: 'column', sm: 'row' },
          gap: 2,
        }}
      >
        <Grid container spacing={2} sx={{ display: 'flex', alignItems: 'center' }}>
          <Grid item xs={12} sm={12}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton onClick={handleBack} sx={{ mr: 1 }}>
                <ArrowBack />
              </IconButton>
              <Box>
                <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
                  {t('bookings.details.title')}
                </Typography>
                <Typography variant="subtitle1" color="text.secondary">
                  {t('bookings.details.bookingNumber')}: {booking.bookingNo}
                </Typography>
              </Box>
            </Box>
          </Grid>

          <Grid item xs={12} sm={12}>
            <Box
              sx={{
                display: 'flex',
                gap: 2,
                flexWrap: 'wrap',
                alignItems: 'center',
                justifyContent: { xs: 'flex-start', sm: 'flex-end' },
              }}
            >
              <Button
                variant="contained"
                sx={{
                  backgroundColor: '#1976d2',
                  '&:hover': { backgroundColor: '#115293' },
                  minWidth: '120px',
                  height: '36px',
                }}
                startIcon={<TimelineIcon />}
                onClick={() => setTimelineModalOpen(true)}
                size="small"
              >
                {t('bookings.actions.timeline')}
              </Button>
              <Button
                variant="contained"
                sx={{
                  backgroundColor: '#9c27b0',
                  '&:hover': { backgroundColor: '#7b1fa2' },
                  minWidth: '140px',
                  height: '36px',
                }}
                startIcon={<EditIcon />}
                onClick={() => setStatusModalOpen(true)}
                size="small"
              >
                {t('bookings.actions.changeStatus')}
              </Button>
              <Button
                variant="contained"
                sx={{
                  backgroundColor: '#ff9800',
                  '&:hover': { backgroundColor: '#f57c00' },
                  minWidth: '100px',
                  height: '36px',
                }}
                startIcon={<ExtensionIcon />}
                onClick={() => setExtendModalOpen(true)}
                size="small"
              >
                {t('bookings.actions.extend')}
              </Button>
              <Button
                variant="contained"
                sx={{
                  backgroundColor: '#4caf50',
                  '&:hover': { backgroundColor: '#388e3c' },
                  minWidth: '120px',
                  height: '36px',
                }}
                startIcon={<NoteIcon />}
                onClick={() => setAddNoteModalOpen(true)}
                size="small"
              >
                {t('bookings.actions.addNote')}
              </Button>
              <Button
                variant="contained"
                sx={{
                  backgroundColor: '#f44336',
                  '&:hover': { backgroundColor: '#d32f2f' },
                  minWidth: '140px',
                  height: '36px',
                }}
                startIcon={<PriceIcon />}
                onClick={() => setUpdatePriceModalOpen(true)}
                size="small"
              >
                {t('bookings.actions.updatePrice')}
              </Button>
              <Button
                variant="contained"
                sx={{
                  backgroundColor: '#00bcd4',
                  '&:hover': { backgroundColor: '#0097a7' },
                  minWidth: '160px',
                  height: '36px',
                }}
                startIcon={<DurationIcon />}
                onClick={() => setChangeDurationModalOpen(true)}
                size="small"
              >
                {t('bookings.actions.changeDuration')}
              </Button>
              <Button
                variant="contained"
                sx={{
                  backgroundColor: '#673ab7',
                  '&:hover': { backgroundColor: '#512da8' },
                  minWidth: '160px',
                  height: '36px',
                }}
                startIcon={<ServiceIcon />}
                onClick={() => setUpdateExtraServiceModalOpen(true)}
                size="small"
              >
                {t('bookings.actions.updateServices')}
              </Button>
              <Button
                variant="contained"
                sx={{
                  backgroundColor: '#607d8b',
                  '&:hover': { backgroundColor: '#455a64' },
                  minWidth: '100px',
                  height: '36px',
                }}
                startIcon={<PrintIcon />}
                size="small"
                onClick={handlePrint}
              >
                {t('bookings.actions.print')}
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Box>

      {/* Two Column Layout like old dashboard */}
      <Grid container spacing={3}>
        {/* Left Column */}
        <Grid item xs={12} md={6}>
          {/* Customer Information */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <PersonIcon color="primary" />
                <Typography variant="h6">{t('bookings.details.customerInformation')}</Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <Avatar src={booking.customerProfileImage} sx={{ width: 60, height: 60 }}>
                  {booking.customerName?.charAt(0)}
                </Avatar>
                <Box>
                  <Typography variant="h6">{booking.customerName}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {t('bookings.details.customerId')}: {booking.userId}
                  </Typography>
                </Box>
              </Box>
              <Stack spacing={1}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <PhoneIcon fontSize="small" color="action" />
                  <Typography variant="body2">{booking.customerMobile}</Typography>
                </Box>
                {booking.customerEmail ? (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <EmailIcon fontSize="small" color="action" />
                    <Typography variant="body2">{booking.customerEmail}</Typography>
                  </Box>
                ) : null}
                {booking.customerRate ? (
                  <Typography variant="body2">
                    {t('bookings.details.customerRating')}: {booking.customerRate}/5
                  </Typography>
                ) : null}
              </Stack>
            </CardContent>
          </Card>

          {/* Booking Assignment */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <AssignmentIcon color="primary" />
                <Typography variant="h6">{t('bookings.details.assignment')}</Typography>
                <IconButton size="small" onClick={() => toggleSection('assignment')}>
                  {collapsedSections.assignment ? <ExpandMoreIcon /> : <ExpandLessIcon />}
                </IconButton>
              </Box>
              <Collapse in={!collapsedSections.assignment}>
                <BookingAssignment
                  bookingId={bookingId}
                  currentAssignment={booking.assignment}
                  refetchBooking={refetch}
                />
              </Collapse>
            </CardContent>
          </Card>

          {/* Status and Basic Info */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                    <Typography variant="h6">{t('Status')}</Typography>
                    <Chip
                      label={t(booking.statusLocalized || booking.status)}
                      color={getStatusColor(booking.status) as any}
                      variant="filled"
                    />
                  </Box>
                  <Stack spacing={1}>
                    <Typography variant="body2" color="text.secondary">
                      {t('Created')}: {formatDate(booking.createdAt)}
                    </Typography>
                    {booking.invoicedAt && (
                      <Typography variant="body2" color="text.secondary">
                        {t('Invoiced')}: {formatDate(booking.invoicedAt)}
                      </Typography>
                    )}
                    {booking.refundedAt && (
                      <Typography variant="body2" color="text.secondary">
                        {t('Refunded')}: {formatDate(booking.refundedAt)}
                      </Typography>
                    )}
                  </Stack>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    {t('Payment Information')}
                  </Typography>
                  <Stack spacing={1}>
                    <Typography variant="body2">
                      {t('Total Amount')}:{' '}
                      <strong>{formatCurrency(booking.totalBookingPrice)}</strong>
                    </Typography>
                    <Typography variant="body2">
                      {t('Paid Amount')}: <strong>{formatCurrency(booking.paidAmount)}</strong>
                    </Typography>
                    <Typography variant="body2">
                      {t('Payment Method')}: {booking.paymentMethod || '-'}
                    </Typography>
                    <Typography variant="body2">
                      {t('Payment Status')}:
                      <Chip
                        label={booking.isPaid ? t('Paid') : t('Unpaid')}
                        color={booking.isPaid ? 'success' : 'warning'}
                        size="small"
                        sx={{ ml: 1 }}
                      />
                    </Typography>
                  </Stack>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Right Column */}
        <Grid item xs={12} md={6}>
          {/* Google Maps - Branch Location */}
          {branch && (
            <GoogleMapComponent
              latitude={branch.lat || 0}
              longitude={branch.lng || 0}
              branch={{
                id: branch.id,
                name: isRTL ? branch.arName : branch.enName,
                address: branch.address,
                latitude: branch.lat || 0,
                longitude: branch.lng || 0,
                phone: branch.officeNumber || branch.allyCompany?.phoneNumber,
              }}
            />
          )}

          {/* Ally Information */}
          {booking.allyCompanyId && (
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <BusinessIcon color="primary" />
                  <Typography variant="h6">{t('Ally Company')}</Typography>
                </Box>
                <Stack spacing={1}>
                  <Typography variant="h6">
                    {isRTL ? booking.arAllyName : booking.enAllyName}
                  </Typography>
                  <Typography variant="body2">
                    {t('Ally ID')}: {booking.allyCompanyId}
                  </Typography>
                  {booking.allyRate && (
                    <Typography variant="body2">
                      {t('Ally Rating')}: {booking.allyRate}/5
                    </Typography>
                  )}
                </Stack>
              </CardContent>
            </Card>
          )}

          {/* Vehicle Information */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <CarIcon color="primary" />
                <Typography variant="h6">{t('Vehicle Information')}</Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <Avatar src={booking.carImage} variant="rounded" sx={{ width: 80, height: 60 }}>
                  <CarIcon />
                </Avatar>
                <Box>
                  <Typography variant="h6">
                    {booking.makeName} {booking.modelName}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {booking.versionName} - {booking.year}
                  </Typography>
                </Box>
              </Box>
              <Stack spacing={1}>
                <Typography variant="body2">
                  {t('Car ID')}: {booking.carId}
                </Typography>
                <Typography variant="body2">
                  {t('Daily Price')}: {formatCurrency(booking.pricePerDay)}
                </Typography>
                {booking.isUnlimited && (
                  <Chip
                    label={t('Unlimited Mileage')}
                    color="info"
                    size="small"
                    variant="outlined"
                  />
                )}
              </Stack>
            </CardContent>
          </Card>

          {/* Rental Period */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <CalendarIcon color="primary" />
                <Typography variant="h6">{t('Rental Period')}</Typography>
              </Box>
              <Stack spacing={2}>
                <Box>
                  <Typography variant="subtitle2" color="primary">
                    {t('Pick-up')}
                  </Typography>
                  <Typography variant="body2">
                    {formatDate(booking.pickUpDate)} at {booking.pickUpTime}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {booking.pickUpCityName}
                  </Typography>
                </Box>
                <Divider />
                <Box>
                  <Typography variant="subtitle2" color="primary">
                    {t('Drop-off')}
                  </Typography>
                  <Typography variant="body2">
                    {formatDate(booking.dropOffDate)} at {booking.dropOffTime}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {booking.dropOffCityName}
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="body2">
                    {t('Duration')}:{' '}
                    <strong>
                      {booking.numberOfDays} {t('days')}
                    </strong>
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Full Width Sections */}

      {/* Rental Installments */}
      {booking.installments && booking.installments.length > 0 && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <PaymentIcon color="primary" />
              <Typography variant="h6">{t('bookings.installments.title')}</Typography>
              <IconButton size="small" onClick={() => toggleSection('installments')}>
                {collapsedSections.installments ? <ExpandMoreIcon /> : <ExpandLessIcon />}
              </IconButton>
            </Box>
            <Collapse in={!collapsedSections.installments}>
              <InstallmentsTable
                installments={booking.installments}
                rentalId={bookingId}
                refetchBooking={refetch}
                bookingDetails={booking}
              />
            </Collapse>
          </CardContent>
        </Card>
      )}

      {/* Extension Requests */}
      {booking.rentalDateExtensionRequests && booking.rentalDateExtensionRequests.length > 0 && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <ExtensionIcon color="primary" />
              <Typography variant="h6">{t('bookings.extensionRequests.title')}</Typography>
              <IconButton size="small" onClick={() => toggleSection('extensionRequests')}>
                {collapsedSections.extensionRequests ? <ExpandMoreIcon /> : <ExpandLessIcon />}
              </IconButton>
            </Box>
            <Collapse in={!collapsedSections.extensionRequests}>
              <ExtensionRequestsTable
                extensionRequests={booking.rentalDateExtensionRequests}
                rentalId={bookingId}
                refetchBooking={refetch}
                bookingDetails={booking}
              />
            </Collapse>
          </CardContent>
        </Card>
      )}

      {/* Price Breakdown */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <ReceiptIcon color="primary" />
            <Typography variant="h6">{t('Price Breakdown')}</Typography>
            <IconButton size="small" onClick={() => toggleSection('aboutPrice')}>
              {collapsedSections.aboutPrice ? <ExpandMoreIcon /> : <ExpandLessIcon />}
            </IconButton>
          </Box>
          <Collapse in={!collapsedSections.aboutPrice}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Stack spacing={1}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2">{t('Daily Price')}</Typography>
                    <Typography variant="body2">{formatCurrency(booking.dailyPrice)}</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2">{t('Number of Days')}</Typography>
                    <Typography variant="body2">{booking.numberOfDays}</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2">{t('Price Before Tax')}</Typography>
                    <Typography variant="body2">
                      {formatCurrency(booking.priceBeforeTax)}
                    </Typography>
                  </Box>
                  {booking.taxValue > 0 && (
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2">
                        {t('Tax')} ({booking.valueAddedTaxPercentage}%)
                      </Typography>
                      <Typography variant="body2">{formatCurrency(booking.taxValue)}</Typography>
                    </Box>
                  )}
                </Stack>
              </Grid>
              <Grid item xs={12} md={6}>
                <Stack spacing={1}>
                  {booking.totalInsurancePrice > 0 && (
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2">{t('Insurance')}</Typography>
                      <Typography variant="body2">
                        {formatCurrency(booking.totalInsurancePrice)}
                      </Typography>
                    </Box>
                  )}
                  {booking.deliveryPrice > 0 && (
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2">{t('Delivery')}</Typography>
                      <Typography variant="body2">
                        {formatCurrency(booking.deliveryPrice)}
                      </Typography>
                    </Box>
                  )}
                  {booking.discountValue > 0 && (
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2" color="success.main">
                        {t('Discount')}
                      </Typography>
                      <Typography variant="body2" color="success.main">
                        -{formatCurrency(booking.discountValue)}
                      </Typography>
                    </Box>
                  )}
                  <Divider />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="h6">{t('Total Amount')}</Typography>
                    <Typography variant="h6" color="primary">
                      {formatCurrency(booking.totalBookingPrice)}
                    </Typography>
                  </Box>
                </Stack>
              </Grid>
            </Grid>
          </Collapse>
        </CardContent>
      </Card>

      {/* Extra Services */}
      {booking.rentalExtraServices && booking.rentalExtraServices.length > 0 && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2 }}>
              {t('Extra Services')}
            </Typography>
            <Grid container spacing={2}>
              {booking.rentalExtraServices.map((service: any) => (
                <Grid item xs={12} md={6} key={service.id}>
                  <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                    <Typography variant="subtitle2">
                      {isRTL ? service.arTitle : service.enTitle}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {isRTL ? service.arDescription : service.enDescription}
                    </Typography>
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      {t('Price')}: {formatCurrency(service.totalServiceValue)}
                    </Typography>
                  </Box>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Notes Section */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <NoteIcon color="primary" />
            <Typography variant="h6">{t('Notes')}</Typography>
            <IconButton size="small" onClick={() => toggleSection('notes')}>
              {collapsedSections.notes ? <ExpandMoreIcon /> : <ExpandLessIcon />}
            </IconButton>
          </Box>
          <Collapse in={!collapsedSections.notes}>
            {booking.notes && booking.notes.length > 0 ? (
              <Stack spacing={2}>
                {booking.notes.map((noteItem: any, index: number) => (
                  <Box
                    key={index}
                    sx={{
                      p: 2,
                      bgcolor: 'grey.50',
                      borderRadius: 1,
                      borderLeft: 4,
                      borderLeftColor: 'primary.main',
                    }}
                  >
                    <Typography variant="body2" sx={{ mb: 1, whiteSpace: 'pre-wrap' }}>
                      {noteItem.note}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', mt: 1 }}>
                      <Typography variant="caption" color="text.secondary">
                        {t('bookings.notes.createdBy')}: {noteItem.created_by}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {formatDate(noteItem.created_at)}
                      </Typography>
                      {noteItem.status && (
                        <Chip
                          label={t(noteItem.status)}
                          size="small"
                          variant="outlined"
                          color={getStatusColor(noteItem.status) as any}
                        />
                      )}
                    </Box>
                  </Box>
                ))}
              </Stack>
            ) : (
              <Typography variant="body2" color="text.secondary">
                {t('No notes available')}
              </Typography>
            )}
          </Collapse>
        </CardContent>
      </Card>

      {/* Delivery Information */}
      {booking.deliverType && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <LocationIcon color="primary" />
              <Typography variant="h6">{t('Delivery Information')}</Typography>
            </Box>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2">{t('Delivery Type')}</Typography>
                <Typography variant="body2">{booking.deliverType}</Typography>
              </Grid>
              {booking.deliverAddress && (
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">{t('Delivery Address')}</Typography>
                  <Typography variant="body2">{booking.deliverAddress}</Typography>
                </Grid>
              )}
              {booking.deliveryPrice > 0 && (
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">{t('Delivery Price')}</Typography>
                  <Typography variant="body2">{formatCurrency(booking.deliveryPrice)}</Typography>
                </Grid>
              )}
              {booking.handoverPrice > 0 && (
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">{t('Handover Price')}</Typography>
                  <Typography variant="body2">{formatCurrency(booking.handoverPrice)}</Typography>
                </Grid>
              )}
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* All Modals */}
      <TimelineModal
        open={timelineModalOpen}
        onClose={() => setTimelineModalOpen(false)}
        bookingId={bookingId}
      />

      <ChangeStatusModal
        open={statusModalOpen}
        onClose={() => setStatusModalOpen(false)}
        bookingId={bookingId}
        currentStatus={booking.status}
        onStatusChanged={handleModalSuccess}
      />

      <ExtendModal
        open={extendModalOpen}
        onClose={() => setExtendModalOpen(false)}
        bookingId={bookingId}
        onSuccess={handleModalSuccess}
        currentEndDate={booking.dropOffDate}
        pricePerDay={booking.pricePerDay}
      />

      <AddNoteModal
        open={addNoteModalOpen}
        onClose={() => setAddNoteModalOpen(false)}
        bookingId={bookingId}
        onNoteAdded={handleModalSuccess}
      />

      <UpdateSuggestedPriceModal
        open={updatePriceModalOpen}
        onClose={() => setUpdatePriceModalOpen(false)}
        bookingId={bookingId}
        currentPrice={booking.suggestedPrice || booking.totalBookingPrice}
        originalPrice={booking.originalPrice || booking.totalBookingPrice}
        onPriceUpdated={handleModalSuccess}
      />

      <ChangeDurationModal
        open={changeDurationModalOpen}
        onClose={() => setChangeDurationModalOpen(false)}
        bookingId={bookingId}
        currentStartDate={booking.pickUpDate}
        currentEndDate={booking.dropOffDate}
        pricePerDay={booking.pricePerDay}
        totalPrice={booking.totalBookingPrice}
        onDurationChanged={handleModalSuccess}
      />

      <UpdateExtraServiceModal
        open={updateExtraServiceModalOpen}
        onClose={() => setUpdateExtraServiceModalOpen(false)}
        bookingId={bookingId}
        carId={booking.carId}
        allyId={booking.allyCompanyId}
        currentExtraServices={booking.rentalExtraServices || []}
        onServicesUpdated={handleModalSuccess}
      />
    </Box>
  );
}
