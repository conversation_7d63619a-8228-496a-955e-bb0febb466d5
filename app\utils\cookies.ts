/**
 * Cookie utility functions for the application
 */

/**
 * Get a cookie value by name
 * @param name - The name of the cookie to retrieve
 * @returns The cookie value or null if not found
 */
export const getCookie = (name: string): string | null => {
  if (typeof document === 'undefined') return null;

  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) {
    const cookieValue = parts.pop();
    return cookieValue ? cookieValue.split(';').shift() || null : null;
  }
  return null;
};

/**
 * Set a cookie with the specified options
 * @param name - The name of the cookie
 * @param value - The value to store
 * @param options - Cookie options (path, maxAge, secure, etc.)
 */
export const setCookie = (
  name: string,
  value: string,
  options: {
    path?: string;
    maxAge?: number;
    secure?: boolean;
    sameSite?: 'strict' | 'lax' | 'none';
  } = {}
): void => {
  if (typeof document === 'undefined') return;

  const defaultOptions = {
    path: '/',
    maxAge: 86400, // 1 day in seconds
    secure: true,
    sameSite: 'strict' as const,
  };

  const cookieOptions = { ...defaultOptions, ...options };

  let cookieString = `${name}=${value}`;
  if (cookieOptions.path) cookieString += `; path=${cookieOptions.path}`;
  if (cookieOptions.maxAge) cookieString += `; max-age=${cookieOptions.maxAge}`;
  if (cookieOptions.secure) cookieString += '; secure';
  if (cookieOptions.sameSite) cookieString += `; samesite=${cookieOptions.sameSite}`;

  document.cookie = cookieString;
};

/**
 * Remove a cookie by setting its expiration in the past
 * @param name - The name of the cookie to remove
 */
export const removeCookie = (name: string): void => {
  if (typeof document === 'undefined') return;
  document.cookie = `${name}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; secure; samesite=strict`;
};

/**
 * Get the authentication token from cookies or localStorage
 * @returns The auth token or null if not found
 */
export const getAuthToken = (): string | null => {
  // First check cookies
  const cookieToken = getCookie('token') || getCookie('auth_token');
  if (cookieToken) return cookieToken;

  // Then check localStorage (for backward compatibility)
  if (typeof window !== 'undefined') {
    try {
      const localToken = localStorage.getItem('token') || localStorage.getItem('auth_token');
      return localToken;
    } catch (e) {
      console.error('Error accessing localStorage:', e);
    }
  }

  return null;
};
