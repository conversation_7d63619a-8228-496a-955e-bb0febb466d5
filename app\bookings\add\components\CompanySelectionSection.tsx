import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  Autocomplete,
  TextField,
  CircularProgress,
} from '@mui/material';

interface Company {
  id: string;
  name: string;
  arName: string;
  enName: string;
}

interface CompanySelectionSectionProps {
  companies: Company[];
  selectedCompany: Company | null;
  onCompanyChange: (company: Company | null) => void;
  disabled?: boolean;
  clicked?: boolean;
  isLoading?: boolean;
}

export default function CompanySelectionSection({
  companies,
  selectedCompany,
  onCompanyChange,
  disabled = false,
  clicked = false,
  isLoading = false,
}: CompanySelectionSectionProps) {
  const { t } = useTranslation();

  const isRequired = true;
  const hasError = clicked && isRequired && !selectedCompany;

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        {t('selecting.company')}
      </Typography>

      <Box sx={{ mt: 2 }}>
        <FormControl fullWidth error={hasError}>
          <Autocomplete
            options={companies || []}
            value={selectedCompany}
            onChange={(event, newValue) => onCompanyChange(newValue)}
            getOptionLabel={(option) => option.arName || option.name}
            isOptionEqualToValue={(option, value) => option.id === value.id}
            loading={isLoading}
            disabled={disabled || isLoading}
            renderInput={(params) => (
              <TextField
                {...params}
                label={t('selecting.company')}
                placeholder={t('selecting.company')}
                error={hasError}
                helperText={hasError ? t('This field is required') : ''}
                InputProps={{
                  ...params.InputProps,
                  endAdornment: (
                    <>
                      {isLoading ? <CircularProgress color="inherit" size={20} /> : null}
                      {params.InputProps.endAdornment}
                    </>
                  ),
                }}
              />
            )}
            renderOption={(props, option) => (
              <Box component="li" {...props}>
                {option.arName || option.name}
              </Box>
            )}
          />
        </FormControl>
      </Box>
    </Paper>
  );
}
