'use client';
import { useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { getAuthToken } from '../../../utils/cookies';
import PageLayout from '../../../PageLayout';
import AddEditBookingContent from '../../add/AddEditBookingContent';

export default function EditBooking() {
  const router = useRouter();
  const params = useParams();
  const bookingId = params.id as string;

  useEffect(() => {
    // Check for token in cookies
    const token = getAuthToken();
    if (!token) {
      router.replace('/login');
    }
  }, [router]);

  return (
    // <PageLayout>
    <AddEditBookingContent mode="edit" bookingId={bookingId} />
    // </PageLayout>
  );
}
