import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  Autocomplete,
  TextField,
  CircularProgress,
  Grid,
} from '@mui/material';

interface Branch {
  id: string;
  name: string;
  arName: string;
  enName: string;
  location?: {
    lat: number;
    lng: number;
  };
}

interface BranchSelectionSectionProps {
  branches: Branch[];
  selectedPickupBranch: Branch | null;
  selectedDropoffBranch: Branch | null;
  onPickupBranchChange: (branch: Branch | null) => void;
  onDropoffBranchChange: (branch: Branch | null) => void;
  disabled?: boolean;
  clicked?: boolean;
  isLoading?: boolean;
  showDropoffBranch: boolean;
  showPickupBranch?: boolean;
  hasSelectedCompany: boolean;
}

export default function BranchSelectionSection({
  branches,
  selectedPickupBranch,
  selectedDropoffBranch,
  onPickupBranchChange,
  onDropoffBranchChange,
  disabled = false,
  clicked = false,
  isLoading = false,
  showDropoffBranch,
  showPickupBranch = true,
  hasSelectedCompany,
}: BranchSelectionSectionProps) {
  const { t } = useTranslation();

  const isRequired = true;
  const hasPickupError = clicked && isRequired && showPickupBranch && !selectedPickupBranch;
  const hasDropoffError = clicked && isRequired && showDropoffBranch && !selectedDropoffBranch;

  // Filter out selected pickup branch from dropoff options
  const dropoffBranchOptions =
    branches?.filter((branch) => !selectedPickupBranch || branch.id !== selectedPickupBranch.id) ||
    [];

  // Don't render if both pickup and dropoff are disabled
  if (!showPickupBranch && !showDropoffBranch) {
    return null;
  }

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        {showPickupBranch && showDropoffBranch
          ? t('selecting.branch')
          : showPickupBranch
          ? t('select.pickup.branch')
          : t('select.dropoff.branch')}
      </Typography>

      <Grid container spacing={2}>
        {/* Pickup Branch */}
        {showPickupBranch && (
          <Grid item xs={12} md={showDropoffBranch ? 6 : 12}>
            <FormControl fullWidth error={hasPickupError}>
              <Autocomplete
                options={branches || []}
                value={selectedPickupBranch}
                onChange={(event, newValue) => onPickupBranchChange(newValue)}
                getOptionLabel={(option) => option.arName || option.name}
                isOptionEqualToValue={(option, value) => option.id === value.id}
                loading={isLoading}
                disabled={disabled || isLoading || !hasSelectedCompany}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label={t('select.pickup.branch')}
                    placeholder={
                      hasSelectedCompany ? t('select.pickup.branch') : t('selecting.company.first')
                    }
                    error={hasPickupError}
                    helperText={hasPickupError ? t('This field is required') : ''}
                    InputProps={{
                      ...params.InputProps,
                      endAdornment: (
                        <>
                          {isLoading ? <CircularProgress color="inherit" size={20} /> : null}
                          {params.InputProps.endAdornment}
                        </>
                      ),
                    }}
                  />
                )}
                renderOption={(props, option) => (
                  <Box component="li" {...props}>
                    {option.arName || option.name}
                  </Box>
                )}
              />
            </FormControl>
          </Grid>
        )}

        {/* Dropoff Branch */}
        {showDropoffBranch && (
          <Grid item xs={12} md={showPickupBranch ? 6 : 12}>
            <FormControl fullWidth error={hasDropoffError}>
              <Autocomplete
                options={dropoffBranchOptions}
                value={selectedDropoffBranch}
                onChange={(event, newValue) => onDropoffBranchChange(newValue)}
                getOptionLabel={(option) => option.arName || option.name}
                isOptionEqualToValue={(option, value) => option.id === value.id}
                loading={isLoading}
                disabled={
                  disabled ||
                  isLoading ||
                  !hasSelectedCompany ||
                  (showPickupBranch && !selectedPickupBranch)
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label={t('select.dropoff.branch')}
                    placeholder={
                      !showPickupBranch || selectedPickupBranch
                        ? t('select.dropoff.branch')
                        : t('select.pickup.branch.first')
                    }
                    error={hasDropoffError}
                    helperText={hasDropoffError ? t('This field is required') : ''}
                    InputProps={{
                      ...params.InputProps,
                      endAdornment: (
                        <>
                          {isLoading ? <CircularProgress color="inherit" size={20} /> : null}
                          {params.InputProps.endAdornment}
                        </>
                      ),
                    }}
                  />
                )}
                renderOption={(props, option) => (
                  <Box component="li" {...props}>
                    {option.arName || option.name}
                  </Box>
                )}
              />
            </FormControl>
          </Grid>
        )}
      </Grid>
    </Paper>
  );
}
