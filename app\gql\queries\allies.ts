import { gql } from "@apollo/client";

 export const GET_ALLIES = gql`
   query AllyCompanies($limit: Int, $page: Int )
   {
    allyCompanies(
      limit: $limit,
      page: $page
    ) {
      collection {
        addedBy
        address
        allyClass
        allyRateId
        arName
        bankCardImage
        canHandoverInAntherCity
        commercialRegestration
        commercialRegistrationImage
        commisionRate
        conditions
        email
        enName
        features
        id
        isActive
        isApiIntegrated
        isB2b
        isB2c
        isDeliveryOnlinePay
        isExtendFixedPrice
        isOnlinePayEnable
        lat
        licenceImage
        lng
        logo
        lonlat
        managerName
        name
        officeNumber
        phoneNumber
        rate
        updatedBy
      }
      metadata {
        currentPage
        limitValue
        totalCount
        totalPages
      }
    }
   }
  `;