'use client';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { getAuthToken } from '../../utils/cookies';
import PageLayout from '../../PageLayout';
import AddEditBookingContent from './AddEditBookingContent';

export default function AddBooking() {
  const router = useRouter();

  useEffect(() => {
    // Check for token in cookies
    const token = getAuthToken();
    if (!token) {
      router.replace('/login');
    }
  }, [router]);

  return (
    <PageLayout>
      <AddEditBookingContent mode="add" />
    </PageLayout>
  );
}
