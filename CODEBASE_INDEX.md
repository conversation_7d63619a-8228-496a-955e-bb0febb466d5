# Dashboard Application Codebase Index

## Project Overview
This is a Next.js dashboard application for Carwah, built with modern web technologies. The application provides an administrative interface with features like statistics, bookings management, and banner management.

## Tech Stack
- **Framework**: Next.js 15 with App Router
- **UI Library**: Material UI (MUI) v5
- **State Management**: Redux Toolkit
- **API Communication**: Apollo Client (GraphQL)
- **Form Handling**: React Hook Form with Zod/Yup validation
- **Internationalization**: i18next
- **Styling**: SCSS modules and CSS modules
- **Authentication**: Token-based authentication

## Project Structure

### Root Configuration Files
- `next.config.ts` - Next.js configuration
- `tsconfig.json` - TypeScript configuration
- `.env.example` - Environment variables template
- `package.json` - Dependencies and scripts

### Core Application Structure
- `/app` - Main application directory (Next.js App Router)
  - `layout.tsx` - Root layout with providers (Apollo, Redux, Theme, i18n)
  - `PageLayout.tsx` - Dashboard layout with navigation drawer and app bar
  - `/login` - Authentication pages
  - `/statistics` - Dashboard statistics
  - `/bookings` - Bookings management
  - `/banners` - Banner management

### Key Directories

#### State Management
- `/app/store` - Redux store configuration
  - `store.ts` - Store setup
  - `/slices` - Redux slices (e.g., authSlice.ts)
  - `hooks.ts` - Typed hooks for Redux

#### API & Data Fetching
- `/app/gql` - GraphQL configuration
  - `apollo-client.ts` - Apollo Client setup with error handling
  - `/mutations` - GraphQL mutations
  - `/queries` - GraphQL queries

#### Models & Types
- `/app/models` - TypeScript interfaces and types
  - `authentication.models.ts` - Auth-related types
  - `interfaces.model.ts` - Common interfaces
  - `table.model.ts` - Data table models

#### Utilities
- `/app/utils` - Utility functions
  - `cookies.ts` - Cookie management
  - `network.ts` - Network utilities (throttling, retries)
  - `signature.ts` - Request signing
  - `toast.ts` - Toast notifications
  - `language.ts` - Language utilities

#### Hooks
- `/app/hooks` - Custom React hooks
  - `useAuth.ts` - Authentication hook

#### Internationalization
- `/app/localization` - i18n configuration
  - `next-i18next.config.ts` - i18n setup
  - `en.json` - English translations
  - `ar.json` - Arabic translations

#### Public Assets
- `/public` - Static assets
  - `/fonts` - Custom fonts
  - `logo.png`, `fullLogo.svg` - Brand assets

## Key Features

### Authentication
- Login system with form validation
- Token-based authentication with cookie storage
- Protected routes with authentication checks

### Dashboard Layout
- Responsive drawer navigation
- Light/dark mode toggle
- Language switching (English/Arabic)
- RTL support for Arabic
- Accessibility features (high contrast mode)

### Data Management
- GraphQL API integration with Apollo Client
- Error handling and retry mechanisms
- Rate limiting and request throttling
- Loading states and error messages

### UI Components
- Material UI components with custom theming
- Data tables for information display
- Charts and statistics visualization
- Form components with validation

## Application Flow
1. User authentication through login page
2. Token stored in cookies for session management
3. Protected routes check for authentication
4. Dashboard layout with navigation to different sections
5. Data fetching through GraphQL with Apollo Client
6. State management with Redux for global state
7. Localized UI with i18next for multilingual support

## Development Practices
- Client-side rendering with Next.js
- TypeScript for type safety
- Component-based architecture
- Responsive design for multiple device sizes
- Form validation with schema-based validation libraries
- Error handling and user feedback