'use client';
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Collapse,
  IconButton,
  Stack,
  Button,
  Tooltip,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  ContentCopy as CopyIcon,
  Directions as DirectionsIcon,
  LocationOn,
} from '@mui/icons-material';

// Google Maps type declarations
declare global {
  interface Window {
    google: typeof google;
  }
}

declare namespace google {
  namespace maps {
    class Map {
      constructor(mapDiv: Element, opts?: MapOptions);
      setZoom(zoom: number): void;
    }

    class Marker {
      constructor(opts?: MarkerOptions);
      addListener(eventName: string, handler: Function): void;
    }

    class InfoWindow {
      constructor(opts?: InfoWindowOptions);
      open(map: Map, anchor?: Marker): void;
    }

    class DirectionsService {
      route(
        request: DirectionsRequest,
        callback: (result: DirectionsResult | null, status: DirectionsStatus) => void
      ): void;
    }

    class DirectionsRenderer {
      constructor(opts?: DirectionsRendererOptions);
      setDirections(directions: DirectionsResult): void;
    }

    class Size {
      constructor(width: number, height: number);
    }

    class Point {
      constructor(x: number, y: number);
    }

    interface MapOptions {
      center?: LatLng | LatLngLiteral;
      zoom?: number;
      mapTypeId?: MapTypeId;
      streetViewControl?: boolean;
      mapTypeControl?: boolean;
      fullscreenControl?: boolean;
      zoomControl?: boolean;
    }

    interface MarkerOptions {
      position?: LatLng | LatLngLiteral;
      map?: Map;
      title?: string;
      icon?: string | Icon;
    }

    interface Icon {
      url: string;
      scaledSize?: Size;
      anchor?: Point;
    }

    interface InfoWindowOptions {
      content?: string;
    }

    interface DirectionsRendererOptions {
      map?: Map;
      suppressMarkers?: boolean;
    }

    interface DirectionsRequest {
      origin: LatLng | LatLngLiteral | string;
      destination: LatLng | LatLngLiteral | string;
      travelMode: TravelMode;
    }

    interface DirectionsResult {
      routes: DirectionsRoute[];
    }

    interface DirectionsRoute {
      legs: DirectionsLeg[];
    }

    interface DirectionsLeg {
      distance: { value: number; text: string };
    }

    interface LatLng {
      lat(): number;
      lng(): number;
    }

    interface LatLngLiteral {
      lat: number;
      lng: number;
    }

    enum MapTypeId {
      ROADMAP = 'roadmap',
      SATELLITE = 'satellite',
      HYBRID = 'hybrid',
      TERRAIN = 'terrain',
    }

    enum TravelMode {
      DRIVING = 'DRIVING',
      WALKING = 'WALKING',
      BICYCLING = 'BICYCLING',
      TRANSIT = 'TRANSIT',
    }

    enum DirectionsStatus {
      OK = 'OK',
      NOT_FOUND = 'NOT_FOUND',
      ZERO_RESULTS = 'ZERO_RESULTS',
      MAX_WAYPOINTS_EXCEEDED = 'MAX_WAYPOINTS_EXCEEDED',
      INVALID_REQUEST = 'INVALID_REQUEST',
      OVER_QUERY_LIMIT = 'OVER_QUERY_LIMIT',
      REQUEST_DENIED = 'REQUEST_DENIED',
      UNKNOWN_ERROR = 'UNKNOWN_ERROR',
    }
  }
}

// Define types
interface Branch {
  id: string;
  name: string;
  address: string;
  latitude: number;
  longitude: number;
  phone?: string;
}

interface GoogleMapComponentProps {
  latitude: number;
  longitude: number;
  branch?: Branch;
  height?: string;
  zoom?: number;
  showDirections?: boolean;
  showBranchDetails?: boolean;
}

const GoogleMapComponent: React.FC<GoogleMapComponentProps> = ({
  latitude,
  longitude,
  branch,
  height = '400px',
  zoom = 15,
  showDirections = true,
  showBranchDetails = true,
}) => {
  const { t } = useTranslation();
  const [expanded, setExpanded] = useState(true);
  const [distance, setDistance] = useState<number | null>(null);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [directionsService, setDirectionsService] = useState<google.maps.DirectionsService | null>(
    null
  );
  const [directionsRenderer, setDirectionsRenderer] =
    useState<google.maps.DirectionsRenderer | null>(null);
  const mapRef = useRef<HTMLDivElement>(null);

  const branchDetails = branch
    ? [
        { label: t('Branch Name'), value: branch.name },
        { label: t('Branch Address'), value: branch.address },
        { label: t('Branch Phone'), value: branch.phone },
      ].filter((item) => item.value)
    : [];

  // Load Google Maps API
  useEffect(() => {
    const loadGoogleMaps = async () => {
      try {
        const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;

        if (!apiKey) {
          throw new Error('Google Maps API key is not configured');
        }

        // Check if Google Maps is already loaded
        if (window.google && window.google.maps) {
          initializeMap();
          return;
        }

        // Load Google Maps script
        const script = document.createElement('script');
        script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=geometry,places`;
        script.async = true;
        script.defer = true;

        script.onload = () => {
          initializeMap();
        };

        script.onerror = () => {
          setError(t('map.mapError'));
          setIsLoading(false);
        };

        document.head.appendChild(script);
      } catch (err) {
        setError(err instanceof Error ? err.message : t('map.mapError'));
        setIsLoading(false);
      }
    };

    loadGoogleMaps();
  }, [t]);

  const initializeMap = () => {
    if (!mapRef.current) return;
    // Validate coordinates
    if (!latitude || !longitude || latitude === 0 || longitude === 0) {
      console.error('Invalid coordinates:', { latitude, longitude });
      setError(t('map.invalidCoordinates'));
      setIsLoading(false);
      return;
    }

    try {
      const mapOptions: google.maps.MapOptions = {
        center: { lat: latitude, lng: longitude },
        zoom: zoom,
        mapTypeId: google.maps.MapTypeId.ROADMAP,
        streetViewControl: false,
        mapTypeControl: false,
        fullscreenControl: true,
        zoomControl: true,
      };

      const newMap = new google.maps.Map(mapRef.current, mapOptions);

      // Add marker for the location
      const marker = new google.maps.Marker({
        position: { lat: latitude, lng: longitude },
        map: newMap,
        title: branch?.name || t('map.location'),
        // Use default marker instead of custom SVG for now
      });

      // Add info window if branch details are available
      if (branch && showBranchDetails) {
        const infoWindow = new google.maps.InfoWindow({
          content: `
            <div style="padding: 10px; max-width: 250px;">
              <h3 style="margin: 0 0 8px 0; color: #1976d2;">${branch.name}</h3>
              <p style="margin: 0 0 8px 0; color: #666;">${
                branch.address || 'Address not available'
              }</p>
              ${
                branch.phone
                  ? `<p style="margin: 0; color: #666;"><strong>${t('map.branchPhone')}:</strong> ${
                      branch.phone
                    }</p>`
                  : ''
              }
            </div>
          `,
        });

        marker.addListener('click', () => {
          infoWindow.open(newMap, marker);
        });
      }

      setMap(newMap);
      setDirectionsService(new google.maps.DirectionsService());
      setDirectionsRenderer(
        new google.maps.DirectionsRenderer({
          map: newMap,
          suppressMarkers: false,
        })
      );

      setIsLoading(false);
    } catch (err) {
      console.error('Error initializing map:', err);
      setError(t('map.mapError'));
      setIsLoading(false);
    }
  };

  const handleGetDirections = () => {
    if (!navigator.geolocation) {
      alert(t('map.geolocationNotSupported'));
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const userLocation = {
          lat: position.coords.latitude,
          lng: position.coords.longitude,
        };

        if (directionsService && directionsRenderer && map) {
          directionsService.route(
            {
              origin: userLocation,
              destination: { lat: latitude, lng: longitude },
              travelMode: google.maps.TravelMode.DRIVING,
            },
            (result, status) => {
              if (status === google.maps.DirectionsStatus.OK && result) {
                directionsRenderer.setDirections(result);
              } else {
                alert(`${t('map.directionsError')}: ${status}`);
              }
            }
          );
        }
      },
      (error) => {
        alert(`${t('map.locationError')}: ${error.message}`);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000,
      }
    );
  };

  const handleOpenInGoogleMaps = () => {
    const url = `https://www.google.com/maps/dir/?api=1&destination=${latitude},${longitude}`;
    window.open(url, '_blank');
  };

  const handleCopyLocation = async () => {
    const locationText = `${latitude}, ${longitude}`;

    try {
      await navigator.clipboard.writeText(locationText);
      alert(t('map.locationCopied'));
    } catch (err) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = locationText;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      alert(t('map.locationCopied'));
    }
  };

  if (error) {
    return (
      <Box sx={{ height, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Alert severity="error" sx={{ maxWidth: 400 }}>
          <Typography variant="h6" gutterBottom>
            {t('map.mapError')}
          </Typography>
          <Typography variant="body2">{error}</Typography>
          <Typography variant="caption" display="block" sx={{ mt: 1 }}>
            {t('map.checkConnection')}
          </Typography>
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ position: 'relative', height }}>
      {isLoading && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            zIndex: 1,
          }}
        >
          <CircularProgress />
        </Box>
      )}

      <div
        ref={mapRef}
        style={{
          width: '100%',
          height: '100%',
          borderRadius: '8px',
          border: '1px solid #e0e0e0',
        }}
      />

      {!isLoading && (
        <Box
          sx={{
            position: 'absolute',
            top: 10,
            right: 10,
            display: 'flex',
            flexDirection: 'column',
            gap: 1,
            zIndex: 2,
          }}
        >
          {showDirections && (
            <Button
              variant="contained"
              size="small"
              startIcon={<DirectionsIcon />}
              onClick={handleGetDirections}
              sx={{
                backgroundColor: '#4caf50',
                color: 'white',
                boxShadow: 2,
                '&:hover': {
                  backgroundColor: '#388e3c',
                },
              }}
            >
              {t('map.directions')}
            </Button>
          )}

          <Button
            variant="contained"
            size="small"
            startIcon={<LocationOn />}
            onClick={handleOpenInGoogleMaps}
            sx={{
              backgroundColor: '#2196f3',
              color: 'white',
              boxShadow: 2,
              '&:hover': {
                backgroundColor: '#1976d2',
              },
            }}
          >
            {t('map.openInMaps')}
          </Button>

          <Button
            variant="contained"
            size="small"
            startIcon={<CopyIcon />}
            onClick={handleCopyLocation}
            sx={{
              backgroundColor: '#ff9800',
              color: 'white',
              boxShadow: 2,
              '&:hover': {
                backgroundColor: '#f57c00',
              },
            }}
          >
            {t('map.copyLocation')}
          </Button>
        </Box>
      )}

      {branch && showBranchDetails && !isLoading && (
        <Box
          sx={{
            position: 'absolute',
            bottom: 10,
            left: '50%',
            transform: 'translateX(-50%)',
            width: '80%',
            maxWidth: '400px',
            backgroundColor: 'white',
            borderRadius: 2,
            boxShadow: 2,
            zIndex: 2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-around',
          }}
        >
          <Typography variant="h6" color="primary" gutterBottom>
            {branch.name}
          </Typography>
          <Typography variant="body2" margin={0} color="text.secondary" gutterBottom>
            {branch.address}
          </Typography>
          {branch.phone && (
            <Typography variant="body2" color="text.secondary">
              <strong>{t('map.branchPhone')}:</strong> {branch.phone}
            </Typography>
          )}
        </Box>
      )}
    </Box>
  );
};

export default GoogleMapComponent;
