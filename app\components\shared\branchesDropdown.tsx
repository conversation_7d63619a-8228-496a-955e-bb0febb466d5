import React from 'react';
import { <PERSON>complete, TextField, Chip, CircularProgress } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@apollo/client';
import { GET_Branches } from '../../gql/queries/branches';

interface Ally {
  id: number | string;
  name: string;
  arName: string;
}

interface BranchesDropdownProps {
  filters: {
    allyCompanyId?: string | null;
    [key: string]: any;
  };
  handleAutocompleteChange: (field: string, value: string | null) => void;
}

const BranchesDropdown: React.FC<BranchesDropdownProps> = ({ 
  filters, 
  handleAutocompleteChange
}) => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  
  const { loading, error, data } = useQuery(GET_Branches, {
    fetchPolicy: 'cache-and-network',
    variables: {
        limit: 1000,
        page: 1,
        allyCompanyIds:  filters.allyCompanyId ? [filters.allyCompanyId] : undefined,
    }
  });

  const Branches = data?.branches?.collection || [];
  return (
    <Autocomplete 
      id="allyCompanyId" 
      options={Branches} 
      loading={loading}
      getOptionLabel={(option) => (isRTL ? option.arName : option.name)} 
      value={ 
        filters.allyCompanyId 
          ? Branches.find((ally:any) => ally.id.toString() === filters.allyCompanyId) || null 
          : null 
      } 
      onChange={(_, newValue) => 
        handleAutocompleteChange('allyCompanyId', newValue?.id?.toString() || null) 
      } 
      renderInput={(params) => ( 
        <TextField 
          {...params} 
          label={t('branches')} 
          variant="outlined" 
          size="small" 
          InputLabelProps={{ 
            sx: { padding: '0 20px' }, 
          }}
          InputProps={{
            ...params.InputProps,
            endAdornment: (
              <>
                {loading ? <CircularProgress color="inherit" size={20} /> : null}
                {params.InputProps.endAdornment}
              </>
            ),
          }}
          error={!!error}
          helperText={error ? t('Error loading Branches') : ''}
        /> 
      )} 
      renderTags={(value, getTagProps) => 
        value.map((option, index) => ( 
          <Chip 
            label={isRTL ? option.arName : option.name} 
            {...getTagProps({ index })} 
            size="small" 
          /> 
        )) 
      }
    />
  );
};

export default BranchesDropdown;