'use client';
import { useState, useEffect } from 'react';
import {
  Box,
  Drawer,
  AppBar,
  Too<PERSON><PERSON>,
  List,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Container,
  Tooltip,
  Menu,
  MenuItem,
  Switch,
  FormControlLabel,
  useMediaQuery,
  Avatar,
  useTheme,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard,
  DirectionsCar,
  People,
  Settings,
  Business,
  CardGiftcard,
  Security,
  Translate,
  AccessibilityNew,
  Brightness4,
  Brightness7,
  ExpandLess,
  ExpandMore,
  Logout,
} from '@mui/icons-material';
import Link from 'next/link';
import Image from 'next/image';
import { useTranslation } from 'react-i18next';
import '../app/localization/next-i18next.config';

const drawerWidth = 240;

const PageLayout = ({ children }: { children: React.ReactNode }) => {
  const [mobileOpen, setMobileOpen] = useState(false);
  const [darkMode, setDarkMode] = useState(false);
  const [highContrast, setHighContrast] = useState(false);
  const [largeText, setLargeText] = useState(false);
  const [reducedMotion, setReducedMotion] = useState(false);
  const [languageAnchorEl, setLanguageAnchorEl] = useState<null | HTMLElement>(null);
  const [accessibilityAnchorEl, setAccessibilityAnchorEl] = useState<null | HTMLElement>(null);
  const [expandedItems, setExpandedItems] = useState<{ [key: string]: boolean }>({});
  // Use the theme with current mode and contrast settings
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { t, i18n } = useTranslation();

  // Load user preferences from localStorage on component mount
  useEffect(() => {
    const savedDarkMode = localStorage.getItem('darkMode');
    const savedHighContrast = localStorage.getItem('highContrast');
    const savedLargeText = localStorage.getItem('largeText');
    const savedReducedMotion = localStorage.getItem('reducedMotion');

    if (savedDarkMode) {
      setDarkMode(savedDarkMode === 'true');
    }

    if (savedHighContrast) {
      setHighContrast(savedHighContrast === 'true');
    }

    if (savedLargeText) {
      setLargeText(savedLargeText === 'true');
    }

    if (savedReducedMotion) {
      setReducedMotion(savedReducedMotion === 'true');
    }
  }, []);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleLanguageMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setLanguageAnchorEl(event.currentTarget);
  };

  const handleLanguageMenuClose = () => {
    setLanguageAnchorEl(null);
  };

  const handleLanguageChange = (language: string) => {
    i18n.changeLanguage(language);
    localStorage.setItem('preferredLanguage', language);
    handleLanguageMenuClose();
  };

  const handleAccessibilityMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAccessibilityAnchorEl(event.currentTarget);
  };

  const handleAccessibilityMenuClose = () => {
    setAccessibilityAnchorEl(null);
  };

  const toggleDarkMode = () => {
    const newDarkMode = !darkMode;
    setDarkMode(newDarkMode);
    localStorage.setItem('darkMode', newDarkMode.toString());

    // Update data attribute for CSS selectors
    document.documentElement.setAttribute('data-theme-mode', newDarkMode ? 'dark' : 'light');

    // Force a repaint of scrollbars
    const style = document.createElement('style');
    style.textContent = '* { }'; // Empty rule to force a repaint
    document.head.appendChild(style);

    // Apply scrollbar styles directly
    const scrollbarStyle = document.createElement('style');
    scrollbarStyle.textContent = newDarkMode
      ? `*::-webkit-scrollbar-thumb { background: #f1922380 !important; } *::-webkit-scrollbar-track { background: rgba(30, 30, 30, 0.9) !important; }`
      : `*::-webkit-scrollbar-thumb { background: rgba(241, 146, 35, 0.5) !important; } *::-webkit-scrollbar-track { background: rgba(0, 0, 0, 0.05) !important; }`;
    document.head.appendChild(scrollbarStyle);

    // Remove temporary styles after a delay
    setTimeout(() => {
      document.head.removeChild(style);
      document.head.removeChild(scrollbarStyle);
      window.location.reload();
    }, 200);
  };

  const toggleHighContrast = () => {
    setHighContrast(!highContrast);
    localStorage.setItem('highContrast', (!highContrast).toString());
  };

  const toggleLargeText = () => {
    setLargeText(!largeText);
    localStorage.setItem('largeText', (!largeText).toString());
  };

  const toggleReducedMotion = () => {
    setReducedMotion(!reducedMotion);
    localStorage.setItem('reducedMotion', (!reducedMotion).toString());
  };

  const toggleExpandItem = (text: string) => {
    setExpandedItems({
      ...expandedItems,
      [text]: !expandedItems[text],
    });
  };

  useEffect(() => {
    const savedLanguage = localStorage.getItem('preferredLanguage');
    if (savedLanguage) {
      i18n.changeLanguage(savedLanguage);
    }

    const savedDarkMode = localStorage.getItem('darkMode');
    if (savedDarkMode) {
      setDarkMode(savedDarkMode === 'true');
    }

    const savedHighContrast = localStorage.getItem('highContrast');
    if (savedHighContrast) {
      setHighContrast(savedHighContrast === 'true');
    }

    const savedLargeText = localStorage.getItem('largeText');
    if (savedLargeText) {
      setLargeText(savedLargeText === 'true');
    }

    const savedReducedMotion = localStorage.getItem('reducedMotion');
    if (savedReducedMotion) {
      setReducedMotion(savedReducedMotion === 'true');
    }

    // Check prefers-reduced-motion media query
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
    if (prefersReducedMotion.matches && localStorage.getItem('reducedMotion') === null) {
      setReducedMotion(true);
      localStorage.setItem('reducedMotion', 'true');
    }
  }, [i18n]);

  const languageMenuOpen = Boolean(languageAnchorEl);
  const accessibilityMenuOpen = Boolean(accessibilityAnchorEl);

  const isRTL = i18n.language === 'ar';
  const direction = isRTL ? 'rtl' : 'ltr';

  const menuItems = [
    { text: t('Statistics'), icon: <Dashboard />, path: '/statistics' },
    { text: t('Bookings'), icon: <CardGiftcard />, path: '/bookings' },
    { text: t('Customers'), icon: <People />, path: '/customers' },
    {
      text: t('Allies'),
      icon: <Business />,
      subItems: [
        { text: t('Ally Companies'), path: '/companies' },
        { text: t('Branches'), path: '/branches' },
        { text: t('Managers'), path: '/managers' },
        { text: t('Allies Rate'), path: '/rates' },
      ],
    },
    {
      text: t('Cars'),
      icon: <DirectionsCar />,
      subItems: [
        { text: t('Listing Cars'), path: '/cars' },
        { text: t('Makes'), path: '/makes' },
        { text: t('Models'), path: '/models' },
        { text: t('Car Versions'), path: '/carVersions' },
        { text: t('Features'), path: '/features' },
      ],
    },
    {
      text: t('Settings'),
      icon: <Settings />,
      subItems: [
        { text: t('Banners'), path: '/banners' },
        { text: t('Extra Service'), path: '/extraservices' },
        { text: t('Coupon'), path: '/coupons' },
        { text: t('Packages'), path: '/packages' },
        { text: t('Loyalty Partners'), path: '/partners' },
      ],
    },
    {
      text: t('Roles & Permissions'),
      icon: <Security />,
      subItems: [
        { text: t('Banners'), path: '/banners' },
        { text: t('Roles'), path: '/roles' },
        { text: t('Users'), path: '/users' },
      ],
    },
  ];

  const drawer = (
    <Box
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        direction,
        pt: 10,
      }}
    >
      <List
        sx={{
          flexGrow: 1,
          overflowY: 'auto',
          p: 0,
        }}
      >
        {menuItems.map((item) => (
          <Box key={item.text}>
            {item.subItems ? (
              <>
                <ListItem
                  disablePadding
                  sx={{
                    display: 'block',
                    mb: 0.5,
                  }}
                >
                  <ListItemButton
                    onClick={() => toggleExpandItem(item.text)}
                    sx={{
                      minHeight: 48,
                      px: 2.5,
                      borderRadius: '8px',
                      mx: 1,
                      '&:hover': {
                        backgroundColor: 'rgba(74, 169, 206, 0.08)',
                      },
                    }}
                  >
                    <ListItemIcon
                      sx={{
                        minWidth: 0,
                        mr: isRTL ? 0 : 2,
                        ml: isRTL ? 2 : 0,
                        color: 'primary.main',
                      }}
                    >
                      {item.icon}
                    </ListItemIcon>
                    <ListItemText
                      primary={item.text}
                      primaryTypographyProps={{
                        fontSize: 14,
                        fontWeight: 'medium',
                        textAlign: isRTL ? 'right' : 'left',
                      }}
                    />
                    {expandedItems[item.text] ? <ExpandLess /> : <ExpandMore />}
                  </ListItemButton>

                  <Box
                    sx={{
                      display: expandedItems[item.text] ? 'block' : 'none',
                      pl: isRTL ? 0 : 4,
                      pr: isRTL ? 4 : 0,
                    }}
                  >
                    {item.subItems.map((subItem) => (
                      <Link
                        href={subItem.path}
                        key={subItem.path}
                        style={{ textDecoration: 'none' }}
                      >
                        <ListItemButton
                          sx={{
                            minHeight: 36,
                            px: 2,
                            borderRadius: '8px',
                            mx: 1,
                            my: 0.5,
                            '&:hover': {
                              backgroundColor: 'rgba(74, 169, 206, 0.08)',
                            },
                          }}
                        >
                          <ListItemText
                            primary={subItem.text}
                            primaryTypographyProps={{
                              fontSize: 13,
                              color: 'text.secondary',
                              textAlign: isRTL ? 'right' : 'left',
                            }}
                          />
                        </ListItemButton>
                      </Link>
                    ))}
                  </Box>
                </ListItem>
              </>
            ) : (
              <ListItem
                disablePadding
                sx={{
                  display: 'block',
                  mb: 0.5,
                }}
              >
                <Link href={item.path} style={{ textDecoration: 'none' }}>
                  <ListItemButton
                    sx={{
                      minHeight: 48,
                      px: 2.5,
                      borderRadius: '8px',
                      mx: 1,
                      '&:hover': {
                        backgroundColor: 'rgba(74, 169, 206, 0.08)',
                      },
                    }}
                  >
                    <ListItemIcon
                      sx={{
                        minWidth: 0,
                        mr: isRTL ? 0 : 2,
                        ml: isRTL ? 2 : 0,
                        color: 'primary.main',
                      }}
                    >
                      {item.icon}
                    </ListItemIcon>
                    <ListItemText
                      primary={item.text}
                      primaryTypographyProps={{
                        fontSize: 14,
                        fontWeight: 'medium',
                        textAlign: isRTL ? 'right' : 'left',
                      }}
                    />
                  </ListItemButton>
                </Link>
              </ListItem>
            )}
          </Box>
        ))}
      </List>
      <Divider />
      <Box sx={{ p: 2, textAlign: isRTL ? 'right' : 'center' }}>
        <Typography variant="caption" color="text.secondary">
          © {new Date().getFullYear()} {t('Carwah')}. {t('All rights reserved')}.
        </Typography>
      </Box>
    </Box>
  );

  return (
    <Box
      sx={{
        display: 'flex',
        direction,
        ...(largeText && {
          fontSize: '1.25rem',
          '& .MuiTypography-root': {
            fontSize: '1.25em !important',
          },
          '& .MuiButton-root': {
            fontSize: '1.15em !important',
            padding: '10px 20px !important',
          },
          '& .MuiMenuItem-root': {
            fontSize: '1.15em !important',
          },
          '& .MuiListItemText-primary': {
            fontSize: '1.15em !important',
          },
          '& .MuiInputBase-root': {
            fontSize: '1.15em !important',
          },
          '& .MuiFormLabel-root': {
            fontSize: '1.15em !important',
          },
          '& .MuiInputLabel-root': {
            fontSize: '1.15em !important',
          },
          '& th, & td': {
            fontSize: '1.15em !important',
          },
          '& p, & span, & div, & label, & a': {
            fontSize: '1.15em !important',
          },
          '& input, & select, & textarea': {
            fontSize: '1.15em !important',
          },
          '& .MuiTableCell-root': {
            padding: '16px !important',
          },
          '& .MuiListItem-root': {
            padding: '12px 16px !important',
          },
        }),
        ...(highContrast && {
          '& .MuiPaper-root': {
            backgroundColor: darkMode ? '#121212 !important' : '#ffffff !important',
            color: darkMode ? '#ffffff !important' : '#000000 !important',
          },
          '& .MuiAppBar-root': {
            backgroundColor: darkMode ? '#000000 !important' : '#ffffff !important',
            color: darkMode ? '#ffffff !important' : '#000000 !important',
          },
          '& .MuiDrawer-paper': {
            backgroundColor: darkMode ? '#121212 !important' : '#f5f5f5 !important',
            color: darkMode ? '#ffffff !important' : '#000000 !important',
          },
          '& .MuiCard-root': {
            backgroundColor: darkMode ? '#1E1E1E !important' : '#FFFFFF !important',
            color: darkMode ? '#FFFFFF !important' : '#000000 !important',
            border: darkMode ? '1px solid #444 !important' : '1px solid #ccc !important',
          },
          '& .MuiIconButton-root': {
            color: darkMode ? '#ffffff !important' : '#000000 !important',
          },
          '& .MuiButton-contained': {
            backgroundColor: darkMode ? '#3a3a3a !important' : '#e0e0e0 !important',
            color: darkMode ? '#ffffff !important' : '#000000 !important',
          },
          '& .MuiButton-containedPrimary': {
            backgroundColor: `${theme.palette.primary.main} !important`,
            color: `${theme.palette.primary.contrastText} !important`,
          },
          '& .MuiSwitch-track': {
            backgroundColor: darkMode ? '#555555 !important' : '#aaaaaa !important',
          },
          '& .MuiListItemButton-root:hover, & .MuiMenuItem-root:hover': {
            backgroundColor: darkMode
              ? 'rgba(255, 255, 255, 0.15) !important'
              : 'rgba(0, 0, 0, 0.15) !important',
          },
          '& a': {
            color: darkMode ? '#90caf9 !important' : '#0d47a1 !important',
            textDecoration: 'underline !important',
          },
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: darkMode ? '#777777 !important' : '#555555 !important',
            borderWidth: '2px !important',
          },
          '& .MuiInputBase-root:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: darkMode ? '#aaaaaa !important' : '#333333 !important',
          },
          '& .MuiFormLabel-root': {
            color: darkMode ? '#dddddd !important' : '#333333 !important',
          },
          '& .MuiTableCell-head': {
            backgroundColor: darkMode ? '#333333 !important' : '#e0e0e0 !important',
            color: darkMode ? '#ffffff !important' : '#000000 !important',
            fontWeight: 'bold !important',
          },
          '& .MuiTableRow-root': {
            borderBottom: darkMode
              ? '1px solid #444444 !important'
              : '1px solid #dddddd !important',
          },
          '& .MuiTableRow-root:nth-of-type(even)': {
            backgroundColor: darkMode ? '#252525 !important' : '#f5f5f5 !important',
          },
          '& .Mui-focused': {
            outline: '2px solid #1976d2 !important',
            outlineOffset: '2px !important',
          },
        }),
        // Dark mode is now handled by the theme system
        ...(reducedMotion && {
          '& *': {
            transitionProperty: 'none !important',
            transitionDuration: '0s !important',
            animationName: 'none !important',
            animationDuration: '0s !important',
            animationDelay: '0s !important',
            scrollBehavior: 'auto !important',
          },
        }),
      }}
    >
      <AppBar
        position="fixed"
        sx={{
          zIndex: (theme) => theme.zIndex.drawer + 1,
          boxShadow:
            theme.palette.mode === 'dark'
              ? '0 2px 10px 0 rgba(0,0,0,0.2)'
              : '0 2px 10px 0 rgba(0,0,0,0.05)',
          backgroundColor: theme.palette.mode === 'dark' ? '#1a1a1a' : 'background.paper',
          color: 'text.primary',
          width: '100%',
          borderBottom: theme.palette.mode === 'dark' ? '1px solid rgba(255,255,255,0.05)' : 'none',
          ...(isRTL ? { right: 0, left: 0 } : { left: 0, right: 0 }),
        }}
      >
        <Toolbar sx={{ justifyContent: 'space-between', direction }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge={isRTL ? 'end' : 'start'}
              onClick={handleDrawerToggle}
              sx={{
                mr: isRTL ? 0 : 2,
                ml: isRTL ? 2 : 0,
                display: { sm: 'none' },
              }}
            >
              <MenuIcon />
            </IconButton>

            {isMobile ? (
              <Image
                src={darkMode ? '/logo-dark.png' : '/logo.png'}
                alt="Carwah Logo"
                width={32}
                height={32}
                style={{
                  marginRight: isRTL ? 0 : 10,
                  marginLeft: isRTL ? 10 : 0,
                  objectFit: 'contain',
                  filter: darkMode ? 'drop-shadow(0 2px 4px rgba(255,255,255,0.1))' : 'none',
                }}
              />
            ) : (
              <Image
                src={'/fullLogo.svg'}
                alt="Carwah Logo"
                width={120}
                height={36}
                style={{
                  marginRight: isRTL ? 0 : 10,
                  marginLeft: isRTL ? 10 : 0,
                  objectFit: 'contain',
                  filter: darkMode ? 'drop-shadow(0 2px 4px rgba(255,255,255,0.1))' : 'none',
                }}
              />
            )}
            {/*
            {!isMobile && (
              <Typography variant="h6" noWrap component="div" sx={{ ml: 1 }}>
                {t('Dashboard')}
              </Typography>
            )} */}
          </Box>

          {/* Right side of header with language and accessibility features */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              flexDirection: isRTL ? 'row-reverse' : 'row',
            }}
          >
            {/* Dark mode toggle */}
            <Tooltip title={darkMode ? t('Light Mode') : t('Dark Mode')}>
              <IconButton color="inherit" onClick={toggleDarkMode} sx={{ mx: 0.5 }}>
                {darkMode ? <Brightness7 /> : <Brightness4 />}
              </IconButton>
            </Tooltip>

            {/* Language Menu */}
            <Tooltip title={t('Language')}>
              <IconButton
                color="inherit"
                aria-label="change language"
                onClick={handleLanguageMenuOpen}
                aria-controls={languageMenuOpen ? 'language-menu' : undefined}
                aria-expanded={languageMenuOpen ? 'true' : undefined}
                aria-haspopup="true"
                sx={{ mx: 0.5 }}
              >
                <Translate />
              </IconButton>
            </Tooltip>
            <Menu
              id="language-menu"
              anchorEl={languageAnchorEl}
              open={languageMenuOpen}
              onClose={handleLanguageMenuClose}
              MenuListProps={{
                'aria-labelledby': 'language-button',
                dense: largeText ? false : true,
              }}
              slotProps={{
                paper: {
                  elevation: 3,
                  sx: {
                    mt: 1.5,
                    borderRadius: 2,
                    minWidth: 150,
                    boxShadow: darkMode
                      ? '0 4px 20px rgba(0,0,0,0.3)'
                      : '0 4px 20px rgba(0,0,0,0.1)',
                    ...(darkMode && {
                      backgroundColor: highContrast ? '#121212' : '#1e1e1e',
                      color: '#f0f0f0',
                      border: '1px solid rgba(255,255,255,0.05)',
                    }),
                    ...(highContrast &&
                      !darkMode && {
                        backgroundColor: '#ffffff',
                        color: '#000000',
                        border: '1px solid #000000',
                      }),
                  },
                },
              }}
            >
              <MenuItem
                divider
                sx={{
                  justifyContent: 'center',
                  fontWeight: 'bold',
                  color: highContrast ? (darkMode ? '#ffffff' : '#000000') : 'primary.main',
                  ...(largeText && {
                    fontSize: '1.2em !important',
                  }),
                }}
              >
                {t('Language Selection')}
              </MenuItem>
              <MenuItem
                onClick={() => handleLanguageChange('en')}
                selected={i18n.language === 'en'}
                sx={{
                  padding: largeText ? '12px 16px' : '8px 16px',
                  ...(highContrast && {
                    backgroundColor:
                      i18n.language === 'en' ? (darkMode ? '#333' : '#e0e0e0') : 'transparent',
                    '&:hover': {
                      backgroundColor: darkMode
                        ? 'rgba(255, 255, 255, 0.15) !important'
                        : 'rgba(0, 0, 0, 0.15) !important',
                    },
                  }),
                }}
              >
                {t('English')}
              </MenuItem>
              <MenuItem
                onClick={() => handleLanguageChange('ar')}
                selected={i18n.language === 'ar'}
                sx={{
                  padding: largeText ? '12px 16px' : '8px 16px',
                  ...(highContrast && {
                    backgroundColor:
                      i18n.language === 'ar' ? (darkMode ? '#333' : '#e0e0e0') : 'transparent',
                    '&:hover': {
                      backgroundColor: darkMode
                        ? 'rgba(255, 255, 255, 0.15) !important'
                        : 'rgba(0, 0, 0, 0.15) !important',
                    },
                  }),
                }}
              >
                {t('Arabic')}
              </MenuItem>
            </Menu>

            {/* Accessibility Menu */}
            <Tooltip title={t('Accessibility')}>
              <IconButton
                color="inherit"
                aria-label="accessibility options"
                onClick={handleAccessibilityMenuOpen}
                aria-controls={accessibilityMenuOpen ? 'accessibility-menu' : undefined}
                aria-expanded={accessibilityMenuOpen ? 'true' : undefined}
                aria-haspopup="true"
                sx={{ mx: 0.5 }}
              >
                <AccessibilityNew />
              </IconButton>
            </Tooltip>
            <Menu
              id="accessibility-menu"
              anchorEl={accessibilityAnchorEl}
              open={accessibilityMenuOpen}
              onClose={handleAccessibilityMenuClose}
              MenuListProps={{
                'aria-labelledby': 'accessibility-button',
                dense: largeText ? false : true,
              }}
              slotProps={{
                paper: {
                  elevation: 3,
                  sx: {
                    mt: 1.5,
                    borderRadius: 2,
                    minWidth: 220,
                    boxShadow: darkMode
                      ? '0 4px 20px rgba(0,0,0,0.3)'
                      : '0 4px 20px rgba(0,0,0,0.1)',
                    ...(darkMode && {
                      backgroundColor: highContrast ? '#121212' : '#1e1e1e',
                      color: '#f0f0f0',
                      border: '1px solid rgba(255,255,255,0.05)',
                    }),
                    ...(highContrast &&
                      !darkMode && {
                        backgroundColor: '#ffffff',
                        color: '#000000',
                        border: '1px solid #000000',
                      }),
                  },
                },
              }}
            >
              <MenuItem
                divider
                sx={{
                  justifyContent: 'center',
                  fontWeight: 'bold',
                  color: highContrast ? (darkMode ? '#ffffff' : '#000000') : 'primary.main',
                  ...(largeText && {
                    fontSize: '1.2em !important',
                  }),
                }}
              >
                {t('Accessibility Options')}
              </MenuItem>
              <MenuItem
                sx={{
                  ...(largeText && {
                    padding: '8px 16px',
                  }),
                }}
              >
                <FormControlLabel
                  control={
                    <Switch
                      checked={darkMode}
                      onChange={toggleDarkMode}
                      name="darkMode"
                      color="primary"
                      inputProps={{ 'aria-label': t('Toggle dark mode') }}
                    />
                  }
                  label={t('Dark Mode')}
                  sx={{ width: '100%', ...(largeText && { marginRight: '8px' }) }}
                />
              </MenuItem>
              <MenuItem
                sx={{
                  ...(largeText && {
                    padding: '8px 16px',
                  }),
                }}
              >
                <FormControlLabel
                  control={
                    <Switch
                      checked={highContrast}
                      onChange={toggleHighContrast}
                      name="highContrast"
                      color="primary"
                      inputProps={{
                        'aria-label': t('Toggle high contrast mode for better visibility'),
                      }}
                    />
                  }
                  label={t('High Contrast')}
                  sx={{ width: '100%', ...(largeText && { marginRight: '8px' }) }}
                />
              </MenuItem>
              <MenuItem
                sx={{
                  ...(largeText && {
                    padding: '8px 16px',
                  }),
                }}
              >
                <FormControlLabel
                  control={
                    <Switch
                      checked={largeText}
                      onChange={toggleLargeText}
                      name="largeText"
                      color="primary"
                      inputProps={{
                        'aria-label': t('Toggle large text mode for better readability'),
                      }}
                    />
                  }
                  label={t('Large Text')}
                  sx={{ width: '100%', ...(largeText && { marginRight: '8px' }) }}
                />
              </MenuItem>
              <MenuItem
                sx={{
                  ...(largeText && {
                    padding: '8px 16px',
                  }),
                }}
              >
                <FormControlLabel
                  control={
                    <Switch
                      checked={reducedMotion}
                      onChange={toggleReducedMotion}
                      name="reducedMotion"
                      color="primary"
                      inputProps={{ 'aria-label': t('Toggle reduced motion for fewer animations') }}
                    />
                  }
                  label={t('Reduced Motion')}
                  sx={{ width: '100%', ...(largeText && { marginRight: '8px' }) }}
                />
              </MenuItem>
            </Menu>

            {/* Logout Button */}
            <Tooltip title={t('Logout')}>
              <IconButton
                color="inherit"
                aria-label="logout"
                onClick={() => {
                  // Clear localStorage
                  localStorage.clear();

                  // Clear cookies
                  document.cookie.split(';').forEach((cookie) => {
                    const [name] = cookie.trim().split('=');
                    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
                  });

                  // Redirect to login page
                  window.location.href = '/login';
                }}
                sx={{ mx: 0.5 }}
              >
                <Logout />
              </IconButton>
            </Tooltip>

            {/* User Avatar */}
            <Avatar
              sx={{
                ml: 1.5,
                width: 36,
                height: 36,
                bgcolor: 'primary.main',
                cursor: 'pointer',
              }}
              alt="User Avatar"
            >
              <span style={{ height: i18n.language === 'ar' ? '36px' : '22px' }}>
                {i18n.language === 'ar' ? 'ع' : 'A'}
              </span>
            </Avatar>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Mobile Drawer */}
      <Drawer
        variant="temporary"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile.
        }}
        sx={{
          display: { xs: 'block', sm: 'none' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: drawerWidth,
            borderRight: isRTL ? 'none' : '1px solid rgba(0, 0, 0, 0.08)',
            borderLeft: isRTL ? '1px solid rgba(0, 0, 0, 0.08)' : 'none',
            boxShadow: 3,
          },
        }}
        anchor={isRTL ? 'right' : 'left'}
      >
        {drawer}
      </Drawer>

      {/* Desktop Drawer */}
      <Drawer
        variant="permanent"
        sx={{
          display: { xs: 'none', sm: 'block' },
          width: drawerWidth,
          flexShrink: 0,
          [`& .MuiDrawer-paper`]: {
            width: drawerWidth,
            boxSizing: 'border-box',
            borderRight: isRTL
              ? 'none'
              : theme.palette.mode === 'dark'
              ? '1px solid rgba(255, 255, 255, 0.05)'
              : '1px solid rgba(0, 0, 0, 0.08)',
            borderLeft: isRTL
              ? theme.palette.mode === 'dark'
                ? '1px solid rgba(255, 255, 255, 0.05)'
                : '1px solid rgba(0, 0, 0, 0.08)'
              : 'none',
            boxShadow:
              theme.palette.mode === 'dark'
                ? '0 2px 10px 0 rgba(0,0,0,0.2)'
                : '0 2px 10px 0 rgba(0,0,0,0.05)',
            ...(isRTL ? { right: 0, left: 'auto' } : { left: 0, right: 'auto' }),
            backgroundColor: theme.palette.mode === 'dark' ? '#1a1a1a' : 'background.paper',
            color: 'text.primary',
            ...(largeText && {
              '& .MuiTypography-root': {
                fontSize: '1.1em',
              },
            }),
          },
        }}
        anchor={isRTL ? 'right' : 'left'}
        aria-label="main navigation"
      >
        {drawer}
      </Drawer>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: 'background.default',
          minHeight: '100vh',
          width: '100%',
          paddingRight: isRTL ? { xs: 1, sm: 2, md: 3 } : { xs: 1, sm: 2, md: 3 },
          paddingLeft: !isRTL ? { xs: 1, sm: 2, md: 3 } : { xs: 1, sm: 2, md: 3 },
          transition: reducedMotion
            ? 'none'
            : theme.transitions.create(['margin', 'width'], {
                easing: theme.transitions.easing.sharp,
                duration: theme.transitions.duration.leavingScreen,
              }),
          overflow: 'hidden',
          ...(largeText && {
            '& p, & div, & span, & button, & input, & select, & textarea': {
              fontSize: '1.1em !important',
              lineHeight: '1.5 !important',
            },
          }),
          // High contrast mode is now handled by the theme system
        }}
      >
        <Toolbar />
        <Container
          disableGutters
          maxWidth={false}
          sx={{
            direction,
            // padding: { xs: '0 !important', sm: '0 24px !important' },
            overflowX: 'auto',
            overflowY: 'hidden',
            display: 'flex',
            flexDirection: 'column',
            // Scrollbar styling is now handled globally
            [theme.breakpoints.down('sm')]: {
              overflowX: 'auto',
              WebkitOverflowScrolling: 'touch',
              msOverflowStyle: 'none',
              scrollbarWidth: 'none',
            },
            '& .MuiGrid-container, & .MuiBox-root': {
              width: '100%',
              maxWidth: '100%',
            },
            '& .MuiCard-root': {
              maxWidth: '100%',
              '& .MuiCardContent-root': {
                overflow: 'auto',
              },
              ...(highContrast && {
                backgroundColor: darkMode ? '#1a1a1a !important' : '#ffffff !important',
                color: darkMode ? '#ffffff !important' : '#000000 !important',
                border: darkMode ? '1px solid #444 !important' : '1px solid #ddd !important',
              }),
              ...(darkMode &&
                !highContrast && {
                  backgroundColor: '#252525',
                  color: '#ffffff',
                }),
            },
            '& .MuiFormControl-root': {
              maxWidth: '100%',
            },
            '& img, & video': {
              maxWidth: '100%',
              height: 'auto',
            },
            ...(highContrast && {
              '& .MuiButton-root': {
                backgroundColor: darkMode ? '#333333 !important' : '#e0e0e0 !important',
                color: darkMode ? '#ffffff !important' : '#000000 !important',
                border: darkMode ? '2px solid #777 !important' : '2px solid #555 !important',
                '&.MuiButton-contained': {
                  backgroundColor: darkMode ? '#444 !important' : '#ccc !important',
                },
                '&.MuiButton-containedPrimary': {
                  backgroundColor: `${theme.palette.primary.main} !important`,
                  color: `${theme.palette.primary.contrastText} !important`,
                  border: 'none !important',
                },
              },
            }),
            '& .MuiTableCell-head': {
              backgroundColor: darkMode ? '#333 !important' : '#e0e0e0 !important',
              color: darkMode ? '#fff !important' : '#000 !important',
              fontWeight: 'bold !important',
            },
            '& .MuiTableRow-root:nth-of-type(even)': {
              backgroundColor: darkMode ? '#222 !important' : '#f5f5f5 !important',
            },
            '& *:focus-visible': {
              outline: highContrast
                ? '3px solid #1976d2 !important'
                : '2px solid #1976d2 !important',
              outlineOffset: '2px !important',
            },
            ...(largeText && {
              '& .MuiInputBase-input': {
                fontSize: '1.15em !important',
                padding: '12px !important',
              },
              '& .MuiInputLabel-root': {
                fontSize: '1.15em !important',
              },
              '& button': {
                fontSize: '1.15em !important',
                padding: '10px 16px !important',
              },
            }),
          }}
        >
          <Box
            component="div"
            sx={{
              minWidth: { xs: 'fit-content', md: '100%' },
              width: '100%',
              position: 'relative',
              '& > *': {
                maxWidth: '100%',
              },
              '& table': {
                display: { xs: 'block', md: 'table' },
                width: '100%',
                overflowX: { xs: 'auto', md: 'visible' },
                WebkitOverflowScrolling: 'touch',
                '&::-webkit-scrollbar': {
                  height: '6px',
                  display: { xs: 'block', sm: 'block' },
                },
                '&::-webkit-scrollbar-thumb': {
                  backgroundColor: theme.palette.primary.main + '80',
                  borderRadius: '4px',
                },
                ...(highContrast && {
                  border: darkMode ? '1px solid #555 !important' : '1px solid #ccc !important',
                  '& th': {
                    backgroundColor: darkMode ? '#333 !important' : '#e0e0e0 !important',
                    color: darkMode ? '#fff !important' : '#000 !important',
                    border: darkMode ? '1px solid #555 !important' : '1px solid #ccc !important',
                  },
                  '& td': {
                    border: darkMode ? '1px solid #444 !important' : '1px solid #ddd !important',
                    color: darkMode ? '#fff !important' : '#000 !important',
                  },
                }),
                ...(darkMode &&
                  !highContrast && {
                    '& th': {
                      backgroundColor: '#333',
                      color: '#fff',
                      border: '1px solid #444',
                    },
                    '& td': {
                      border: '1px solid #333',
                      color: '#fff',
                    },
                  }),
              },
              '& .MuiGrid-container, & .MuiBox-root': {
                width: '100%',
                maxWidth: '100%',
              },
            }}
          >
            {children}
          </Box>
        </Container>
      </Box>
    </Box>
  );
};

export default PageLayout;
