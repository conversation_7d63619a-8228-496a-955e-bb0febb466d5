# Docker Setup for Carwah Dashboard

This document provides detailed instructions for using Dock<PERSON> with the Carwah Dashboard application.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Quick Start](#quick-start)
- [Environment Variables](#environment-variables)
- [Development Workflow](#development-workflow)
- [Production Deployment](#production-deployment)
- [Docker Compose Configuration](#docker-compose-configuration)
- [Troubleshooting](#troubleshooting)

## Prerequisites

Before you begin, ensure you have the following installed:

- [Docker](https://docs.docker.com/get-docker/)
- [Docker Compose](https://docs.docker.com/compose/install/)

## Quick Start

To quickly get the application running with Docker:

1. Clone the repository and navigate to the project directory:

```bash
git clone <repository-url>
cd Dashboard_v2
```

2. Build and start the Docker container:

```bash
docker-compose up -d
```

3. Access the application at http://localhost:3000

4. To stop the container:

```bash
docker-compose down
```

## Environment Variables

The application uses environment variables from the `.env.local` file for configuration.

### Setting Up Environment Variables

1. Copy the example environment file to create your local environment file:

```bash
cp .env.example .env.local
```

2. Edit the `.env.local` file with your specific values:

```bash
# Example .env.local content
NEXT_PUBLIC_API_URL=https://api.example.com/graphql
NEXT_PUBLIC_ENV=development
```

3. Docker Compose will automatically use the variables from the `.env.local` file when building and running the container.

### Available Environment Variables

| Variable | Description | Default Value |
|----------|-------------|---------------|
| NEXT_PUBLIC_API_URL | GraphQL API endpoint | http://localhost:3000/graphql |
| NEXT_PUBLIC_ENV | Current environment (development/staging/production) | development |

## Development Workflow

For development with Docker:

1. Make sure your `.env.local` file is set up with development values:

```bash
# Example .env.local for development
NEXT_PUBLIC_API_URL=http://localhost:3000/graphql
NEXT_PUBLIC_ENV=development
```

2. Start the container in development mode:

```bash
docker-compose up
```

This will start the container without detaching, so you can see the logs in real-time.

3. If you need to rebuild the container after making changes to the Dockerfile or docker-compose.yml:

```bash
docker-compose up -d --build
```

## Production Deployment

For production deployment:

1. Update your `.env.local` file with production values:

```bash
# Example .env.local for production
NEXT_PUBLIC_API_URL=https://api.production.com/graphql
NEXT_PUBLIC_ENV=production
```

2. Build and run the container:

```bash
docker-compose up -d --build
```

## Docker Compose Configuration

The project uses a single Docker Compose configuration file:

### docker-compose.yml

This file is configured to use environment variables from your `.env.local` file:

```yaml
version: '3.8'

services:
  dashboard:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - NEXT_PUBLIC_API_URL
        - NEXT_PUBLIC_ENV
    ports:
      - "3000:3000"
    env_file:
      - .env.local
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

This configuration ensures that all environment variables are read from the `.env.local` file.

## Troubleshooting

### Container Not Starting

If the container fails to start:

1. Check the logs:

```bash
docker-compose logs
```

2. Verify that the ports are not already in use:

```bash
netstat -tuln | grep 3000
```

### Environment Variables Not Working

If environment variables are not being applied:

1. Check that your `.env.local` file exists and is in the correct location (same directory as docker-compose.yml)
2. Verify the variable names match exactly what the application expects (NEXT_PUBLIC_API_URL, NEXT_PUBLIC_ENV)
3. Make sure the `.env.local` file has the correct permissions
4. Try rebuilding the container with `docker-compose up -d --build` to ensure the latest environment variables are used

### Performance Issues

If the application is slow in Docker:

1. Increase the resources allocated to Docker in Docker Desktop settings
2. Check if volume mounts are causing performance issues (common in development mode)
3. Consider using a production build for better performance

### Build Errors

If you encounter errors during the build process:

1. Make sure your Dockerfile is correctly configured
2. Check that all required files are included (not in .dockerignore)
3. Try building with verbose output:

```bash
docker-compose build --no-cache --progress=plain
```

## Additional Resources

- [Docker Documentation](https://docs.docker.com/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [Next.js Deployment Documentation](https://nextjs.org/docs/deployment)
