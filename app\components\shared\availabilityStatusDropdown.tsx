import React from 'react';
import { <PERSON>complete, TextField, Chip, CircularProgress } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@apollo/client';
import { GET_VEHICLES } from '../../gql/queries/vehicles';
import { availabillityStatus } from './constants';



interface AvailabilityStatusDropdownProps {
  filters: {
    status?: string | null;
    [key: string]: any;
  };
  handleAutocompleteChange: (field: string, value: string | null |boolean) => void;
}

const AvailabilityStatusDropdown: React.FC<AvailabilityStatusDropdownProps> = ({ 
  filters,
  handleAutocompleteChange,
}) => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  
  

  return (
    <Autocomplete 
      id="vehicleType" 
      options={[]} 
      getOptionLabel={(option) => (isRTL ? option.label : option.label)} 
      value={ 
        filters.vehicleType 
          ? availabillityStatus(t).find((item:any) => item.value.toString() === filters.status) || null 
          : null 
      } 
      onChange={(_, newValue) => 
        handleAutocompleteChange('status', newValue?.value?.toString() || null) 
      } 
      renderInput={(params) => ( 
        <TextField 
          {...params} 
          label={t('availabilityStatus')} 
          variant="outlined" 
          size="small" 
          InputLabelProps={{ 
            sx: { padding: '0 20px' }, 
          }}
          InputProps={{
            ...params.InputProps,
            endAdornment: (
              <>
                {params.InputProps.endAdornment}
              </>
            ),
          }}
       
        /> 
      )} 
      renderTags={(value, getTagProps) => 
        value.map((option, index) => ( 
          <Chip 
            label={isRTL ? option.label : option.label} 
            {...getTagProps({ index })} 
            size="small" 
          /> 
        )) 
      }
    />
  );
};

export default AvailabilityStatusDropdown;