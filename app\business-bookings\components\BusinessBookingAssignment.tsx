'use client';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation, useQuery } from '@apollo/client';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Box,
  Avatar,
  Typography,
  Alert,
  CircularProgress,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Assignment as AssignmentIcon,
  Edit as EditIcon,
} from '@mui/icons-material';

// Import GraphQL operations
import { GET_CUSTOMER_CARE_USERS_QUERY } from '../../gql/queries/bookings';
import { ASSIGN_BUSINESS_RENTAL_TO, ASSIGN_BUSINESS_RENTAL_TO_ME } from '../../gql/mutations/businessBookings';

interface CustomerCareUser {
  id: string;
  name: string;
  email: string;
  profileImage?: string;
  isActive: boolean;
  roles: Array<{
    id: string;
    enName: string;
    arName: string;
  }>;
}

interface BusinessBookingAssignmentModalProps {
  open: boolean;
  onClose: () => void;
  businessRentalId: string;
  currentAssignedTo?: string;
  onSuccess?: () => void;
}

export function BusinessBookingAssignmentModal({
  open,
  onClose,
  businessRentalId,
  currentAssignedTo,
  onSuccess,
}: BusinessBookingAssignmentModalProps) {
  const { t } = useTranslation();
  const [selectedUserId, setSelectedUserId] = useState<string>('');
  const [notes, setNotes] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [loading, setLoading] = useState(false);

  // Fetch customer care users
  const {
    data: usersData,
    loading: usersLoading,
    error: usersError,
  } = useQuery(GET_CUSTOMER_CARE_USERS_QUERY);

  // Assignment mutations
  const [assignBusinessRentalTo] = useMutation(ASSIGN_BUSINESS_RENTAL_TO);
  const [assignBusinessRentalToMe] = useMutation(ASSIGN_BUSINESS_RENTAL_TO_ME);

  const users = usersData?.users?.collection || [];
  const activeUsers = users.filter((user: CustomerCareUser) => user.isActive);

  const handleAssign = async () => {
    if (!selectedUserId) {
      setError(t('assignment.pleaseSelectUser'));
      return;
    }

    setLoading(true);
    setError('');

    try {
      const result = await assignBusinessRentalTo({
        variables: {
          businessRentalId,
          userId: selectedUserId,
        },
      });

      if (result.data?.assignBusinessRentalTo?.status) {
        onSuccess?.();
        onClose();
        setSelectedUserId('');
        setNotes('');
      } else {
        setError(result.data?.assignBusinessRentalTo?.errors?.[0] || t('assignment.errorAssigning'));
      }
    } catch (error) {
      console.error('Error assigning business booking:', error);
      setError(t('assignment.errorAssigning'));
    } finally {
      setLoading(false);
    }
  };

  const handleAssignToMe = async () => {
    setLoading(true);
    setError('');

    try {
      const result = await assignBusinessRentalToMe({
        variables: {
          businessRentalId,
        },
      });

      if (result.data?.assignBusinessRentalToMe?.status) {
        onSuccess?.();
        onClose();
        setSelectedUserId('');
        setNotes('');
      } else {
        setError(result.data?.assignBusinessRentalToMe?.errors?.[0] || t('assignment.errorAssigning'));
      }
    } catch (error) {
      console.error('Error assigning business booking to me:', error);
      setError(t('assignment.errorAssigning'));
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
      setSelectedUserId('');
      setNotes('');
      setError('');
    }
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <AssignmentIcon />
          {t('assignment.assignBooking')}
        </Box>
      </DialogTitle>
      <DialogContent>
        {currentAssignedTo && (
          <Alert severity="info" sx={{ mb: 2 }}>
            {t('assignment.currentlyAssignedTo')}: {currentAssignedTo}
          </Alert>
        )}

        {usersError && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {t('assignment.errorLoadingUsers')}
          </Alert>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {usersLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
            <CircularProgress />
          </Box>
        ) : (
          <Box sx={{ mt: 2 }}>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>{t('assignment.selectUser')}</InputLabel>
              <Select
                value={selectedUserId}
                onChange={(e) => {
                  setSelectedUserId(e.target.value);
                  setError('');
                }}
                error={!!error && !selectedUserId}
                disabled={loading}
              >
                {activeUsers.map((user: CustomerCareUser) => (
                  <MenuItem key={user.id} value={user.id}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Avatar src={user.profileImage} sx={{ width: 24, height: 24 }}>
                        {user.name.charAt(0)}
                      </Avatar>
                      <Box>
                        <Typography variant="body2">{user.name}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {user.email}
                        </Typography>
                      </Box>
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <TextField
              fullWidth
              multiline
              rows={3}
              label={t('assignment.assignmentNotes')}
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder={t('assignment.optionalNotes')}
              disabled={loading}
            />
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} disabled={loading}>
          {t('common.cancel')}
        </Button>
        <Button
          onClick={handleAssignToMe}
          variant="outlined"
          disabled={loading || usersLoading}
          sx={{ mr: 1 }}
        >
          {loading ? <CircularProgress size={20} /> : t('assignment.assignToMe')}
        </Button>
        <Button
          onClick={handleAssign}
          variant="contained"
          disabled={loading || usersLoading || !selectedUserId}
        >
          {loading ? <CircularProgress size={20} /> : t('assignment.assign')}
        </Button>
      </DialogActions>
    </Dialog>
  );
}

interface BusinessBookingAssignmentButtonProps {
  businessRentalId: string;
  currentAssignedTo?: string;
  onSuccess?: () => void;
}

export default function BusinessBookingAssignmentButton({
  businessRentalId,
  currentAssignedTo,
  onSuccess,
}: BusinessBookingAssignmentButtonProps) {
  const { t } = useTranslation();
  const [modalOpen, setModalOpen] = useState(false);

  return (
    <>
      <Tooltip title={currentAssignedTo ? t('assignment.reassign') : t('assignment.assignBooking')}>
        <IconButton
          size="small"
          onClick={() => setModalOpen(true)}
          sx={{
            backgroundColor: currentAssignedTo ? '#FF9800' : '#2196F3',
            color: 'white',
            '&:hover': {
              backgroundColor: currentAssignedTo ? '#F57C00' : '#1976D2',
            },
          }}
        >
          <AssignmentIcon fontSize="small" />
        </IconButton>
      </Tooltip>

      <BusinessBookingAssignmentModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        businessRentalId={businessRentalId}
        currentAssignedTo={currentAssignedTo}
        onSuccess={onSuccess}
      />
    </>
  );
}
