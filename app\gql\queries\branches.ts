import { gql } from "@apollo/client";
export const GET_Branches = gql`
  query Branches($limit: Int, $page: Int,$allyCompanyIds:[ID!]) {
    branches(limit: $limit, page: $page,allyCompanyIds:$allyCompanyIds) {
      collection {
        allyCompanyId
        arName
        cheapestCarMonthlyPrice
        cheapestCarPrice
        description
        distanceBetweenBranchUser
        districtName
        enName
        id
        isActive
        name
        updatedBy 
      }
      metadata {
        currentPage
        limitValue
        totalCount
        totalPages
      }
    }
  }`;

