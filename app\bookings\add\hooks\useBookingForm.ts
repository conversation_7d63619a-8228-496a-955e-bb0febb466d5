import { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { useLazyQuery, useMutation, useQuery } from '@apollo/client';
import dayjs, { Dayjs } from 'dayjs';
import React from 'react';

// GraphQL imports
import { BOOKING_DETAILS_QUERY } from '../../../gql/queries/bookings';
import { CREATE_BOOKING_MUTATION, EDIT_BOOKING_MUTATION } from '../../../gql/mutations/bookings';
import { GET_CITIES_FOR_BOOKING } from '../../../gql/queries/areas';
import { GET_AVAILABLE_COMPANIES, GET_AVAILABLE_BRANCHES } from '../../../gql/queries/companies';
import {
  GET_ALL_AVAILABLE_CARS,
  GET_RENT_PRICE,
  GET_COUPON_AVAILABILITY,
} from '../../../gql/queries/cars';

// Types
interface BookingFormData {
  customerId: string;
  customerName: string;
  carId: string;
  pickUpDate: Dayjs;
  dropOffDate: Dayjs;
  pickUpCityId: string;
  dropOffCityId: string;
  branchId: string;
  dropOffBranchId?: string;
  paymentMethod: string;
  deliverType: string;
  deliverAddress: string;
  deliverLat: number | null;
  deliverLng: number | null;
  deliveryPrice: number;
  handoverAddress: string;
  handoverLat: number | null;
  handoverLng: number | null;
  handoverPrice: number;
  suggestedPrice?: string;
  isUnlimited: boolean;
  withInstallment: boolean;
  isPickSameReturn: boolean;
  isTwoWays: boolean;
  insuranceId?: string;
  couponId?: string;
}

interface UseBookingFormProps {
  mode: 'add' | 'edit';
  bookingId?: string;
}

export const useBookingForm = ({ mode, bookingId }: UseBookingFormProps) => {
  const { t } = useTranslation();
  const { user } = useSelector((state: any) => state.auth);
  const ally_id = user?.ally_id;

  // Refs
  const noteRef = useRef('');
  const couponRef = useRef('');
  const suggestedPriceRef = useRef('');

  // Loading states
  const [loading, setLoading] = useState(false);
  const [gettingAreas, setGettingAreas] = useState(false);
  const [calculatingPrice, setCalculatingPrice] = useState(false);

  // Main state
  const [changed, setChanged] = useState(false);
  const [ready, setReady] = useState(false);
  const [editDatedReady, setEditDatedReady] = useState(false);
  const [clicked, setClicked] = useState(false);

  // Customer state
  const [customerDetails, setCustomerDetails] = useState<any>(null);
  const [customerId, setCustomerId] = useState<string | null>(null);

  // Location state
  const [pickUpCity, setPickUpCity] = useState<any>(null);
  const [dropOffCity, setDropOffCity] = useState<any>(null);
  const [isPickSameReturn, setIsPickSameReturn] = useState(true);

  // Date state
  const [pickUpDate, setPickUpDate] = useState(dayjs().add(2, 'hour'));
  const [dropOffDate, setDropOffDate] = useState(dayjs().add(3, 'day').add(2, 'hour'));
  const [bookingType, setBookingType] = useState('daily');
  const [months, setMonths] = useState('1');
  const [monthTime, setMonthTime] = useState<string>('');

  // Company/Branch/Car state
  const [allCompanies, setAllCompanies] = useState<any[]>([]);
  const [companiesData, setCompaniesData] = useState<any[]>([]);
  const [selectedCompany, setSelectedCompany] = useState<any>(null);
  const [selectedBranch, setSelectedBranch] = useState<any>(null);
  const [selectedDropoffBranch, setSelectedDropoffBranch] = useState<any>(null);
  const [branchesData, setBranchesData] = useState<any[]>([]);
  const [availableCarsCollection, setAvailableCarsCollection] = useState<any[]>([]);
  const [availableCarsDD, setAvailableCarsDD] = useState<Array<{ value: string; label: string }>>(
    []
  );
  const [carsData, setCarsData] = useState<any[]>([]);
  const [selectedCar, setSelectedCar] = useState<any>(null);

  // Insurance state
  const [insurancesData, setInsurancesData] = useState<any[]>([]);
  const [selectedInsurance, setSelectedInsurance] = useState<any>(null);

  // Extra services state
  const [extraServices, setExtraServices] = useState<any>({});
  const [selectedExtraServices, setSelectedExtraServices] = useState<any[]>([]);
  const [allyExtraServices, setAllyExtraServices] = useState<any[]>([]);
  const [branchExtraServices, setBranchExtraServices] = useState<any[]>([]);
  const [allyExtraServicesIds, setAllyExtraServicesIds] = useState<string[]>([]);
  const [branchExtraServicesIds, setBranchExtraServicesIds] = useState<string[]>([]);

  // Plan state (for rent-to-own)
  const [carPlans, setCarPlans] = useState<any[]>([]);
  const [selectedPlan, setSelectedPlan] = useState<any>(null);
  const [plan, setPlan] = useState<any>(null);

  // Coupon state
  const [couponId, setCouponId] = useState<string>('');
  const [couponCode, setCouponCode] = useState('');
  const [appliedCoupon, setAppliedCoupon] = useState<any>(null);
  const [couponAvailability, setCouponAvailability] = useState<string | null>(null);

  // Fursan state
  const [fursanMemberId, setFursanMemberId] = useState<string>('');
  const [fursanChecked, setFursanChecked] = useState(false);
  const [fursanVerified, setFursanVerified] = useState(false);

  // Price state
  const [basePrice, setBasePrice] = useState<number>(0);
  const [discountAmount, setDiscountAmount] = useState<number>(0);

  // Loading states for different operations
  const [gettingCompanies, setGettingCompanies] = useState(false);
  const [gettingBranches, setGettingBranches] = useState(false);
  const [gettingCars, setGettingCars] = useState(false);
  const [gettingInsurances, setGettingInsurances] = useState(false);

  // Delivery address state
  const [deliveryAddress, setDeliveryAddress] = useState('');

  // Delivery state
  const [isDelivery, setIsDelivery] = useState(false);
  const [hasDelivery, setHasDelivery] = useState(false);
  const [deliveryType, setDeliveryType] = useState<'one-way' | 'two-way'>('one-way');
  const [deliveryLocation, setDeliveryLocation] = useState<{
    lat: number;
    lng: number;
    address?: string;
    placeId?: string;
  } | null>(null);
  const [deliverLat, setDeliverLat] = useState<number | null>(null);
  const [deliverLng, setDeliverLng] = useState<number | null>(null);
  const [deliverAddress, setDeliverAddress] = useState('');
  const [deliveryPrice, setDeliveryPrice] = useState<number>(0);
  const [distanceCarUser, setDistanceCarUser] = useState<number>(0);

  // Handover state
  const [isTwoWays, setIsTwoWays] = useState(false);
  const [handoverPrice, setHandoverPrice] = useState<number>(0);
  const [handoverLat, setHandoverLat] = useState<number | null>(null);
  const [handoverLng, setHandoverLng] = useState<number | null>(null);

  // Payment state
  const [paymentMethod, setPaymentMethod] = useState('CASH');
  const [insuranceId, setInsuranceId] = useState<string>('');
  const [insuranceName, setInsuranceName] = useState('');

  // Other missing options
  const [unlimited, setUnlimited] = useState(false);
  const [withInstallment, setWithInstallment] = useState(false);

  // Suggested price state (matching old implementation)
  const [suggestedPrice, setSuggestedPrice] = useState<number | null>(null);
  const [useSuggestedPrice, setUseSuggestedPrice] = useState(false);

  // Notes state (for edit mode)
  const [notes, setNotes] = useState('');

  // Areas data
  const [areasData, setAreasData] = useState<any[]>([]);

  // Price data
  const [bookingPriceData, setBookingPriceData] = useState<any>(null);

  // Calculate total price
  const [totalPrice, setTotalPrice] = useState<number>(0);

  // Form Data State
  const [formData, setFormData] = useState<BookingFormData>({
    customerId: '',
    customerName: '',
    carId: '',
    pickUpDate: dayjs().add(2, 'hour'),
    dropOffDate: dayjs().add(3, 'day').add(2, 'hour'),
    pickUpCityId: '',
    dropOffCityId: '',
    branchId: '',
    dropOffBranchId: '',
    paymentMethod: 'CASH',
    deliverType: 'NO_DELIVERY',
    deliverAddress: '',
    deliverLat: null,
    deliverLng: null,
    deliveryPrice: 0,
    handoverAddress: '',
    handoverLat: null,
    handoverLng: null,
    handoverPrice: 0,
    suggestedPrice: '',
    isUnlimited: false,
    withInstallment: false,
    isPickSameReturn: true,
    isTwoWays: false,
    insuranceId: '',
    couponId: '',
  });

  // GraphQL queries
  const [getAreas, { loading: areasLoading }] = useLazyQuery(GET_CITIES_FOR_BOOKING, {
    onCompleted: (data) => {
      if (data?.areas) {
        setAreasData(data.areas);
      }
      setGettingAreas(false);
    },
    onError: (error) => {
      console.error('Error fetching areas:', error);
      setGettingAreas(false);
    },
  });

  const [getCompanies, { loading: companiesLoading }] = useLazyQuery(GET_AVAILABLE_COMPANIES, {
    onCompleted: (data) => {
      if (data?.availableAllyCompanies?.collection) {
        const companies = data.availableAllyCompanies.collection.map((company: any) => ({
          id: company.id,
          value: company.id,
          label: company.enName,
          arName: company.arName,
          enName: company.enName,
        }));
        setAllCompanies(companies);
        setCompaniesData(companies);
      }
    },
    onError: (error) => {
      console.error('Error fetching companies:', error);
    },
  });

  const [getBranches, { loading: branchesLoading, data: branches }] = useLazyQuery(
    GET_AVAILABLE_BRANCHES,
    {
      onCompleted: (data) => {
        if (data?.availableBranches?.collection) {
          const branchesData = data.availableBranches.collection.map((branch: any) => ({
            id: branch.id,
            value: branch.id,
            label: branch.enName,
            arName: branch.arName,
            enName: branch.enName,
            name: branch.name,
            canDelivery: branch.canDelivery,
            canHandover: branch.canHandover,
            branchDeliveryPrices: branch.branchDeliveryPrices,
            area: branch.area,
            cityId: branch.area?.id,
            allyCompany: branch.allyCompany,
            branchExtraServices: branch.branchExtraServices,
          }));
          setBranchesData(branchesData);
        }
        setGettingBranches(false);
      },
      onError: (error) => {
        console.error('Error fetching branches:', error);
        setGettingBranches(false);
      },
    }
  );

  const [getCars, { loading: carsLoading, data: CarsRes }] = useLazyQuery(GET_ALL_AVAILABLE_CARS, {
    onCompleted: (data) => {
      if (data?.cars?.collection) {
        const cars = data.cars.collection;
        setAvailableCarsCollection(cars);
        setCarsData(cars);

        const carsDD = cars.map((car: any) => ({
          value: car.id,
          label: `${car.make.enName} ${car.carModel.enName} ${car.year} - ${car.dailyPrice} SAR/day`,
          isUnlimited: car.isUnlimited,
          unlimitedFeePerDay: car.unlimitedFeePerDay,
        }));
        setAvailableCarsDD(carsDD);

        // In edit mode, set the selected car if it matches
        if (bookingId && !changed) {
          const selectedCarFromBooking = cars.find(
            (car: any) => car.id === bookingData?.rentalDetails?.carId
          );
          if (selectedCarFromBooking) {
            setSelectedCar(selectedCarFromBooking);
          }
        }
      }
    },
    onError: (error) => {
      console.error('Error fetching cars:', error);
    },
  });

  const [getRentPrice, { loading: priceLoading }] = useLazyQuery(GET_RENT_PRICE, {
    onCompleted: (data) => {
      if (data?.aboutRentPrice) {
        setBookingPriceData(data);
      }
    },
    onError: (error) => {
      console.error('Error calculating price:', error);
    },
  });

  const [checkCoupon] = useLazyQuery(GET_COUPON_AVAILABILITY, {
    onCompleted: (data) => {
      if (data?.carCouponAvailability) {
        const { status } = data.carCouponAvailability;
        if (status === 'VALID') {
          setCouponAvailability(null);
          setCouponId(data.carCouponAvailability.coupon?.id || '');
        } else {
          setCouponAvailability(status || t('Invalid coupon'));
          setCouponId('');
        }
      }
    },
    onError: (error) => {
      console.error('Error checking coupon:', error);
      setCouponAvailability(t('error.checking.coupon'));
    },
  });

  // GraphQL mutations
  const [createBooking, { loading: creatingBooking }] = useMutation(CREATE_BOOKING_MUTATION);
  const [editBooking, { loading: editingBooking }] = useMutation(EDIT_BOOKING_MUTATION);

  // Fetch booking details for edit mode
  const {
    data: bookingData,
    loading: bookingLoading,
    refetch: refetchBooking,
  } = useQuery(BOOKING_DETAILS_QUERY, {
    variables: { id: bookingId },
    skip: mode === 'add' || !bookingId,
    fetchPolicy: 'network-only',
    errorPolicy: 'all',
    onCompleted: (data) => {
      if (data?.rentalDetails) {
        const booking = data.rentalDetails;

        // Set customer details
        if (booking.userId && booking.customerName && booking.customerMobile) {
          const customerData = {
            id: booking.userId,
            name: booking.customerName,
            mobile: booking.customerMobile,
            phone: booking.customerMobile,
            email: booking.customerEmail || '',
            nationalId: booking.customerNationalId || '',
          };

          setCustomerDetails(customerData);
          setCustomerId(booking.userId);
        }

        // Set base price and discount from booking data
        if (booking.priceBeforeDiscount) {
          setBasePrice(booking.priceBeforeDiscount);
        }
        if (booking.couponDiscount) {
          setDiscountAmount(booking.couponDiscount);
        }

        // Update formData object with all the booking data
        setFormData({
          customerId: booking.userId || '',
          customerName: booking.customerName || '',
          carId: booking.carId || '',
          pickUpDate: dayjs(`${booking.pickUpDate} ${booking.pickUpTime}`),
          dropOffDate: dayjs(`${booking.dropOffDate} ${booking.dropOffTime}`),
          pickUpCityId: booking.pickUpCityId || '',
          dropOffCityId: booking.dropOffCityId || '',
          branchId: booking.branchId?.toString() || '',
          dropOffBranchId: booking.dropOffBranchId?.toString() || '',
          paymentMethod: booking.paymentMethod || 'CASH',
          deliverType: booking.deliverType || 'NO_DELIVERY',
          deliverAddress: booking.deliverAddress || '',
          deliverLat: booking.deliverLat || null,
          deliverLng: booking.deliverLng || null,
          deliveryPrice: booking.deliveryPrice || 0,
          handoverAddress: '', // This field is not in the booking response
          handoverLat: booking.handoverLat || null,
          handoverLng: booking.handoverLng || null,
          handoverPrice: booking.handoverPrice || 0,
          suggestedPrice: booking.suggestedPrice?.toString() || '',
          isUnlimited: booking.isUnlimited || false,
          withInstallment: booking.withInstallment || false,
          isPickSameReturn: booking.pickUpCityId === booking.dropOffCityId,
          isTwoWays: !!(booking.handoverLat && booking.handoverLng),
          insuranceId: booking.insuranceId || '',
          couponId: booking.couponId || '',
        });

        // Mark as ready
        setReady(true);
      }
    },
  });

  // Effect to calculate total price when relevant data changes
  useEffect(() => {
    if (bookingPriceData) {
      let calculatedTotal = 0;

      // Use suggested price if enabled and available
      if (useSuggestedPrice && suggestedPrice) {
        const days = dropOffDate.diff(pickUpDate, 'day') || 1;
        calculatedTotal = suggestedPrice * days;
      } else {
        // Use calculated price from backend
        if (bookingPriceData.aboutRentPrice) {
          calculatedTotal = bookingPriceData.aboutRentPrice.totalAmountDue || 0;
        }
      }

      setTotalPrice(calculatedTotal);
    } else {
      setTotalPrice(0);
    }
  }, [bookingPriceData, useSuggestedPrice, suggestedPrice, pickUpDate, dropOffDate]);

  // Load initial data - optimized to prevent duplicate calls
  useEffect(() => {
    const loadInitialData = async () => {
      if (mode === 'edit' && bookingId) {
        // In edit mode, first refetch booking data then load areas
        if (!bookingData?.rentalDetails) {
          await refetchBooking();
        }
        if (!areasData?.length) {
          await getAreas();
        }
      } else if (mode === 'add') {
        // In add mode, just load areas
        if (!areasData?.length) {
          await getAreas();
        }
      }
    };

    if (!gettingAreas) {
      setGettingAreas(true);
      loadInitialData().finally(() => setGettingAreas(false));
    }
  }, [mode, bookingId]); // Simplified dependencies

  // Handle companies data when it's loaded
  useEffect(() => {
    if (companiesData?.length && !allCompanies?.length) {
      const formattedList =
        companiesData
          .filter((item) => item.isB2c || +item.id === +bookingData?.rentalDetails?.allyCompanyId)
          .map((item) => ({
            value: item?.id,
            label: item?.enName,
          })) || [];
      setAllCompanies(formattedList);
    }
  }, [companiesData, bookingData?.rentalDetails, allCompanies?.length]);

  // Handle areas data and set cities in edit mode
  useEffect(() => {
    if (!changed && areasData?.length && bookingData?.rentalDetails && mode === 'edit') {
      const booking = bookingData.rentalDetails;

      setIsPickSameReturn(+booking.branchId === +booking.dropOffBranchId);

      const pickupCity = areasData.find((area) => +area?.id === +booking?.pickUpCityId);
      const dropoffCity = areasData.find((area) => +area?.id === +booking?.dropOffCityId);

      if (pickupCity && !pickUpCity) {
        setPickUpCity(pickupCity);
      }
      if (dropoffCity && !dropOffCity) {
        setDropOffCity(dropoffCity);
      }
    }
  }, [areasData, bookingData?.rentalDetails, changed, mode, pickUpCity, dropOffCity]);

  // Handle cars data
  useEffect(() => {
    if (CarsRes?.cars?.collection?.length) {
      const availableCars = [...CarsRes.cars.collection];
      setAvailableCarsCollection(availableCars);
      setCarsData(availableCars);

      const selectedCarFromBooking = availableCars?.find(
        (car) => +car.id === +bookingData?.rentalDetails?.carId
      );

      setAvailableCarsDD(
        availableCars?.map((car) => ({
          label: `${car.make?.enName} - ${car.carModel?.enName} - ${car.year} | Daily: ${car.dailyPrice}`,
          value: car.id,
          isUnlimited: car.isUnlimited,
          unlimitedFeePerDay: car.unlimitedFeePerDay,
        }))
      );

      if (!changed && selectedCarFromBooking && !selectedCar) {
        setSelectedCar(selectedCarFromBooking);
      }
    }
  }, [CarsRes, bookingData?.rentalDetails, changed, selectedCar]);

  // Populate insurance data from selected car
  useEffect(() => {
    if (selectedCar?.carInsurances) {
      const insurances = selectedCar.carInsurances.map((carInsurance: any) => ({
        id: carInsurance.insuranceId,
        insuranceId: carInsurance.insuranceId,
        insuranceName: carInsurance.insuranceName,
        price: carInsurance.value || carInsurance.monthlyValue || 0,
      }));
      setInsurancesData(insurances);
    } else {
      setInsurancesData([]);
    }
  }, [selectedCar]);

  // Extra services effect - Fixed to only set available services, not selected ones
  useEffect(() => {
    if (selectedCar && selectedBranch && extraServices) {
      const allyServices = extraServices.allyExtraServicesForAlly || [];
      const branchServices = extraServices.branchExtraServices || [];

      setAllyExtraServices(allyServices);
      setBranchExtraServices(branchServices);

      // Don't automatically populate IDs - these should come from selectedExtraServices
      // setAllyExtraServicesIds(allyServices.map((service: any) => service.id));
      // setBranchExtraServicesIds(branchServices.map((service: any) => service.id));
    }
  }, [selectedCar, selectedBranch, extraServices]);

  // New effect to update service IDs based on selected services
  useEffect(() => {
    if (selectedExtraServices?.length) {
      const allyIds: string[] = [];
      const branchIds: string[] = [];

      selectedExtraServices.forEach((service: any) => {
        // Check if service is an ally service
        if (
          allyExtraServices?.some(
            (ally: any) => ally.id === service.id || ally.extraService?.id === service.id
          )
        ) {
          allyIds.push(service.id);
        }
        // Check if service is a branch service
        else if (
          branchExtraServices?.some(
            (branch: any) => branch.id === service.id || branch.extraService?.id === service.id
          )
        ) {
          branchIds.push(service.id);
        } else {
        }
      });

      setAllyExtraServicesIds([...new Set(allyIds)]);
      setBranchExtraServicesIds([...new Set(branchIds)]);
    } else {
      // No services selected, clear the arrays
      setAllyExtraServicesIds([]);
      setBranchExtraServicesIds([]);
    }
  }, [selectedExtraServices, allyExtraServices, branchExtraServices]);

  // Handle booking type changes for dates
  useEffect(() => {
    if (bookingType === 'daily' && bookingId && bookingData?.rentalDetails) {
      setDropOffDate(
        dayjs(
          `${bookingData.rentalDetails.dropOffDate}T${bookingData.rentalDetails.dropOffTime}Z`
        ).subtract(Math.abs(new Date().getTimezoneOffset() / 60), 'hours')
      );
    } else if (bookingType === 'monthly' && bookingId && bookingData?.rentalDetails) {
      if (bookingData.rentalDetails.dropOffDate) {
        const timeParts = bookingData.rentalDetails.dropOffTime?.split(':') || ['00', '00'];
        setDropOffDate(
          dayjs(bookingData.rentalDetails.dropOffDate)
            .hour(parseInt(timeParts[0]))
            .minute(parseInt(timeParts[1]))
        );
      }
    }
  }, [bookingType, bookingId, bookingData?.rentalDetails]);

  // Handle month time changes
  useEffect(() => {
    if (monthTime) {
      const timeParts = monthTime.split(':');
      setDropOffDate(dropOffDate.hour(parseInt(timeParts[0])).minute(parseInt(timeParts[1])));
    }
  }, [monthTime]);

  // Main edit mode data population effect - optimized
  useEffect(() => {
    if (bookingData?.rentalDetails && mode === 'edit' && !editDatedReady && !changed) {
      const booking = bookingData.rentalDetails;

      // Set delivery price and other basic fields
      setDeliveryPrice(booking.deliveryPrice || 0);

      // Set delivery state
      if (booking.deliverAddress || booking.deliverLat || booking.deliverLng) {
        setHasDelivery(true);
        setIsDelivery(true);
        setDeliveryAddress(booking.deliverAddress || '');
        setDeliveryLocation({
          lat: booking.deliverLat || 0,
          lng: booking.deliverLng || 0,
          address: booking.deliverAddress || '',
        });
      }

      // Set Fursan status
      if (booking.loyaltyType) {
        setFursanChecked(true);
        setFursanVerified(true);
        // If there's a member ID in the booking, set it
        if (booking.loyaltyMembership) {
          setFursanMemberId(booking.loyaltyMembership);
        }
      }

      // Set two-way delivery
      if (booking.deliverType === 'two_ways') {
        setIsTwoWays(true);
        setDeliveryType('two-way');
      }

      // Set insurance
      if (booking.insuranceId) {
        setInsuranceId(booking.insuranceId);
        setInsuranceName(booking.insuranceName || '');
      }

      // Set plan for rent-to-own
      if (booking.ownCarDetails?.rentalOwnCarPlan) {
        setPlan(booking.ownCarDetails.rentalOwnCarPlan);
        setSelectedPlan(booking.ownCarDetails.rentalOwnCarPlan);
      }

      // Set coupon details
      if (booking.couponId || booking.couponCode) {
        setCouponId(booking.couponId || '');
        setCouponCode(booking.couponCode || '');
        couponRef.current = booking.couponCode || '';

        if (booking.couponDiscount) {
          setAppliedCoupon({
            id: booking.couponId,
            code: booking.couponCode,
            discount: booking.couponDiscount,
          });
          setCouponAvailability('valid');
        }
      }

      // Set unlimited
      setUnlimited(booking.isUnlimited || false);

      // Set installment status
      setWithInstallment(booking.withInstallment || false);

      // Set notes if available
      if (booking.notes) {
        setNotes(booking.notes);
        noteRef.current = booking.notes;
      }

      // Set dates
      setPickUpDate(
        dayjs(`${booking.pickUpDate}T${booking.pickUpTime}Z`).subtract(
          Math.abs(new Date().getTimezoneOffset() / 60),
          'hours'
        )
      );
      setDropOffDate(
        dayjs(`${booking.dropOffDate}T${booking.dropOffTime}Z`).subtract(
          Math.abs(new Date().getTimezoneOffset() / 60),
          'hours'
        )
      );

      // Set booking type based on duration and rent-to-own status
      if (booking.numberOfDays % 30 === 0 && !booking.isRentToOwn) {
        setMonths((booking.numberOfDays / 30).toString());
        setBookingType('monthly');
      } else if (booking.isRentToOwn) {
        setBookingType('rent-to-own');
      }

      // Set suggested price
      if (booking.suggestedPrice) {
        suggestedPriceRef.current = booking.suggestedPrice.toString();
        setSuggestedPrice(booking.suggestedPrice);
        setUseSuggestedPrice(true);
      }

      // Set dropoff branch
      if (booking.dropOffBranchId) {
        setSelectedDropoffBranch({ id: booking.dropOffBranchId });
      }

      // Set payment method
      setPaymentMethod(booking.paymentMethod || 'CASH');

      // Set delivery location if exists
      if (booking.deliverLat || booking.deliverLng) {
        setDeliverLat(booking.deliverLat);
        setDeliverLng(booking.deliverLng);
        setDeliverAddress(booking.deliverAddress || '');
        setIsDelivery(true);
      }

      // Set handover location if exists
      if (booking.handoverLat && booking.handoverLng) {
        setHandoverLat(booking.handoverLat);
        setHandoverLng(booking.handoverLng);
        setHandoverPrice(booking.handoverPrice || 0);
      }

      // Set total price from booking data for edit mode
      if (booking.totalBookingPrice) {
        setTotalPrice(booking.totalBookingPrice);
      }

      setEditDatedReady(true);
    }
  }, [bookingData?.rentalDetails, mode, editDatedReady, changed]);

  // Fetch companies when pickup city changes - optimized to prevent duplicates
  useEffect(() => {
    if (pickUpCity && customerDetails && !gettingCompanies) {
      const variables: any = {
        limit: 1000,
        isActive: true,
        pickStartDate: pickUpDate.format('DD/MM/YYYY'),
      };

      if (isPickSameReturn) {
        variables.cityId = parseInt(pickUpCity.id);
      } else {
        variables.pickupCityId = parseInt(pickUpCity.id);
        if (dropOffCity) {
          variables.dropoffCityId = parseInt(dropOffCity.id);
        }
      }

      if (bookingType === 'rent-to-own') {
        variables.isRentToOwn = true;
      }

      setGettingCompanies(true);
      getCompanies({ variables }).finally(() => {
        setGettingCompanies(false);
      });
    }
  }, [
    pickUpCity?.id,
    dropOffCity?.id,
    isPickSameReturn,
    bookingType,
    pickUpDate,
    customerDetails?.id,
  ]);

  // Set selected company and branch in edit mode - optimized
  useEffect(() => {
    if (!changed && mode === 'edit' && bookingData?.rentalDetails) {
      // Set company
      if (allCompanies?.length && !selectedCompany) {
        const company = allCompanies.find(
          (i) => +i.value === +bookingData.rentalDetails.allyCompanyId
        );
        if (company) {
          setSelectedCompany(company);
        }
      }

      // Set branch
      if (
        branchesData?.length &&
        !selectedBranch &&
        selectedCompany?.value === bookingData.rentalDetails?.allyCompanyId
      ) {
        const branch = branchesData.find((i) => +i.value === +bookingData.rentalDetails.branchId);
        if (branch) {
          setSelectedBranch(branch);
        }
      }
    }
  }, [
    allCompanies,
    branchesData,
    changed,
    mode,
    bookingData?.rentalDetails,
    selectedCompany?.value,
    selectedBranch,
  ]);

  // Fetch branches when company is selected - optimized
  useEffect(() => {
    if (selectedCompany && pickUpCity && !gettingBranches) {
      const variables: any = {
        limit: 1000,
        isActive: true,
        allyCompanyIds: [selectedCompany.value || selectedCompany.id],
        areaIds: isPickSameReturn
          ? [pickUpCity.id]
          : [pickUpCity.id, dropOffCity?.id].filter(Boolean),
        pickStartDate: pickUpDate.format('DD/MM/YYYY'),
      };

      if (bookingType === 'rent-to-own') {
        variables.isRentToOwn = true;
      }

      setGettingBranches(true);
      getBranches({ variables }).finally(() => {
        setGettingBranches(false);
      });
    }
  }, [
    selectedCompany?.value,
    pickUpCity?.id,
    dropOffCity?.id,
    isPickSameReturn,
    bookingType,
    pickUpDate,
  ]);

  // Populate insurance selection in edit mode
  useEffect(() => {
    if (
      !changed &&
      mode === 'edit' &&
      insurancesData?.length &&
      bookingData?.rentalDetails &&
      !selectedInsurance
    ) {
      const booking = bookingData.rentalDetails;

      // Try to find insurance by insuranceId - use string comparison since IDs might be strings or numbers
      let insurance = insurancesData.find(
        (ins) =>
          ins.id === booking.insuranceId?.toString() ||
          ins.insuranceId === booking.insuranceId?.toString() ||
          ins.id === booking.insuranceId ||
          ins.insuranceId === booking.insuranceId
      );

      // If not found by ID, try to find by name
      if (!insurance && booking.insuranceName) {
        insurance = insurancesData.find((ins) => ins.insuranceName === booking.insuranceName);
      }

      if (insurance) {
        setSelectedInsurance(insurance);
      }
    }
  }, [insurancesData, selectedInsurance, changed, mode, bookingData?.rentalDetails]);

  // Populate extra services in edit mode
  useEffect(() => {
    if (!changed && mode === 'edit' && bookingData?.rentalDetails) {
      // Case 1: Booking has NO extra services - clear everything
      if (!bookingData.rentalDetails.rentalExtraServices?.length) {
        setSelectedExtraServices([]);
        setAllyExtraServicesIds([]);
        setBranchExtraServicesIds([]);
        return;
      }

      // Case 2: Booking has extra services - process them
      if (
        bookingData.rentalDetails.rentalExtraServices?.length &&
        (branchExtraServices?.length || allyExtraServices?.length) &&
        selectedExtraServices?.length === 0 // Only set if not already set
      ) {
        const bookingExtraServices = bookingData.rentalDetails.rentalExtraServices;
        const allAvailableServices = [...(branchExtraServices || []), ...(allyExtraServices || [])];

        if (allAvailableServices.length > 0) {
          const selectedServices = bookingExtraServices
            .map((bookingService: any) => {
              // Try to find matching service by ID
              let matchingService = allAvailableServices.find(
                (service: any) => service.id === bookingService.extraServiceId
              );

              // If not found by ID, try to find by extraService.id
              if (!matchingService) {
                matchingService = allAvailableServices.find(
                  (service: any) => service.extraService?.id === bookingService.extraServiceId
                );
              }

              if (matchingService) {
                return {
                  ...matchingService,
                  id: matchingService.id,
                  price: bookingService.serviceValue || matchingService.price || 0,
                  title:
                    matchingService.extraService?.enTitle ||
                    matchingService.enTitle ||
                    matchingService.title,
                  arTitle: matchingService.extraService?.arTitle || matchingService.arTitle,
                  enTitle: matchingService.extraService?.enTitle || matchingService.enTitle,
                };
              }

              // If service not found in available services, create a representation
              return {
                id: bookingService.extraServiceId,
                title: bookingService.title || bookingService.enTitle || 'Unknown Service',
                arTitle: bookingService.arTitle || bookingService.title,
                enTitle: bookingService.enTitle || bookingService.title,
                price: bookingService.serviceValue || 0,
              };
            })
            .filter(Boolean); // Remove any null/undefined values

          setSelectedExtraServices(selectedServices);
        }
      }
    }
  }, [
    branchExtraServices,
    allyExtraServices,
    bookingData?.rentalDetails?.rentalExtraServices,
    changed,
    mode,
    selectedExtraServices?.length, // Add this to track current state
  ]);

  // Fetch cars when branch is selected - optimized
  useEffect(() => {
    if (selectedBranch?.value && pickUpCity && !gettingCars) {
      const requestCarsVariables = {
        pickStartDate: pickUpDate.format('DD/MM/YYYY'),
        pickEndDate:
          dropOffDate.diff(dayjs(), 'days') < 0 ? null : dropOffDate.format('DD/MM/YYYY'),
        pickUpLocationId: +pickUpCity?.id,
        dropOffLocationId: isPickSameReturn ? +pickUpCity?.id : +dropOffCity?.id,
        isActive: true,
        branchId: selectedBranch.value,
        canDelivery: bookingType === 'delivery' ? true : undefined,
        isRentToOwn: bookingType === 'rent-to-own' ? true : undefined,
        carId: mode === 'edit' ? selectedCar?.id : undefined,
      };

      setGettingCars(true);
      getCars({ variables: requestCarsVariables }).finally(() => {
        setGettingCars(false);
      });

      // Set extra services from branch
      const filteredBranch = branches?.availableBranches?.collection.find(
        (b: any) => +b.id === +selectedBranch.value
      );
      if (filteredBranch) {
        setExtraServices({
          allyExtraServicesForAlly: filteredBranch.allyCompany?.allyExtraServices || [],
          branchExtraServices: filteredBranch.branchExtraServices || [],
        });
      }
    }
  }, [
    selectedBranch?.value,
    pickUpCity?.id,
    dropOffCity?.id,
    isPickSameReturn,
    bookingType,
    pickUpDate,
    dropOffDate,
    mode,
  ]);

  // Separate effect for fetching branches in edit mode when rental details are available
  useEffect(() => {
    if (
      mode === 'edit' &&
      bookingData?.rentalDetails?.allyCompanyId &&
      pickUpCity &&
      !branchesData?.length
    ) {
      getBranches({
        variables: {
          allyCompanyIds: [bookingData.rentalDetails.allyCompanyId],
          areaIds: [bookingData.rentalDetails.pickUpCityId],
          canDelivery: bookingType === 'delivery' ? true : undefined,
          isActive: true,
          isRentToOwn: bookingType === 'rent-to-own' ? true : undefined,
          pickStartDate: pickUpDate.format('DD/MM/YYYY'),
        },
      });
    }
  }, [mode, bookingData?.rentalDetails, pickUpCity, bookingType, pickUpDate, branchesData?.length]);

  // Effect to calculate price when relevant data changes in edit mode
  useEffect(() => {
    if (
      mode === 'edit' &&
      selectedCar &&
      selectedBranch &&
      pickUpDate &&
      dropOffDate &&
      customerDetails &&
      insurancesData?.length // Ensure insurance data is loaded
    ) {
      const variables = {
        carId: selectedCar?.id,
        isCarChanged: mode === 'edit' ? !!changed : false,
        isUnlimited: unlimited,
        paymentMethod,
        deliveryPrice: hasDelivery ? deliveryPrice : undefined,
        handoverPrice:
          deliveryType === 'two-way'
            ? handoverPrice || bookingData?.rentalDetails?.handoverPrice
            : undefined,
        ownCarPlanId: bookingType === 'rent-to-own' ? selectedPlan?.id : undefined,
        handoverBranch:
          deliveryType === 'two-way'
            ? selectedDropoffBranch?.id || selectedDropoffBranch?.value
            : undefined,
        deliverLat: deliveryLocation?.lat || selectedBranch.latitude || deliverLat,
        deliverLng: deliveryLocation?.lng || selectedBranch.longitude || deliverLng,
        couponId: appliedCoupon?.id || null,
        deliveryType: hasDelivery
          ? deliveryType === 'two-way'
            ? 'two_ways'
            : 'one_way'
          : 'no_delivery',
        dropOffDate:
          bookingType === 'rent-to-own' ? undefined : dropOffDate.locale('en').format('DD/MM/YYYY'),
        dropOffTime:
          bookingType === 'rent-to-own'
            ? undefined
            : `${dropOffDate.locale('en').format('HH:mm')}:00`,
        insuranceId:
          mode === 'edit' && !changed
            ? bookingData?.rentalDetails?.insuranceId
            : selectedInsurance?.id || undefined,
        pickUpDate: pickUpDate.locale('en').format('DD/MM/YYYY'),
        pickUpTime: `${pickUpDate.locale('en').format('HH:mm')}:00`,
        allyExtraServices: allyExtraServicesIds?.length ? [...new Set(allyExtraServicesIds)] : null,
        branchExtraServices: branchExtraServicesIds?.length
          ? [...new Set(branchExtraServicesIds)]
          : null,
        usedPrice:
          mode === 'edit' && !changed ? bookingData?.rentalDetails?.pricePerDay : undefined,
        walletPaidAmount: bookingData?.rentalDetails?.walletTransactions?.amount || undefined,
        suggestedPrice: bookingData?.rentalDetails?.suggestedPrice || undefined,
        isEdit: mode === 'edit',
        rentalId: mode === 'edit' && bookingId ? parseInt(bookingId) : undefined,
        payWithInstallments:
          bookingType !== 'rent-to-own' &&
          (withInstallment || Boolean(bookingData?.rentalDetails?.installments?.length)),
        unlimitedKm: unlimited,
        customerRanking: customerDetails?.ranking || null,
        memberId: fursanMemberId || null,
        driverId: null,
        promotionCode: null,
      };

      getRentPrice({ variables });
    }
  }, [
    mode,
    selectedCar?.id,
    selectedBranch?.id,
    pickUpDate,
    dropOffDate,
    customerDetails?.id,
    selectedInsurance?.id,
    selectedExtraServices,
    hasDelivery,
    deliveryType,
    unlimited,
    appliedCoupon?.id,
    insurancesData?.length,
    deliveryLocation,
    bookingId,
    bookingData?.rentalDetails?.pricePerDay,
    bookingData?.rentalDetails?.insuranceId,
    changed,
    bookingType,
    selectedPlan?.id,
    withInstallment,
    allyExtraServicesIds,
    branchExtraServicesIds,
    paymentMethod,
    selectedDropoffBranch?.id,
    handoverPrice,
    bookingData?.rentalDetails?.handoverPrice,
    bookingData?.rentalDetails?.suggestedPrice,
    bookingData?.rentalDetails?.walletTransactions?.amount,
    bookingData?.rentalDetails?.installments?.length,
  ]);

  // Helper function to validate coupon
  const validateCoupon = () => {
    if (selectedCar && couponCode && customerId) {
      checkCoupon({
        variables: {
          carId: selectedCar.id,
          couponCode: couponCode,
          userId: customerId,
        },
      });
    }
  };

  // Form validation
  const isFormValid = (): boolean => {
    return !!(
      customerDetails &&
      selectedCar &&
      selectedBranch &&
      paymentMethod &&
      ((selectedInsurance && bookingType !== 'rent-to-own') || bookingType === 'rent-to-own')
    );
  };

  const calculateTotalPrice = () => {
    if (!formData.carId || !selectedBranch?.id || !formData.pickUpDate || !formData.dropOffDate) {
      return;
    }

    const variables = {
      carId: formData.carId,
      isCarChanged: mode === 'edit' ? !!changed : false,
      isUnlimited: unlimited,
      paymentMethod,
      deliveryPrice: hasDelivery ? deliveryPrice : undefined,
      handoverPrice:
        deliveryType === 'two-way'
          ? handoverPrice || bookingData?.rentalDetails?.handoverPrice
          : undefined,
      ownCarPlanId: bookingType === 'rent-to-own' ? selectedPlan?.id : undefined,
      handoverBranch:
        deliveryType === 'two-way'
          ? selectedDropoffBranch?.id || selectedDropoffBranch?.value
          : undefined,
      deliverLat: deliveryLocation?.lat || selectedBranch.latitude || deliverLat,
      deliverLng: deliveryLocation?.lng || selectedBranch.longitude || deliverLng,
      couponId: appliedCoupon?.id || null,
      deliveryType: hasDelivery
        ? deliveryType === 'two-way'
          ? 'two_ways'
          : 'one_way'
        : 'no_delivery',
      dropOffDate:
        bookingType === 'rent-to-own'
          ? undefined
          : formData.dropOffDate.locale('en').format('DD/MM/YYYY'),
      dropOffTime:
        bookingType === 'rent-to-own'
          ? undefined
          : `${formData.dropOffDate.locale('en').format('HH:mm')}:00`,
      insuranceId:
        mode === 'edit' && !changed
          ? bookingData?.rentalDetails?.insuranceId
          : selectedInsurance?.id || undefined,
      pickUpDate: formData.pickUpDate.locale('en').format('DD/MM/YYYY'),
      pickUpTime: `${formData.pickUpDate.locale('en').format('HH:mm')}:00`,
      allyExtraServices: allyExtraServicesIds?.length ? [...new Set(allyExtraServicesIds)] : null,
      branchExtraServices: branchExtraServicesIds?.length
        ? [...new Set(branchExtraServicesIds)]
        : null,
      usedPrice: mode === 'edit' && !changed ? bookingData?.rentalDetails?.pricePerDay : undefined,
      walletPaidAmount: bookingData?.rentalDetails?.walletTransactions?.amount || undefined,
      suggestedPrice: bookingData?.rentalDetails?.suggestedPrice || undefined,
      isEdit: mode === 'edit',
      rentalId: mode === 'edit' && bookingId ? parseInt(bookingId) : undefined,
      payWithInstallments:
        bookingType !== 'rent-to-own' &&
        (withInstallment || Boolean(bookingData?.rentalDetails?.installments?.length)),
      unlimitedKm: unlimited,
      customerRanking: customerDetails?.ranking || null,
      memberId: fursanMemberId || null,
      driverId: null,
      promotionCode: null,
    };
    getRentPrice({ variables });
  };

  return {
    // Data
    areasData,
    allCompanies,
    companiesData,
    branchesData,
    carsData,
    insurancesData,
    availableCarsCollection,
    availableCarsDD,
    bookingPriceData,
    totalPrice,
    basePrice,
    discountAmount,
    formData,

    // State
    customerDetails,
    customerId,
    pickUpCity,
    dropOffCity,
    isPickSameReturn,
    pickUpDate,
    dropOffDate,
    bookingType,
    months,
    monthTime,
    selectedCompany,
    selectedBranch,
    selectedDropoffBranch,
    selectedCar,
    selectedInsurance,
    selectedExtraServices,
    selectedPlan,
    appliedCoupon,
    isDelivery,
    hasDelivery,
    deliveryType,
    deliveryLocation,
    deliverLat,
    deliverLng,
    deliverAddress,
    deliveryAddress,
    deliveryPrice,
    distanceCarUser,
    isTwoWays,
    handoverPrice,
    handoverLat,
    handoverLng,
    paymentMethod,
    insuranceId,
    insuranceName,
    extraServices,
    allyExtraServices,
    branchExtraServices,
    allyExtraServicesIds,
    branchExtraServicesIds,
    carPlans,
    plan,
    couponId,
    couponCode,
    couponAvailability,
    fursanMemberId,
    fursanChecked,
    fursanVerified,
    unlimited,
    withInstallment,
    loading,
    gettingAreas,
    gettingCompanies,
    gettingBranches,
    gettingCars,
    gettingInsurances,
    calculatingPrice,
    changed,
    ready,
    editDatedReady,
    clicked,

    // Setters
    setCustomerDetails,
    setCustomerId,
    setPickUpCity,
    setDropOffCity,
    setIsPickSameReturn,
    setPickUpDate,
    setDropOffDate,
    setBookingType,
    setMonths,
    setMonthTime,
    setSelectedCompany,
    setSelectedBranch,
    setSelectedDropoffBranch,
    setSelectedCar,
    setSelectedInsurance,
    setSelectedExtraServices,
    setSelectedPlan,
    setAppliedCoupon,
    setIsDelivery,
    setHasDelivery,
    setDeliveryType,
    setDeliveryLocation,
    setDeliveryAddress,
    setDeliveryPrice,
    setDistanceCarUser,
    setIsTwoWays,
    setHandoverPrice,
    setPaymentMethod,
    setInsuranceId,
    setInsuranceName,
    setExtraServices,
    setAllyExtraServicesIds,
    setBranchExtraServicesIds,
    setCarPlans,
    setPlan,
    setCouponId,
    setCouponCode,
    setCouponAvailability,
    setFursanMemberId,
    setFursanChecked,
    setFursanVerified,
    setUnlimited,
    setWithInstallment,
    setFormData,
    setLoading,
    setClicked,
    setChanged,
    setCarsData,
    setInsurancesData,
    setAllyExtraServices,
    setBranchExtraServices,
    setCompaniesData,
    setAreasData,
    setTotalPrice,
    setBasePrice,
    setDiscountAmount,

    // Suggested price state (matching old implementation)
    suggestedPrice,
    setSuggestedPrice,
    useSuggestedPrice,
    setUseSuggestedPrice,

    // Notes state
    notes,
    setNotes,

    // Refs
    noteRef,
    couponRef,
    suggestedPriceRef,

    // Functions
    validateCoupon,
    isFormValid,

    // GraphQL functions
    getAreas,
    getCompanies,
    getBranches,
    getCars,
    getRentPrice,
    createBooking,
    editBooking,
    refetchBooking,

    // Loading states
    areasLoading,
    companiesLoading,
    branchesLoading,
    carsLoading,
    priceLoading,
    creatingBooking,
    editingBooking,
    bookingLoading,

    // Other
    ally_id,

    // New functions
    calculateTotalPrice,
  };
};
