'use client';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import Link from 'next/link';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  IconButton,
  Divider,
  Grid,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Pagination,
  FormControl,
  InputLabel,
  Select,
  SelectChangeEvent,
  useTheme,
} from '@mui/material';
import {
  Edit as EditIcon,
  Print as PrintIcon,
  History as HistoryIcon,
  MoreVert as MoreVertIcon,
  Cached as CachedIcon,
  NewReleases as NewReleasesIcon,
  Visibility as VisibilityIcon,
  CalendarToday as CalendarIcon,
  LocationOn as LocationIcon,
  AttachMoney as MoneyIcon,
  DirectionsCar as CarIcon,
  Business as BusinessIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';

interface CarsMobileViewProps {
  cars: any[];
  page: number;
  limit: number;
  total: number;
  onPageChange: (page: number) => void;
  onLimitChange: (limit: number) => void;
}

export default function CarsMobileView({
  cars,
  page,
  limit,
  total,
  onPageChange,
  onLimitChange,
}: CarsMobileViewProps) {
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const isRTL = i18n.language === 'ar';

  // State for action menu
  const [actionMenuAnchorEl, setActionMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedBookingId, setSelectedBookingId] = useState<number | null>(null);

  // Handle action menu open
  const handleActionMenuOpen = (event: React.MouseEvent<HTMLElement>, bookingId: number) => {
    setActionMenuAnchorEl(event.currentTarget);
    setSelectedBookingId(bookingId);
  };

  // Handle action menu close
  const handleActionMenuClose = () => {
    setActionMenuAnchorEl(null);
    setSelectedBookingId(null);
  };

  // Handle page change
  const handlePageChange = (_event: React.ChangeEvent<unknown>, value: number) => {
    onPageChange(value);
  };

  // Handle rows per page change
  const handleLimitChange = (event: SelectChangeEvent) => {
    const newLimit = parseInt(event.target.value, 10);
    onLimitChange(newLimit);
    onPageChange(1); // Reset to first page when changing rows per page
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'confirmed':
        return 'primary';
      case 'car_received':
        return 'success';
      case 'invoiced':
        return 'info';
      case 'closed':
        return 'secondary';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      <Box sx={{ mb: 2 }}>
        {cars.map((booking) => (
          <Card
            key={booking.id}
            sx={{
              mb: 2,
              borderRadius: 2,
              boxShadow:
                theme.palette.mode === 'dark'
                  ? '0 2px 8px rgba(0,0,0,0.2)'
                  : '0 2px 8px rgba(0,0,0,0.08)',
              position: 'relative',
              overflow: 'visible',
              border: theme.palette.mode === 'dark' ? '1px solid rgba(255,255,255,0.05)' : 'none',
              '&:hover': {
                borderColor:
                  theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.1)' : 'transparent',
              },
            }}
          >
            {/* Status Badge */}
            <Box
              sx={{
                position: 'absolute',
                top: -10,
                right: isRTL ? 'auto' : 20,
                left: isRTL ? 20 : 'auto',
                zIndex: 1,
              }}
            >
              <Chip
                label={t(
                  booking.status.replace('_', ' ').charAt(0).toUpperCase() +
                    booking.status.replace('_', ' ').slice(1)
                )}
                color={getStatusColor(booking.status) as any}
                size="small"
                sx={{
                  fontWeight: 500,
                  minWidth: 90,
                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                }}
              />
            </Box>

            <CardContent sx={{ pt: 3 }}>
              {/* Header with ID and Actions */}
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  mb: 1,
                }}
              >
                <Box>
                  <Typography variant="subtitle1" component="div" fontWeight={500}>
                    <Link
                      href={`/cars/${booking.id}`}
                      style={{ color: theme.palette.primary.main, textDecoration: 'none' }}
                    >
                      {t('Booking')} #{booking.bookingNo}
                    </Link>
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    ID: {booking.id}
                  </Typography>
                </Box>

                <IconButton
                  aria-label={t('More actions')}
                  size="small"
                  onClick={(event) => handleActionMenuOpen(event, booking.id)}
                >
                  <MoreVertIcon />
                </IconButton>
              </Box>

              <Divider sx={{ my: 1.5 }} />

              {/* Customer and Ally Info */}
              <Grid container spacing={2} sx={{ mb: 1.5 }}>
                <Grid item xs={6}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                    <PersonIcon fontSize="small" color="primary" sx={{ mt: 0.5 }} />
                    <Box>
                      <Typography variant="body2" fontWeight={500}>
                        {t('Customer')}
                      </Typography>
                      <Typography variant="body2">
                        <Link
                          href={`/customers/${booking.id}`}
                          style={{ color: theme.palette.primary.main, textDecoration: 'none' }}
                        >
                          {booking.customerName}
                        </Link>
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {booking.customerMobile}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={6}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                    <BusinessIcon fontSize="small" color="primary" sx={{ mt: 0.5 }} />
                    <Box>
                      <Typography variant="body2" fontWeight={500}>
                        {t('Ally')}
                      </Typography>
                      <Typography variant="body2">
                        <Link
                          href={`/allies/${booking.allyCompanyId}`}
                          style={{ color: theme.palette.primary.main, textDecoration: 'none' }}
                        >
                          {isRTL ? booking.arAllyName : booking.enAllyName}
                        </Link>
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {booking.branchName}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
              </Grid>

              {/* Car Info */}
              <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1, mb: 1.5 }}>
                <CarIcon fontSize="small" color="primary" sx={{ mt: 0.5 }} />
                <Box>
                  <Typography variant="body2" fontWeight={500}>
                    {t('Car')}
                  </Typography>
                  <Typography variant="body2">
                    <Link
                      href={`/cars/${booking.carId}`}
                      style={{ color: theme.palette.primary.main, textDecoration: 'none' }}
                    >
                      {`${isRTL ? booking.arMakeName : booking.enMakeName} ${
                        isRTL ? booking.arModelName : booking.enModelName
                      } ${isRTL ? booking.arVersionName : booking.enVersionName} ${booking.year}`}
                    </Link>
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {t('Plate')}: {booking.plateNo}
                  </Typography>
                </Box>
              </Box>

              {/* Dates */}
              <Grid container spacing={2} sx={{ mb: 1.5 }}>
                <Grid item xs={6}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                    <CalendarIcon fontSize="small" color="primary" sx={{ mt: 0.5 }} />
                    <Box>
                      <Typography variant="body2" fontWeight={500}>
                        {t('Pick-up')}
                      </Typography>
                      <Typography variant="body2">
                        {format(new Date(booking.pickUpDate), 'dd MMM yyyy', {
                          locale: i18n.language === 'ar' ? ar : enUS,
                        })}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {format(
                          new Date(
                            2000,
                            0,
                            1,
                            parseInt(booking.pickUpTime.split(':')[0]),
                            parseInt(booking.pickUpTime.split(':')[1])
                          ),
                          'hh:mm a',
                          { locale: i18n.language === 'ar' ? ar : enUS }
                        )}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={6}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                    <CalendarIcon fontSize="small" color="primary" sx={{ mt: 0.5 }} />
                    <Box>
                      <Typography variant="body2" fontWeight={500}>
                        {t('Drop-off')}
                      </Typography>
                      <Typography variant="body2">
                        {format(
                          new Date(
                            booking.lastConfirmedExtensionRequest
                              ? booking.lastConfirmedExtensionRequest.dropOffDate
                              : booking.dropOffDate
                          ),
                          'dd MMM yyyy',
                          { locale: i18n.language === 'ar' ? ar : enUS }
                        )}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {format(
                          new Date(
                            2000,
                            0,
                            1,
                            parseInt(booking.dropOffTime.split(':')[0]),
                            parseInt(booking.dropOffTime.split(':')[1])
                          ),
                          'hh:mm a',
                          { locale: i18n.language === 'ar' ? ar : enUS }
                        )}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
              </Grid>

              {/* Locations */}
              <Grid container spacing={2} sx={{ mb: 1.5 }}>
                <Grid item xs={6}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                    <LocationIcon fontSize="small" color="primary" sx={{ mt: 0.5 }} />
                    <Box>
                      <Typography variant="body2" fontWeight={500}>
                        {t('Pick-up Location')}
                      </Typography>
                      <Typography variant="body2">
                        {isRTL ? booking.arPickUpCityName : booking.enPickUpCityName}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={6}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                    <LocationIcon fontSize="small" color="primary" sx={{ mt: 0.5 }} />
                    <Box>
                      <Typography variant="body2" fontWeight={500}>
                        {t('Drop-off Location')}
                      </Typography>
                      <Typography variant="body2">
                        {isRTL ? booking.arDropOffCityName : booking.enDropOffCityName}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
              </Grid>

              {/* Price Info */}
              <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1, mb: 1 }}>
                <MoneyIcon fontSize="small" color="primary" sx={{ mt: 0.5 }} />
                <Box>
                  <Typography variant="body2" fontWeight={500}>
                    {t('Payment Details')}
                  </Typography>
                  <Grid container spacing={1}>
                    <Grid item xs={6}>
                      <Typography variant="caption" color="text.secondary">
                        {t('Days')}:{' '}
                        {booking.lastConfirmedExtensionRequest
                          ? booking.lastConfirmedExtensionRequest.numberOfDays
                          : booking.numberOfDays}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="caption" color="text.secondary">
                        {t('Price/Day')}: {booking.pricePerDay} {t('SAR')}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="caption" color="text.secondary">
                        {t('Total')}:{' '}
                        {booking.lastConfirmedExtensionRequest
                          ? booking.lastConfirmedExtensionRequest.totalBookingPrice
                          : booking.totalBookingPrice}{' '}
                        {t('SAR')}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="caption" color="text.secondary">
                        {t('Paid')}: {booking.paidAmount} {t('SAR')}
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>
              </Box>

              {/* Footer with Created Date and Extension Badge */}
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  mt: 2,
                }}
              >
                <Typography variant="caption" color="text.secondary">
                  {t('Created')}:{' '}
                  {format(new Date(booking.createdAt), 'dd MMM yyyy, hh:mm a', {
                    locale: i18n.language === 'ar' ? ar : enUS,
                  })}
                </Typography>

                {booking.lastRentalDateExtensionRequest && (
                  <Chip
                    label={t('Extension')}
                    size="small"
                    variant="outlined"
                    color="primary"
                    sx={{ fontSize: '0.7rem', height: 20 }}
                  />
                )}
              </Box>
            </CardContent>
          </Card>
        ))}
      </Box>

      {/* Pagination Controls */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          justifyContent: 'space-between',
          alignItems: 'center',
          gap: 2,
          mt: 3,
        }}
      >
        <Pagination
          count={Math.ceil(total / limit)}
          page={page}
          onChange={handlePageChange}
          color="primary"
          size="medium"
          showFirstButton
          showLastButton
          sx={{
            '& .MuiPaginationItem-root': {
              borderRadius: 1,
              fontWeight: 500,
            },
          }}
        />

        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel id="rows-per-page-label">{t('Per Page')}</InputLabel>
          <Select
            labelId="rows-per-page-label"
            id="rows-per-page"
            value={limit.toString()}
            defaultValue="50"
            label={t('Per Page')}
            onChange={handleLimitChange}
            sx={{ fontWeight: 500 }}
          >
            <MenuItem value={10}>10</MenuItem>
            <MenuItem value={25}>25</MenuItem>
            <MenuItem value={50}>50</MenuItem>
            <MenuItem value={100}>100</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Actions Menu */}
      <Menu
        anchorEl={actionMenuAnchorEl}
        open={Boolean(actionMenuAnchorEl)}
        onClose={handleActionMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem
          component={Link}
          href={`/cars/${selectedBookingId}`}
          onClick={handleActionMenuClose}
        >
          <ListItemIcon>
            <VisibilityIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('View Details')}</ListItemText>
        </MenuItem>

        <MenuItem
          component={Link}
          href={`/cars/${selectedBookingId}/edit`}
          onClick={handleActionMenuClose}
        >
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('Edit')}</ListItemText>
        </MenuItem>

        <MenuItem onClick={handleActionMenuClose}>
          <ListItemIcon>
            <PrintIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('Print')}</ListItemText>
        </MenuItem>

        <MenuItem onClick={handleActionMenuClose}>
          <ListItemIcon>
            <CachedIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('Extend')}</ListItemText>
        </MenuItem>

        <MenuItem onClick={handleActionMenuClose}>
          <ListItemIcon>
            <NewReleasesIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('Change Status')}</ListItemText>
        </MenuItem>

        <MenuItem onClick={handleActionMenuClose}>
          <ListItemIcon>
            <HistoryIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('Timeline')}</ListItemText>
        </MenuItem>
      </Menu>
    </Box>
  );
}
