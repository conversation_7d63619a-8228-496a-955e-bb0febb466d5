'use client';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation } from '@apollo/client';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  Alert,
  Grid,
  Card,
  CardContent,
  Chip,
  InputAdornment,
  IconButton,
  CircularProgress,
  Snackbar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Close as CloseIcon,
  CalendarToday as CalendarIcon,
  AttachMoney as MoneyIcon,
} from '@mui/icons-material';
import { DatePicker, TimePicker } from '@mui/x-date-pickers';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { format, addDays, differenceInDays } from 'date-fns';
import dayjs from 'dayjs';
import { ar, enUS } from 'date-fns/locale';
import { CREATE_RENTAL_DATE_EXTENSION_REQUEST_MUTATION } from '../../gql/mutations/bookings';

interface ExtendModalProps {
  open: boolean;
  onClose: () => void;
  bookingId: string;
  onSuccess: () => void;
  currentEndDate?: string;
  currentEndTime?: string;
  pricePerDay?: number;
  maxExtensionDays?: number;
  currency?: string;
}

const PAYMENT_METHODS = [
  { value: 'CASH', label: 'Cash' },
  { value: 'CREDIT_CARD', label: 'Credit Card' },
  { value: 'BANK_TRANSFER', label: 'Bank Transfer' },
  { value: 'WALLET', label: 'Wallet' },
];

export default function ExtendModal({
  open,
  onClose,
  bookingId,
  onSuccess,
  currentEndDate,
  currentEndTime = '12:00',
  pricePerDay = 0,
  maxExtensionDays = 30,
  currency = 'SAR',
}: ExtendModalProps) {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  const dateLocale = isRTL ? ar : enUS;

  const [newEndDate, setNewEndDate] = useState<dayjs.Dayjs | null>(null);
  const [newEndTime, setNewEndTime] = useState<dayjs.Dayjs | null>(
    dayjs().hour(12).minute(0).second(0)
  );
  const [paymentMethod, setPaymentMethod] = useState<string>('CASH');
  const [isPaid, setIsPaid] = useState<boolean>(false);
  const [withInstallment, setWithInstallment] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [showSuccess, setShowSuccess] = useState<boolean>(false);

  const [createExtensionRequest] = useMutation(CREATE_RENTAL_DATE_EXTENSION_REQUEST_MUTATION);

  const currentEnd = currentEndDate ? dayjs(currentEndDate) : dayjs();
  const minDate = currentEnd.add(1, 'day');
  const maxDate = currentEnd.add(maxExtensionDays, 'day');

  const additionalDays = newEndDate ? newEndDate.diff(currentEnd, 'day') : 0;
  const additionalCost = additionalDays * pricePerDay;
  const totalPrice = additionalCost;
  const usedPrice = isPaid ? totalPrice : 0;

  const handleSubmit = async () => {
    if (!newEndDate) {
      setError(t('Please select a new end date'));
      return;
    }

    if (!newEndTime) {
      setError(t('Please select a new end time'));
      return;
    }

    if (additionalDays <= 0) {
      setError(t('New end date must be after current end date'));
      return;
    }

    if (additionalDays > maxExtensionDays) {
      setError(t('Extension cannot exceed {{days}} days', { days: maxExtensionDays }));
      return;
    }

    setLoading(true);
    setError('');

    try {
      const result = await createExtensionRequest({
        variables: {
          rentalId: bookingId,
          dropOffDate: newEndDate.format('YYYY-MM-DD'),
          dropOffTime: newEndTime.format('HH:mm'),
          paymentMethod: paymentMethod as any,
          totalPrice,
          isPaid,
          usedPrice,
          withInstallment,
        },
      });

      if (
        result.data?.createRentalDateExtensionRequest?.status === 'success' ||
        !result.data?.createRentalDateExtensionRequest?.errors?.length
      ) {
        setShowSuccess(true);
        setTimeout(() => {
          onSuccess();
          handleClose();
        }, 1500);
      } else {
        setError(
          result.data?.createRentalDateExtensionRequest?.errors?.[0] ||
            t('Failed to create extension request')
        );
      }
    } catch (err: any) {
      console.error('Error creating extension request:', err);
      setError(err.message || t('An error occurred while creating the extension request'));
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setNewEndDate(null);
    setNewEndTime(dayjs().hour(12).minute(0).second(0));
    setPaymentMethod('CASH');
    setIsPaid(false);
    setWithInstallment(false);
    setError('');
    setLoading(false);
    setShowSuccess(false);
    onClose();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(isRTL ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency,
    }).format(amount);
  };

  const formatDate = (date: dayjs.Dayjs) => {
    return date.format('DD/MM/YYYY');
  };

  const handleQuickExtension = (days: number) => {
    const extendedDate = currentEnd.add(days, 'day');
    if (extendedDate.isBefore(maxDate) || extendedDate.isSame(maxDate)) {
      if (extendedDate.isAfter(currentEnd)) {
        setNewEndDate(extendedDate);
      }
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h6">{t('bookings.extend.title')}</Typography>
            <IconButton onClick={handleClose} size="small">
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent>
          <Box sx={{ mt: 2 }}>
            {/* Current Rental Information */}
            <Card sx={{ mb: 3, backgroundColor: 'background.default' }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  {t('bookings.extend.currentRentalInfo')}
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      {t('bookings.extend.currentEndDate')}
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                      {currentEndDate ? formatDate(dayjs(currentEndDate)) : t('Not set')}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      {t('bookings.extend.currentEndTime')}
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                      {currentEndTime}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      {t('bookings.extend.pricePerDay')}
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                      {formatCurrency(pricePerDay)}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      {t('bookings.extend.maxExtension')}
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                      {maxExtensionDays} {t('days')}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>

            {/* Quick Extension Options */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle1" sx={{ mb: 2 }}>
                {t('bookings.extend.quickOptions')}
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                {[1, 3, 7, 14].map((days) => (
                  <Button
                    key={days}
                    variant="contained"
                    size="small"
                    onClick={() => handleQuickExtension(days)}
                    disabled={loading}
                    sx={{
                      background: `linear-gradient(45deg, 
                        ${days === 1 ? '#FF6B6B' : ''} 
                        ${days === 3 ? '#4ECDC4' : ''}
                        ${days === 7 ? '#45B7D1' : ''}
                        ${days === 14 ? '#96C93D' : ''}, 
                        ${days === 1 ? '#FF8E8E' : ''}
                        ${days === 3 ? '#7EE6DE' : ''}
                        ${days === 7 ? '#6CD0E9' : ''}
                        ${days === 14 ? '#B8E986' : ''})`,
                      '&:hover': {
                        opacity: 0.9,
                      },
                      color: '#fff',
                      fontWeight: 'bold',
                      textShadow: '0 1px 2px rgba(0,0,0,0.2)',
                    }}
                  >
                    +{days} {t('days')}
                  </Button>
                ))}
              </Box>
            </Box>

            {/* Extension Details */}
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <DatePicker
                  label={t('bookings.extend.newEndDate')}
                  value={newEndDate}
                  onChange={(date) => setNewEndDate(date)}
                  minDate={minDate}
                  maxDate={maxDate}
                  disabled={loading}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      error: !!error && !newEndDate,
                      InputProps: {
                        startAdornment: (
                          <InputAdornment position="start">
                            <CalendarIcon />
                          </InputAdornment>
                        ),
                      },
                    },
                  }}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TimePicker
                  label={t('bookings.extend.newEndTime')}
                  value={newEndTime}
                  onChange={(time) => setNewEndTime(time)}
                  disabled={loading}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                    },
                  }}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>{t('bookings.extend.paymentMethod')}</InputLabel>
                  <Select
                    value={paymentMethod}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                    label={t('bookings.extend.paymentMethod')}
                    disabled={loading}
                  >
                    {PAYMENT_METHODS.map((method) => (
                      <MenuItem key={method.value} value={method.value}>
                        {t(method.label)}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', height: '100%' }}>
                  <Button
                    variant={isPaid ? 'contained' : 'outlined'}
                    onClick={() => setIsPaid(!isPaid)}
                    disabled={loading}
                    sx={{
                      backgroundColor: isPaid ? '#4caf50' : 'grey',
                      color: isPaid ? '#000' : '#4caf50',
                      borderColor: '#4caf50',
                      '&:hover': {
                        backgroundColor: isPaid ? '#388e3c' : 'orange',
                      },
                    }}
                  >
                    {isPaid ? t('Paid') : t('Unpaid')}
                  </Button>
                  <Button
                    variant={withInstallment ? 'contained' : 'outlined'}
                    onClick={() => setWithInstallment(!withInstallment)}
                    disabled={loading}
                    sx={{
                      backgroundColor: withInstallment ? '#2196f3' : 'grey',
                      color: withInstallment ? '#000' : '#2196f3',
                      borderColor: '#2196f3',
                      '&:hover': {
                        backgroundColor: 'orange',
                      },
                    }}
                  >
                    {withInstallment
                      ? t('bookings.extend.withInstallment')
                      : t('bookings.extend.noInstallment')}
                  </Button>
                </Box>
              </Grid>
            </Grid>

            {/* Extension Summary */}
            {newEndDate && (
              <Card sx={{ mt: 3, backgroundColor: 'primary.light', color: 'primary.contrastText' }}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    {t('bookings.extend.summary')}
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2" sx={{ opacity: 0.8 }}>
                        {t('bookings.extend.additionalDays')}
                      </Typography>
                      <Typography variant="h6">
                        {additionalDays} {t('days')}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" sx={{ opacity: 0.8 }}>
                        {t('bookings.extend.additionalCost')}
                      </Typography>
                      <Typography variant="h6">{formatCurrency(additionalCost)}</Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="body2" sx={{ opacity: 0.8 }}>
                        {t('bookings.extend.newEndDateTime')}
                      </Typography>
                      <Typography variant="h6">
                        {formatDate(newEndDate)} at {newEndTime?.format('HH:mm')}
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            )}

            {error && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {error}
              </Alert>
            )}
          </Box>
        </DialogContent>

        <DialogActions>
          <Button
            onClick={handleClose}
            disabled={loading}
            sx={{
              backgroundColor: '#f44336',
              color: 'white',
              '&:hover': {
                backgroundColor: '#d32f2f',
              },
              '&:disabled': {
                backgroundColor: '#cccccc',
                color: '#666666',
              },
            }}
          >
            {t('common.cancel')}
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={loading || !newEndDate || !newEndTime}
            sx={{
              backgroundColor: '#4caf50',
              color: 'white',
              '&:hover': {
                backgroundColor: '#388e3c',
              },
              '&:disabled': {
                backgroundColor: '#cccccc',
                color: '#666666',
              },
            }}
          >
            {loading ? <CircularProgress size={20} /> : t('bookings.extend.createRequest')}
          </Button>
        </DialogActions>

        <Snackbar
          open={showSuccess}
          autoHideDuration={1500}
          onClose={() => setShowSuccess(false)}
          anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        >
          <Alert severity="success" onClose={() => setShowSuccess(false)}>
            {t('bookings.extend.requestCreatedSuccessfully')}
          </Alert>
        </Snackbar>
      </Dialog>
    </LocalizationProvider>
  );
}
