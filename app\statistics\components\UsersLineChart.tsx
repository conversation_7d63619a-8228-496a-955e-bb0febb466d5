'use client';
import { useTranslation } from 'react-i18next';
import { useTheme, Box } from '@mui/material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from 'recharts';

interface UsersLineChartProps {
  data: any[];
}

export default function UsersLineChart({ data }: UsersLineChartProps) {
  const { t, i18n } = useTranslation();
  const theme = useTheme();

  if (!data || data.length === 0) {
    return <div>No data available</div>;
  }

  // Calculate min and max values for Y axis
  const minValue = Math.min(...data.map((item) => parseInt(item.uv)));
  const maxValue = Math.max(...data.map((item) => parseInt(item.uv)));

  // Add some padding to the min and max values
  const yDomain = [Math.max(0, minValue - 5), maxValue + 5];

  return (
    <Box
      sx={{ width: '100%', height: '100%' }}
      role="img"
      aria-label={t('Line chart showing user growth over time')}
    >
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={data}
          margin={{
            top: 10,
            right: 30,
            left: 0,
            bottom: 0,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
          <XAxis
            dataKey="name"
            tick={{ fill: theme.palette.text.secondary }}
            axisLine={{ stroke: theme.palette.divider }}
          />
          <YAxis
            domain={yDomain}
            tick={{ fill: theme.palette.text.secondary }}
            axisLine={{ stroke: theme.palette.divider }}
          />
          <Tooltip
            contentStyle={{
              backgroundColor: theme.palette.background.paper,
              border: `1px solid ${theme.palette.divider}`,
              borderRadius: 8,
              color: theme.palette.text.primary,
            }}
            labelStyle={{
              color: theme.palette.text.primary,
              fontWeight: 'bold',
              marginBottom: '5px',
            }}
            itemStyle={{
              color: theme.palette.text.primary,
            }}
          />
          <Legend
            wrapperStyle={{
              paddingTop: '10px',
            }}
            formatter={(value) => {
              return <span style={{ color: theme.palette.text.primary }}>{value}</span>;
            }}
          />
          <Line
            type="monotone"
            dataKey={i18n.language === 'ar' ? 'أجمالي المستخدمين' : 'Total Users'}
            stroke={theme.palette.info.main}
            strokeWidth={2}
            dot={{ r: 4, strokeWidth: 2 }}
            activeDot={{ r: 6, strokeWidth: 2 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </Box>
  );
}
