'use client';
import { useState } from 'react';
import { useQuery } from '@apollo/client';
import { Banners_Query } from '../gql/queries/banners';
import { Box, Switch, IconButton, Typography, Snackbar, Alert, useTheme } from '@mui/material';
import { Edit, Delete } from '@mui/icons-material';
import dayjs from 'dayjs';
import DataTable from '../components/molecules/table/DataTable';
import DataTableToolbar from '../components/molecules/table/DataTableToolbar';
import { TableColumn } from '../components/molecules/table/tableUtils';

interface Banner {
  id: string;
  imgAr: string;
  imgEn: string;
  isActive: boolean;
  displayOrder: number;
  createdAt: string;
}

const BannerList = () => {
  const theme = useTheme();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [orderBy, setOrderBy] = useState<string>('createdAt');
  const [order, setOrder] = useState<'asc' | 'desc'>('desc');
  const [deleteDialog, setDeleteDialog] = useState<{
    open: boolean;
    bannerId: string | null;
  }>({
    open: false,
    bannerId: null,
  });
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error';
  }>({
    open: false,
    message: '',
    severity: 'success',
  });

  const { loading, error, data, refetch } = useQuery(Banners_Query, {
    variables: {
      page: page + 1,
      limit: rowsPerPage,
      orderBy,
      order,
    },
    fetchPolicy: 'network-only',
  });

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleStatusChange = async (id: string, currentStatus: boolean) => {
    // Implement status change logic
  };

  if (loading)
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Loading Banners
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Please wait while we fetch the banner data...
        </Typography>
      </Box>
    );

  if (error)
    return (
      <Box sx={{ p: 3 }}>
        <Alert
          severity="error"
          variant="outlined"
          sx={{
            mb: 2,
            '& .MuiAlert-icon': {
              color: theme.palette.error.main,
            },
          }}
        >
          <Typography variant="subtitle1" component="div" fontWeight="medium">
            Error Loading Banners
          </Typography>
          <Typography variant="body2">{error.message}</Typography>
        </Alert>
      </Box>
    );

  const banners = data?.banners?.collection || [];
  const totalCount = data?.banners?.metadata?.totalCount || 0;

  const columns: TableColumn[] = [
    {
      id: 'id',
      label: 'ID',
      minWidth: 100,
      sortable: true,
    },
    {
      id: 'imgAr',
      label: 'Arabic Image',
      minWidth: 150,
      format: (value) => (
        <Box
          sx={{
            height: 50,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            '& img': {
              height: '100%',
              objectFit: 'contain',
              maxWidth: '100%',
              borderRadius: 1,
              border: (theme) => `1px solid ${theme.palette.divider}`,
            },
          }}
        >
          <img src={value} alt="Arabic banner" loading="lazy" />
        </Box>
      ),
    },
    {
      id: 'imgEn',
      label: 'English Image',
      minWidth: 150,
      format: (value) => (
        <Box
          sx={{
            height: 50,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            '& img': {
              height: '100%',
              objectFit: 'contain',
              maxWidth: '100%',
              borderRadius: 1,
              border: (theme) => `1px solid ${theme.palette.divider}`,
            },
          }}
        >
          <img src={value} alt="English banner" loading="lazy" />
        </Box>
      ),
    },
    {
      id: 'isActive',
      label: 'Status',
      minWidth: 100,
      format: (value, row) => (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Switch
            checked={value}
            onChange={() => handleStatusChange(row.id, value)}
            color="primary"
            // disabled={updating}
            inputProps={{
              'aria-label': value ? 'Active (click to deactivate)' : 'Inactive (click to activate)',
            }}
          />
          <Typography variant="body2" sx={{ ml: 1, color: 'text.secondary' }}>
            {value ? 'Active' : 'Inactive'}
          </Typography>
        </Box>
      ),
    },
    {
      id: 'displayOrder',
      label: 'Display Order',
      minWidth: 100,
      sortable: true,
      align: 'right',
    },
    {
      id: 'createdAt',
      label: 'Created At',
      minWidth: 150,
      sortable: true,
      format: (value) => dayjs(value).format('YYYY-MM-DD HH:mm'),
    },
    {
      id: 'actions',
      label: 'Actions',
      minWidth: 100,
      align: 'right',
      format: (_, row) => (
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
          <IconButton
            // onClick={() => handleEdit(row)}
            size="small"
            color="primary"
            aria-label={`Edit banner ${row.id}`}
            sx={{
              '&:focus-visible': {
                outline: (theme) => `2px solid ${theme.palette.primary.main}`,
                outlineOffset: '2px',
              },
            }}
          >
            <Edit fontSize="small" />
          </IconButton>
          <IconButton
            // onClick={() => handleDeleteClick(row)}
            size="small"
            color="error"
            // disabled={deleting}
            aria-label={`Delete banner ${row.id}`}
            sx={{
              '&:focus-visible': {
                outline: (theme) => `2px solid ${theme.palette.error.main}`,
                outlineOffset: '2px',
              },
            }}
          >
            <Delete fontSize="small" />
          </IconButton>
        </Box>
      ),
    },
  ];

  return (
    <Box
      sx={{
        width: '100%',
        '& .MuiPaper-root': {
          backgroundColor: theme.palette.background.paper,
          borderRadius: 2,
          boxShadow: theme.shadows[2],
        },
        '& .MuiTableHead-root': {
          '& .MuiTableCell-head': {
            backgroundColor:
              theme.palette.mode === 'dark' ? theme.palette.grey[800] : theme.palette.grey[100],
            fontWeight: 'bold',
          },
        },
        '& .MuiTableBody-root': {
          '& .MuiTableRow-root:hover': {
            backgroundColor:
              theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.04)',
          },
        },
      }}
      role="region"
      aria-label="Banners management section"
    >
      <DataTableToolbar
        title="Banners"
        numSelected={0}
        // onAdd={handleAdd}
      />
      <DataTable
        columns={columns}
        data={banners}
        loading={loading}
        rowsPerPageOptions={[5, 10, 25]}
        defaultRowsPerPage={10}
        page={page}
        rowsPerPage={rowsPerPage}
        totalCount={totalCount}
        onPageChange={(event, newPage) => {}}
        onRowsPerPageChange={(event) => {}}
        orderBy={orderBy}
        order={order}
        // onSortChange={handleSortChange}
      />
      {/* <Dialog
        open={deleteDialog.open}
        // onClose={handleDeleteCancel}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <DialogTitle id="delete-dialog-title">
          Delete Banner
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="delete-dialog-description">
            Are you sure you want to delete this banner? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} disabled={deleting}>
            Cancel
          </Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
            disabled={deleting}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog> */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        // onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          // onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default BannerList;
