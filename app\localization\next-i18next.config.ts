// lib/i18n.js
import i18next from "i18next";
import { initReactI18next } from "react-i18next";
import En from "./en.json";
import Ar from "./ar.json";

i18next.use(initReactI18next).init({
  fallbackLng: "ar",
  lng: "ar",
  interpolation: {
    escapeValue: false, // not needed for react as it escapes by default
  },
  resources: {
    ar: {
      translation: Ar,
    },
    en: {
      translation: En,
    },
  },
});

export default i18next;
