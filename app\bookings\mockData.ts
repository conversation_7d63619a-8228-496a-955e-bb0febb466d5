// Mock data for bookings
export const mockBookings = {
  collection: [
    {
      id: 1001,
      bookingNo: 'BK-10001',
      customerName: '<PERSON>',
      customerMobile: '+966501234567',
      allyCompanyId: 101,
      allyName: 'Premium Cars',
      arAllyName: 'سيارات بريميوم',
      enAllyName: 'Premium Cars',
      branchId: 201,
      branchName: 'Riyadh Main Branch',
      carId: 301,
      arMakeName: 'تويوتا',
      enMakeName: 'Toyota',
      arModelName: 'كامري',
      enModelName: 'Camry',
      arVersionName: 'SE',
      enVersionName: 'SE',
      year: 2023,
      plateNo: 'ABC 123',
      numberOfDays: 7,
      pricePerDay: 200,
      totalBookingPrice: 1400,
      totalInsurancePrice: 300,
      paidAmount: 1700,
      pickUpDate: '2023-05-15',
      pickUpTime: '10:00:00',
      dropOffDate: '2023-05-22',
      dropOffTime: '10:00:00',
      arPickUpCityName: 'الرياض',
      enPickUpCityName: 'Riyadh',
      arDropOffCityName: 'الرياض',
      enDropOffCityName: 'Riyadh',
      status: 'confirmed',
      subStatus: null,
      paymentMethod: 'credit_card',
      paymentBrand: 'Visa',
      createdAt: '2023-05-10T08:30:00',
      isIntegratedRental: false,
      lastRentalDateExtensionRequest: null,
      lastConfirmedExtensionRequest: null,
    },
    {
      id: 1002,
      bookingNo: 'BK-10002',
      customerName: 'Mohammed Hassan',
      customerMobile: '+966512345678',
      allyCompanyId: 102,
      allyName: 'Luxury Rentals',
      arAllyName: 'تأجير فاخر',
      enAllyName: 'Luxury Rentals',
      branchId: 202,
      branchName: 'Jeddah Main Branch',
      carId: 302,
      arMakeName: 'نيسان',
      enMakeName: 'Nissan',
      arModelName: 'التيما',
      enModelName: 'Altima',
      arVersionName: 'SV',
      enVersionName: 'SV',
      year: 2022,
      plateNo: 'DEF 456',
      numberOfDays: 3,
      pricePerDay: 180,
      totalBookingPrice: 540,
      totalInsurancePrice: 150,
      paidAmount: 690,
      pickUpDate: '2023-05-18',
      pickUpTime: '14:00:00',
      dropOffDate: '2023-05-21',
      dropOffTime: '14:00:00',
      arPickUpCityName: 'جدة',
      enPickUpCityName: 'Jeddah',
      arDropOffCityName: 'جدة',
      enDropOffCityName: 'Jeddah',
      status: 'pending',
      subStatus: null,
      paymentMethod: 'credit_card',
      paymentBrand: 'Mastercard',
      createdAt: '2023-05-12T10:15:00',
      isIntegratedRental: false,
      lastRentalDateExtensionRequest: null,
      lastConfirmedExtensionRequest: null,
    },
    {
      id: 1003,
      bookingNo: 'BK-10003',
      customerName: 'Sara Ahmed',
      customerMobile: '+966523456789',
      allyCompanyId: 103,
      allyName: 'Economy Cars',
      arAllyName: 'سيارات اقتصادية',
      enAllyName: 'Economy Cars',
      branchId: 203,
      branchName: 'Dammam Main Branch',
      carId: 303,
      arMakeName: 'هيونداي',
      enMakeName: 'Hyundai',
      arModelName: 'إلنترا',
      enModelName: 'Elantra',
      arVersionName: 'GLS',
      enVersionName: 'GLS',
      year: 2023,
      plateNo: 'GHI 789',
      numberOfDays: 5,
      pricePerDay: 150,
      totalBookingPrice: 750,
      totalInsurancePrice: 200,
      paidAmount: 950,
      pickUpDate: '2023-05-20',
      pickUpTime: '12:00:00',
      dropOffDate: '2023-05-25',
      dropOffTime: '12:00:00',
      arPickUpCityName: 'الدمام',
      enPickUpCityName: 'Dammam',
      arDropOffCityName: 'الدمام',
      enDropOffCityName: 'Dammam',
      status: 'car_received',
      subStatus: null,
      paymentMethod: 'apple_pay',
      paymentBrand: 'Apple Pay',
      createdAt: '2023-05-15T09:45:00',
      isIntegratedRental: true,
      lastRentalDateExtensionRequest: null,
      lastConfirmedExtensionRequest: null,
    },
    {
      id: 1004,
      bookingNo: 'BK-10004',
      customerName: 'Khalid Omar',
      customerMobile: '+966534567890',
      allyCompanyId: 101,
      allyName: 'Premium Cars',
      arAllyName: 'سيارات بريميوم',
      enAllyName: 'Premium Cars',
      branchId: 201,
      branchName: 'Riyadh Main Branch',
      carId: 304,
      arMakeName: 'كيا',
      enMakeName: 'Kia',
      arModelName: 'سبورتاج',
      enModelName: 'Sportage',
      arVersionName: 'LX',
      enVersionName: 'LX',
      year: 2022,
      plateNo: 'JKL 012',
      numberOfDays: 10,
      pricePerDay: 220,
      totalBookingPrice: 2200,
      totalInsurancePrice: 400,
      paidAmount: 2600,
      pickUpDate: '2023-05-25',
      pickUpTime: '09:00:00',
      dropOffDate: '2023-06-04',
      dropOffTime: '09:00:00',
      arPickUpCityName: 'الرياض',
      enPickUpCityName: 'Riyadh',
      arDropOffCityName: 'الرياض',
      enDropOffCityName: 'Riyadh',
      status: 'invoiced',
      subStatus: null,
      paymentMethod: 'credit_card',
      paymentBrand: 'Visa',
      createdAt: '2023-05-18T14:20:00',
      isIntegratedRental: false,
      lastRentalDateExtensionRequest: {
        pickUpDate: '2023-05-25',
        dropOffDate: '2023-06-08',
        numberOfDays: 14,
        totalBookingPrice: 3080,
        totalInsurancePrice: 560,
      },
      lastConfirmedExtensionRequest: {
        pickUpDate: '2023-05-25',
        dropOffDate: '2023-06-08',
        numberOfDays: 14,
        totalBookingPrice: 3080,
        totalInsurancePrice: 560,
      },
    },
    {
      id: 1005,
      bookingNo: 'BK-10005',
      customerName: 'Fatima Ali',
      customerMobile: '+966545678901',
      allyCompanyId: 102,
      allyName: 'Luxury Rentals',
      arAllyName: 'تأجير فاخر',
      enAllyName: 'Luxury Rentals',
      branchId: 202,
      branchName: 'Jeddah Main Branch',
      carId: 305,
      arMakeName: 'مرسيدس',
      enMakeName: 'Mercedes',
      arModelName: 'سي-كلاس',
      enModelName: 'C-Class',
      arVersionName: 'C300',
      enVersionName: 'C300',
      year: 2023,
      plateNo: 'MNO 345',
      numberOfDays: 2,
      pricePerDay: 500,
      totalBookingPrice: 1000,
      totalInsurancePrice: 300,
      paidAmount: 1300,
      pickUpDate: '2023-05-30',
      pickUpTime: '16:00:00',
      dropOffDate: '2023-06-01',
      dropOffTime: '16:00:00',
      arPickUpCityName: 'جدة',
      enPickUpCityName: 'Jeddah',
      arDropOffCityName: 'جدة',
      enDropOffCityName: 'Jeddah',
      status: 'closed',
      subStatus: null,
      paymentMethod: 'credit_card',
      paymentBrand: 'American Express',
      createdAt: '2023-05-20T11:30:00',
      isIntegratedRental: false,
      lastRentalDateExtensionRequest: null,
      lastConfirmedExtensionRequest: null,
    },
    {
      id: 1006,
      bookingNo: 'BK-10006',
      customerName: 'Nasser Mohammed',
      customerMobile: '+966556789012',
      allyCompanyId: 103,
      allyName: 'Economy Cars',
      arAllyName: 'سيارات اقتصادية',
      enAllyName: 'Economy Cars',
      branchId: 203,
      branchName: 'Dammam Main Branch',
      carId: 306,
      arMakeName: 'فورد',
      enMakeName: 'Ford',
      arModelName: 'إيكو سبورت',
      enModelName: 'EcoSport',
      arVersionName: 'SE',
      enVersionName: 'SE',
      year: 2022,
      plateNo: 'PQR 678',
      numberOfDays: 4,
      pricePerDay: 170,
      totalBookingPrice: 680,
      totalInsurancePrice: 180,
      paidAmount: 860,
      pickUpDate: '2023-06-01',
      pickUpTime: '11:00:00',
      dropOffDate: '2023-06-05',
      dropOffTime: '11:00:00',
      arPickUpCityName: 'الدمام',
      enPickUpCityName: 'Dammam',
      arDropOffCityName: 'الدمام',
      enDropOffCityName: 'Dammam',
      status: 'cancelled',
      subStatus: 'customer_cancelled',
      paymentMethod: 'credit_card',
      paymentBrand: 'Mastercard',
      createdAt: '2023-05-22T13:10:00',
      isIntegratedRental: false,
      lastRentalDateExtensionRequest: null,
      lastConfirmedExtensionRequest: null,
    },
    {
      id: 1007,
      bookingNo: 'BK-10007',
      customerName: 'Layla Hassan',
      customerMobile: '+966567890123',
      allyCompanyId: 101,
      allyName: 'Premium Cars',
      arAllyName: 'سيارات بريميوم',
      enAllyName: 'Premium Cars',
      branchId: 201,
      branchName: 'Riyadh Main Branch',
      carId: 307,
      arMakeName: 'تويوتا',
      enMakeName: 'Toyota',
      arModelName: 'راف 4',
      enModelName: 'RAV4',
      arVersionName: 'XLE',
      enVersionName: 'XLE',
      year: 2023,
      plateNo: 'STU 901',
      numberOfDays: 6,
      pricePerDay: 230,
      totalBookingPrice: 1380,
      totalInsurancePrice: 320,
      paidAmount: 1700,
      pickUpDate: '2023-06-05',
      pickUpTime: '13:00:00',
      dropOffDate: '2023-06-11',
      dropOffTime: '13:00:00',
      arPickUpCityName: 'الرياض',
      enPickUpCityName: 'Riyadh',
      arDropOffCityName: 'الرياض',
      enDropOffCityName: 'Riyadh',
      status: 'confirmed',
      subStatus: null,
      paymentMethod: 'stc_pay',
      paymentBrand: 'STC Pay',
      createdAt: '2023-05-25T15:45:00',
      isIntegratedRental: false,
      lastRentalDateExtensionRequest: null,
      lastConfirmedExtensionRequest: null,
    },
    {
      id: 1008,
      bookingNo: 'BK-10008',
      customerName: 'Omar Saeed',
      customerMobile: '+966578901234',
      allyCompanyId: 102,
      allyName: 'Luxury Rentals',
      arAllyName: 'تأجير فاخر',
      enAllyName: 'Luxury Rentals',
      branchId: 202,
      branchName: 'Jeddah Main Branch',
      carId: 308,
      arMakeName: 'بي إم دبليو',
      enMakeName: 'BMW',
      arModelName: 'الفئة 3',
      enModelName: '3 Series',
      arVersionName: '330i',
      enVersionName: '330i',
      year: 2023,
      plateNo: 'VWX 234',
      numberOfDays: 8,
      pricePerDay: 450,
      totalBookingPrice: 3600,
      totalInsurancePrice: 600,
      paidAmount: 4200,
      pickUpDate: '2023-06-10',
      pickUpTime: '15:00:00',
      dropOffDate: '2023-06-18',
      dropOffTime: '15:00:00',
      arPickUpCityName: 'جدة',
      enPickUpCityName: 'Jeddah',
      arDropOffCityName: 'جدة',
      enDropOffCityName: 'Jeddah',
      status: 'pending',
      subStatus: null,
      paymentMethod: 'credit_card',
      paymentBrand: 'Visa',
      createdAt: '2023-05-28T09:20:00',
      isIntegratedRental: true,
      lastRentalDateExtensionRequest: null,
      lastConfirmedExtensionRequest: null,
    },
    {
      id: 1009,
      bookingNo: 'BK-10009',
      customerName: 'Amal Khalid',
      customerMobile: '+966589012345',
      allyCompanyId: 103,
      allyName: 'Economy Cars',
      arAllyName: 'سيارات اقتصادية',
      enAllyName: 'Economy Cars',
      branchId: 203,
      branchName: 'Dammam Main Branch',
      carId: 309,
      arMakeName: 'هوندا',
      enMakeName: 'Honda',
      arModelName: 'أكورد',
      enModelName: 'Accord',
      arVersionName: 'LX',
      enVersionName: 'LX',
      year: 2022,
      plateNo: 'YZA 567',
      numberOfDays: 5,
      pricePerDay: 190,
      totalBookingPrice: 950,
      totalInsurancePrice: 250,
      paidAmount: 1200,
      pickUpDate: '2023-06-15',
      pickUpTime: '10:30:00',
      dropOffDate: '2023-06-20',
      dropOffTime: '10:30:00',
      arPickUpCityName: 'الدمام',
      enPickUpCityName: 'Dammam',
      arDropOffCityName: 'الدمام',
      enDropOffCityName: 'Dammam',
      status: 'confirmed',
      subStatus: null,
      paymentMethod: 'credit_card',
      paymentBrand: 'Mastercard',
      createdAt: '2023-05-30T12:15:00',
      isIntegratedRental: false,
      lastRentalDateExtensionRequest: null,
      lastConfirmedExtensionRequest: null,
    },
    {
      id: 1010,
      bookingNo: 'BK-10010',
      customerName: 'Yousef Ali',
      customerMobile: '+966590123456',
      allyCompanyId: 101,
      allyName: 'Premium Cars',
      arAllyName: 'سيارات بريميوم',
      enAllyName: 'Premium Cars',
      branchId: 201,
      branchName: 'Riyadh Main Branch',
      carId: 310,
      arMakeName: 'لكزس',
      enMakeName: 'Lexus',
      arModelName: 'إي إس',
      enModelName: 'ES',
      arVersionName: 'ES 350',
      enVersionName: 'ES 350',
      year: 2023,
      plateNo: 'BCD 890',
      numberOfDays: 3,
      pricePerDay: 400,
      totalBookingPrice: 1200,
      totalInsurancePrice: 350,
      paidAmount: 1550,
      pickUpDate: '2023-06-20',
      pickUpTime: '14:30:00',
      dropOffDate: '2023-06-23',
      dropOffTime: '14:30:00',
      arPickUpCityName: 'الرياض',
      enPickUpCityName: 'Riyadh',
      arDropOffCityName: 'الرياض',
      enDropOffCityName: 'Riyadh',
      status: 'pending',
      subStatus: null,
      paymentMethod: 'credit_card',
      paymentBrand: 'Visa',
      createdAt: '2023-06-01T16:40:00',
      isIntegratedRental: false,
      lastRentalDateExtensionRequest: null,
      lastConfirmedExtensionRequest: null,
    }
  ],
  metadata: {
    currentPage: 1,
    totalCount: 120,
    totalPages: 12,
    limit: 10
  }
};
