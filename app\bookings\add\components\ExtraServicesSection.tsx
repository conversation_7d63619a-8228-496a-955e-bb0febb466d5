import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Paper,
  FormControlLabel,
  Checkbox,
  Grid,
  Divider,
  Chip,
} from '@mui/material';

interface ExtraService {
  id: string;
  extraService?: {
    id: string;
    arTitle?: string;
    enTitle?: string;
  };
  serviceValue?: number;
  price?: number;
  isRequired?: boolean;
  arTitle?: string;
  enTitle?: string;
  title?: string;
  name?: string;
  allyExtraService?: {
    extraService?: {
      id: string;
      arTitle?: string;
      enTitle?: string;
    };
  };
}

interface ExtraServicesSectionProps {
  extraServices: ExtraService[];
  selectedServices: ExtraService[];
  onServicesChange: (services: ExtraService[]) => void;
  disabled?: boolean;
  clicked?: boolean;
  selectedCar?: any;
  unlimited?: boolean;
  onUnlimitedChange?: (unlimited: boolean) => void;
}

export default function ExtraServicesSection({
  extraServices,
  selectedServices,
  onServicesChange,
  disabled = false,
  clicked = false,
  selectedCar,
  unlimited = false,
  onUnlimitedChange,
}: ExtraServicesSectionProps) {
  const { t, i18n } = useTranslation();
  const locale = i18n.language;

  const handleServiceToggle = (service: ExtraService, checked: boolean) => {
    if (checked) {
      // Add service to selected services
      if (!selectedServices.find((s) => s.id === service.id)) {
        onServicesChange([...selectedServices, service]);
      }
    } else {
      // Remove service from selected services
      onServicesChange(selectedServices.filter((s) => s.id !== service.id));
    }
  };

  const showUnlimited = selectedCar?.isUnlimited && onUnlimitedChange;

  if (!extraServices || (extraServices.length === 0 && !showUnlimited)) {
    return (
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          {t('extra.services')}
        </Typography>
        <Typography variant="body2" color="textSecondary">
          {t('no.extra.services.available')}
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        {t('extra.services')}
      </Typography>

      <Grid container spacing={2}>
        {extraServices.map((service) => {
          const isChecked = service.isRequired || selectedServices.some((s) => s.id === service.id);

          // Safe access to title with multiple fallback options
          const title =
            locale === 'ar'
              ? service.extraService?.arTitle ||
                service.allyExtraService?.extraService?.arTitle ||
                service.arTitle ||
                service.title ||
                service.name ||
                'Unknown Service'
              : service.extraService?.enTitle ||
                service.allyExtraService?.extraService?.enTitle ||
                service.enTitle ||
                service.title ||
                service.name ||
                'Unknown Service';

          return (
            <Grid item xs={12} md={6} key={service.id}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={isChecked}
                      onChange={(e) => handleServiceToggle(service, e.target.checked)}
                      disabled={disabled || service.isRequired}
                      color="primary"
                    />
                  }
                  label={
                    <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                      <Typography variant="body1">{title}</Typography>
                      <Typography variant="body2" color="textSecondary">
                        {service.serviceValue || service.price || 0} {t('currency.sr')}
                      </Typography>
                    </Box>
                  }
                />
                {service.isRequired && <Chip label={t('required')} size="small" color="error" />}
              </Box>
            </Grid>
          );
        })}

        {/* Unlimited KM Option - matching old implementation */}
        {showUnlimited && (
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={unlimited}
                    onChange={(e) => onUnlimitedChange!(e.target.checked)}
                    disabled={disabled}
                    color="primary"
                  />
                }
                label={
                  <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                    <Typography variant="body1">{t('Unlimited.KM')}</Typography>
                    <Typography variant="body2" color="textSecondary">
                      {t('price.sr/day', { price: selectedCar.unlimitedFeePerDay })}
                    </Typography>
                  </Box>
                }
              />
            </Box>
          </Grid>
        )}
      </Grid>

      {/* Services Summary */}
      {(selectedServices.length > 0 || unlimited) && (
        <Box
          sx={{
            mt: 3,
            p: 2,
            bgcolor: 'background.paper',
            border: 1,
            borderColor: 'divider',
            borderRadius: 1,
          }}
        >
          <Typography variant="subtitle2" gutterBottom>
            {t('selected.services.summary')}:
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {selectedServices.map((service) => {
              // Safe access to title with multiple fallback options
              const title =
                locale === 'ar'
                  ? service.extraService?.arTitle ||
                    service.allyExtraService?.extraService?.arTitle ||
                    service.arTitle ||
                    service.title ||
                    service.name ||
                    'Unknown Service'
                  : service.extraService?.enTitle ||
                    service.allyExtraService?.extraService?.enTitle ||
                    service.enTitle ||
                    service.title ||
                    service.name ||
                    'Unknown Service';

              return (
                <Chip
                  key={service.id}
                  label={`${title} - ${service.serviceValue || service.price || 0} ${t(
                    'currency.sr'
                  )}`}
                  size="small"
                  color="primary"
                />
              );
            })}
            {unlimited && (
              <Chip
                label={`${t('Unlimited.KM')} - ${selectedCar?.unlimitedFeePerDay || 0} ${t(
                  'currency.sr'
                )}/day`}
                size="small"
                color="primary"
              />
            )}
          </Box>
          <Typography variant="body2" color="primary" sx={{ mt: 1 }}>
            {t('total')}:{' '}
            {selectedServices.reduce(
              (total, service) => total + (service.serviceValue || service.price || 0),
              0
            ) + (unlimited ? selectedCar?.unlimitedFeePerDay || 0 : 0)}{' '}
            {t('currency.sr')}
          </Typography>
        </Box>
      )}
    </Paper>
  );
}
