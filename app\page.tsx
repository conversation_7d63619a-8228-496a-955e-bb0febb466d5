'use client';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSelector } from 'react-redux';
import { getAuthToken } from './utils/cookies';
import { CircularProgress, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
interface RootState {
  auth: {
    isAuthenticated: boolean;
    token: string | null;
  };
}

export default function HomePage() {
  const router = useRouter();
  const { t } = useTranslation();
  const { isAuthenticated, token } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    // Check for token in cookies/localStorage and Redux state
    const authToken = getAuthToken();

    // If user is authenticated (either by Redux state or token exists)
    if (isAuthenticated || authToken) {
      // Redirect to statistics page
      router.replace('/statistics');
    } else {
      // Redirect to login page
      router.replace('/login');
    }
  }, [router, isAuthenticated]);

  // Show loading while checking authentication
  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '18px',
        flexDirection: 'column',
      }}
    >
      <CircularProgress />
      <Typography variant="h6" color="textSecondary">
        {t('Rental_Future')}
      </Typography>
    </div>
  );
}
