'use client';
import { useState, useMemo } from 'react';
import { useQuery } from '@apollo/client';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { useRouter } from 'next/navigation';
import {
  Box,
  Typography,
  Paper,
  Button,
  Collapse,
  Badge,
  CircularProgress,
  Alert,
  useTheme,
  useMediaQuery,
  Chip,
  Skeleton,
} from '@mui/material';
import FilterListIcon from '@mui/icons-material/FilterList';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import AddIcon from '@mui/icons-material/Add';
// import { mockBookings } from './mockData'; // No longer needed
import BookingsFilters from './components/BookingsFilters';
import BookingsTable from './components/BookingsTable';
import BookingsMobileView from './components/BookingsMobileView';
import { BOOKINGS_QUERY, BOOKINGS_COUNT_QUERY } from '../gql/queries/bookings';

export default function BookingsContent() {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useSelector((state: any) => state.auth);
  const isAlly = user?.ally_id;
  const router = useRouter();

  // State for filters and pagination
  const [filtersOpen, setFiltersOpen] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  // We don't need isClient anymore since we're using a custom tabs implementation
  const [page, setPage] = useState(1);
  // Set default limit to 50 items per page
  const [limit, setLimit] = useState(50);
  const [filters, setFilters] = useState({});
  const [orderBy, setOrderBy] = useState('pick_up_datetime');
  const [sortBy, setSortBy] = useState('desc');

  // Fetch bookings data
  const {
    data: bookingsData,
    loading: bookingsLoading,
    error: bookingsError,
    refetch,
  } = useQuery(BOOKINGS_QUERY, {
    variables: {
      page,
      limit: 50, // Force limit to 50
      orderBy,
      sortBy,
      ...filters,
    },
  });

  // Fetch booking counts for tabs
  const {
    data: countsData,
    loading: countsLoading,
    error: countsError,
  } = useQuery(BOOKINGS_COUNT_QUERY);

  // Use real data from GraphQL queries
  const bookings = bookingsLoading ? [] : bookingsData?.dashboardRentals?.collection || [];
  const bookingsCount = bookingsLoading
    ? { currentPage: 1, totalCount: 0 }
    : bookingsData?.dashboardRentals?.metadata || { currentPage: 1, totalCount: 0 };

  // Process the API response to get all status counts
  const statusCounts = useMemo(() => {
    if (countsLoading) {
      return [
        ['All', '-'],
        ['Pending', '-'],
        ['Confirmed', '-'],
        ['Car Received', '-'],
        ['Invoiced', '-'],
        ['Closed', '-'],
        ['Cancelled', '-'],
      ];
    }

    // Check if we have the all array in the response
    if (countsData?.rentalsCount?.all && Array.isArray(countsData.rentalsCount.all)) {
      // Format from API response which is an array of [status, count] pairs
      // Return the data as-is to preserve the original format from the API
      // The translation will handle both formats (spaces and snake_case)
      return countsData.rentalsCount.all;
    } else {
      // Fallback to the old format if the new format is not available
      return [
        ['All', countsData?.rentalsCount?.all || 0],
        ['Pending', countsData?.rentalsCount?.pending || 0],
        ['Confirmed', countsData?.rentalsCount?.confirmed || 0],
        ['Car Received', countsData?.rentalsCount?.car_received || 0],
        ['Invoiced', countsData?.rentalsCount?.invoiced || 0],
        ['Closed', countsData?.rentalsCount?.closed || 0],
        ['Cancelled', countsData?.rentalsCount?.cancelled || 0],
      ];
    }
  }, [countsLoading, countsData]);

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent | null, newValue: number) => {
    setActiveTab(newValue);

    // Reset pagination
    setPage(1);

    // Apply filter based on selected tab
    if (newValue === 0) {
      // All bookings
      setFilters({});
    } else {
      // Filter by status
      // Get the status name from the selected tab
      const statusName = String(statusCounts[newValue][0]);

      // Check if the status name is already in snake_case format (from API)
      const status = statusName.includes('_')
        ? statusName
        : statusName.toLowerCase().replace(/ /g, '_');

      setFilters({ status });
    }

    // Refetch data with new filters
    refetch();
  };

  // Handle filter toggle
  const toggleFilters = () => {
    setFiltersOpen(!filtersOpen);
  };

  // Handle filter apply
  const handleFilterApply = (newFilters: any) => {
    setFilters({ ...filters, ...newFilters });
    setPage(1);
    refetch();
  };

  // Handle filter reset
  const handleFilterReset = () => {
    setFilters({});
    setPage(1);
    refetch();
  };

  // Handle sort change
  const handleSortChange = (field: string, direction: 'asc' | 'desc') => {
    setOrderBy(field);
    setSortBy(direction);
    refetch();
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    refetch();
  };

  // Handle rows per page change
  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1);
    refetch();
  };

  // Loading state
  const isLoading = bookingsLoading || countsLoading;

  // Error state
  const hasError = bookingsError || countsError;

  if (hasError) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{t('Error loading bookings data. Please try again later.')}</Alert>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        p: { xs: 0.5, sm: 1, md: 2 },
        maxWidth: '100%',
        overflow: 'hidden',
      }}
    >
      {/* Page Header */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexDirection: { xs: 'column', sm: 'row' },
          gap: 2,
          mb: 3,
        }}
      >
        <Typography
          variant="h4"
          component="h1"
          sx={{
            fontWeight: 600,
            fontSize: { xs: '1.5rem', md: '2rem' },
            color: theme.palette.mode === 'dark' ? '#fff' : 'inherit',
            textShadow: theme.palette.mode === 'dark' ? '0 2px 4px rgba(0,0,0,0.5)' : 'none',
            position: 'relative',
            '&::after':
              theme.palette.mode === 'dark'
                ? {
                    content: '""',
                    position: 'absolute',
                    bottom: -5,
                    left: 0,
                    width: '40px',
                    height: '3px',
                    backgroundColor: theme.palette.primary.main,
                    borderRadius: '2px',
                  }
                : {},
          }}
        >
          {t('Bookings')}
        </Typography>

        <Box
          sx={{
            display: 'flex',
            gap: 2,
            alignSelf: { xs: 'stretch', sm: 'auto' },
            flexDirection: { xs: 'column', sm: 'row' },
            width: { xs: '100%', sm: 'auto' },
            justifyContent: 'flex-end',
            p: 0,
          }}
        >
          <Button
            startIcon={
              isRTL ? filtersOpen ? <ExpandLessIcon /> : <ExpandMoreIcon /> : <FilterListIcon />
            }
            endIcon={
              isRTL ? <FilterListIcon /> : filtersOpen ? <ExpandLessIcon /> : <ExpandMoreIcon />
            }
            onClick={toggleFilters}
            color="primary"
            variant={filtersOpen ? 'contained' : 'outlined'}
            aria-expanded={filtersOpen}
            aria-label={filtersOpen ? t('Hide Filters') : t('Show Filters')}
            sx={{
              borderRadius: 2,
              px: 2,
              py: 0.75,
              fontWeight: 500,
              // boxShadow: filtersOpen ? 2 : 0,
              // backgroundColor: filtersOpen
              //   ? theme.palette.primary.main
              //   : theme.palette.primary.light,
              color: `${
                filtersOpen ? theme.palette.primary.contrastText : theme.palette.primary.main
              } !important`,
              border: `2px solid ${theme.palette.primary.main}`,
              // borderColor: theme.palette.primary.dark,

              boxShadow: filtersOpen ? 3 : 1,
              borderColor: theme.palette.primary.dark,
              // backgroundColor: filtersOpen
              //   ? theme.palette.primary.dark
              //   : `${theme.palette.primary.main}`,
              // color: filtersOpen ? theme.palette.primary.contrastText : theme.palette.primary.main,

              transition: 'all 0.2s ease-in-out',
              minWidth: 120,
              width: { xs: '100%', sm: 'auto' },
            }}
          >
            {t('Filters')}
          </Button>

          {!isAlly && (
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={() => router.push('/bookings/add')}
              sx={{
                borderRadius: 2,
                px: 3,
                py: 1,
                fontWeight: 500,
                boxShadow: 2,
                '&:hover': {
                  boxShadow: 4,
                },
                width: { xs: '100%', sm: 'auto' },
              }}
              aria-label={t('Create New Booking')}
            >
              {t('Create New Booking')}
            </Button>
          )}
        </Box>
      </Box>

      {/* Main Content */}
      <Paper
        elevation={3}
        sx={{
          borderRadius: 2,
          overflow: 'hidden',
          mb: 3,
          boxShadow:
            theme.palette.mode === 'dark'
              ? '0 4px 20px 0 rgba(0,0,0,0.5)'
              : '0 4px 20px 0 rgba(0,0,0,0.1)',
          transition: 'all 0.3s ease-in-out',
          border: theme.palette.mode === 'dark' ? '1px solid rgba(255,255,255,0.05)' : 'none',
          '&:hover': {
            boxShadow:
              theme.palette.mode === 'dark'
                ? '0 6px 25px 0 rgba(0,0,0,0.6)'
                : '0 6px 25px 0 rgba(0,0,0,0.15)',
            borderColor: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.1)' : 'transparent',
          },
          width: '100%',
          maxWidth: '100%',
          mx: 'auto',
          // px: { xs: 0, sm:
          // 2 }, // Remove padding on mobile, add padding on larger screens
        }}
      >
        {/* Status Tabs with Material UI */}
        <Box
          sx={{ width: '100%', bgcolor: 'background.paper', maxWidth: '100%', overflow: 'hidden' }}
        >
          <Box
            sx={{
              borderBottom: 1,
              borderColor: 'divider',
              background: theme.palette.mode === 'dark' ? '#2a2a2a' : '#f5f5f5',
            }}
          >
            <Box
              sx={{
                overflowX: 'auto',
                display: 'flex',
                width: '100%',
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  minWidth: 'max-content',
                  gap: '4px',
                  px: 2,
                  py: 1,
                }}
              >
                {statusCounts.map((status: any, index: number) => (
                  <Box
                    key={status[0]}
                    onClick={() => handleTabChange(null as any, index)}
                    sx={{
                      px: { xs: 1.5, md: 2 },
                      py: 1.5,
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderBottom:
                        activeTab === index
                          ? `3px solid ${theme.palette.primary.main}`
                          : '3px solid transparent',
                      color:
                        activeTab === index
                          ? theme.palette.primary.main
                          : theme.palette.text.primary,
                      fontWeight: activeTab === index ? 600 : 500,
                      transition: 'all 0.2s ease',
                      '&:hover': {
                        backgroundColor: theme.palette.action.hover,
                        color: theme.palette.primary.main,
                      },
                      whiteSpace: 'nowrap',
                      flexShrink: 0,
                      position: 'relative',
                      zIndex: 1,
                      borderRadius: '4px 4px 0 0',
                      mx: 0.5,
                      width: 'max-content !important',
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'end', gap: 0.1 }}>
                      <Typography
                        component="span"
                        sx={{
                          fontWeight: 'inherit',
                          fontSize: { xs: '0.75rem', sm: '0.875rem', md: '0.9rem' },
                          textOverflow: 'ellipsis',
                          overflow: 'hidden',
                          whiteSpace: 'nowrap',
                          maxWidth: '100%',
                          marginInlineEnd: 0.5, // Use logical property for RTL support
                        }}
                      >
                        {/* Try multiple translation approaches to ensure proper localization */}
                        {(() => {
                          const originalText = String(status[0]);
                          const translatedOriginal = t(originalText);

                          // If translation succeeded, use it
                          if (translatedOriginal !== originalText) {
                            return translatedOriginal;
                          }

                          // Try snake_case version
                          const snakeCase = originalText.toLowerCase().replace(/ /g, '_');
                          const translatedSnakeCase = t(snakeCase);
                          if (translatedSnakeCase !== snakeCase) {
                            return translatedSnakeCase;
                          }

                          // Try title case version
                          const titleCase = originalText
                            .split(' ')
                            .map(
                              (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
                            )
                            .join(' ');
                          const translatedTitleCase = t(titleCase);
                          if (translatedTitleCase !== titleCase) {
                            return translatedTitleCase;
                          }

                          // Fallback to original
                          return originalText;
                        })()}
                      </Typography>
                      {countsLoading ? (
                        <Skeleton variant="circular" width={20} height={20} />
                      ) : (
                        <Badge
                          badgeContent={status[1]}
                          max={99999999999999}
                          color="primary"
                          sx={{
                            marginInlineStart: 0.5, // Use logical property for RTL support
                            '& .MuiBadge-badge': {
                              fontSize: '0.7rem',
                              height: 18,
                              minWidth: 18,
                              borderRadius: 9,
                              fontWeight: 600,
                              backgroundColor:
                                activeTab === index
                                  ? theme.palette.primary.main
                                  : theme.palette.mode === 'dark'
                                  ? 'rgba(255,255,255,0.15)'
                                  : 'rgba(0,0,0,0.1)',
                              color:
                                activeTab === index
                                  ? theme.palette.primary.contrastText
                                  : theme.palette.text.secondary,
                              transform: 'translate(0, 0)',
                              position: 'relative',
                            },
                          }}
                        />
                      )}
                    </Box>
                  </Box>
                ))}
              </Box>
            </Box>
          </Box>
        </Box>
        {/* Filters Toggle Button */}
        <Box
          sx={{
            p: 2,
            borderBottom: 1,
            borderColor: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.1)' : 'divider',
            backgroundColor:
              theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.02)' : 'transparent',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              flexDirection: { xs: 'column', sm: 'row' },
              justifyContent: 'space-between',
              alignItems: { xs: 'flex-start', sm: 'center' },
              gap: 1,
            }}
          >
            <Box
              sx={{
                display: 'flex',
                gap: 1,
                flexWrap: 'wrap',
                width: '100%',
                maxWidth: '100%',
                overflow: 'hidden',
              }}
            >
              {Object.keys(filters).length > 0 && (
                <>
                  <Typography
                    variant="body2"
                    sx={{ fontWeight: 500, display: 'flex', alignItems: 'center' }}
                  >
                    {t('Active Filters')}:
                  </Typography>
                  {Object.entries(filters).map(
                    ([key, value]) =>
                      value && (
                        <Chip
                          key={key}
                          label={`${t(
                            key.charAt(0).toUpperCase() + key.slice(1).replace('_', ' ')
                          )}: ${value}`}
                          onDelete={() => {
                            const newFilters = { ...filters } as Record<string, any>;
                            delete newFilters[key];
                            setFilters(newFilters as any);
                            refetch();
                          }}
                          size="small"
                          color="primary"
                          variant="outlined"
                          sx={{
                            maxWidth: { xs: '100%', sm: 'auto' },
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            mb: 0.5,
                          }}
                        />
                      )
                  )}
                  <Button size="small" onClick={handleFilterReset} sx={{ ml: 1 }}>
                    {t('Clear All')}
                  </Button>
                </>
              )}
            </Box>
            {/* Filter button moved to top */}
          </Box>

          <Collapse in={filtersOpen} timeout="auto">
            <Box
              sx={{
                mt: 2,
                px: { xs: 1, sm: 2 },
                maxWidth: '100%',
                overflow: 'hidden',
              }}
            >
              <BookingsFilters
                onApply={handleFilterApply}
                onReset={handleFilterReset}
                initialFilters={filters}
              />
            </Box>
          </Collapse>
        </Box>

        {/* Bookings List */}
        <Box
          sx={{
            p: { xs: 0.5, sm: 1, md: 2 },
            maxWidth: '100%',
            overflow: 'hidden',
          }}
        >
          {isLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              {isMobile ? (
                <BookingsMobileView
                  bookings={bookings}
                  page={page}
                  limit={limit}
                  total={bookingsCount.totalCount}
                  onPageChange={handlePageChange}
                  onLimitChange={handleLimitChange}
                />
              ) : (
                <BookingsTable
                  bookings={bookings}
                  page={page}
                  limit={limit}
                  total={bookingsCount.totalCount}
                  onPageChange={handlePageChange}
                  onLimitChange={handleLimitChange}
                  onSortChange={handleSortChange}
                  orderBy={orderBy}
                  sortBy={sortBy}
                  refetch={refetch}
                />
              )}
            </>
          )}
        </Box>
      </Paper>
    </Box>
  );
}
