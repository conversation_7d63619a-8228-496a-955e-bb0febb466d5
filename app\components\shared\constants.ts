/* eslint-disable prettier/prettier */

export const persist = {
    initLimit: 10,
    unlimitedLimit: 100,
    higherUnlimited: 500,
    megeLimit: 1000,
    customerPhoneLength: 12,
    customerMinPhoneLength: 12,
  };
  
  export const Alert = (text:any) => `🐞 ${text}`;
  
  export const dataTypes = {
    TEXT: "TEXT",
    DATE: "DATE",
    DATETIME: "DATETIME",
    TIME: "TIME",
    NUMBER: "NUMBER",
    PRICE: "PRICE",
    FUNC: "FUNC",
    BILINGUAL: "BILINGUAL", // TEXT ALSO
    TRANSLATE: "TRANSLATE", // ID that exists in locale files
    ACTIONS: "ACTIONS",
    ASSIGNBOOKING: "ASSIGNBOOKING",
    REFUNDBOOKING: "REFUNDBOOKING",
    RECALLPAYMENTGATEWAY: "RECALLPAYMENTGATEWAY",
  };
  export const generateArrayOfYears = () => {
    const max = new Date().getFullYear() + 2;
    const min = max - 50;
    const years = [];
  
    for (let i = max; i >= min; i -= 1) {
      years.push(i);
    }
    return years;
  };
  export const rentalMonths = [
    { label: "two.month.rent", value: "2" },
    { label: "three.month.rent", value: "3" },
    { label: "four.month.rent", value: "4" },
    { label: "five.month.rent", value: "5" },
    { label: "six.month.rent", value: "6" },
    { label: "seven.month.rent", value: "7" },
    { label: "eight.month.rent", value: "8" },
    { label: "nine.month.rent", value: "9" },
    { label: "ten.month.rent", value: "10" },
    { label: "eleven.month.rent", value: "11" },
    { label: "twelve.month.rent", value: "12" },
    { label: "twentyFour.month.rent", value: "24" },
  ];
  export const MonthsOfRent = [
    { label: "one.month", value: "1" },
    { label: "two.month", value: "2" },
    { label: "three.month", value: "3" },
    { label: "four.month", value: "4" },
    { label: "five.month", value: "5" },
    { label: "six.month", value: "6" },
    { label: "seven.month", value: "7" },
    { label: "eight.month", value: "8" },
    { label: "nine.month", value: "9" },
    { label: "ten.month", value: "10" },
    { label: "eleven.month", value: "11" },
    { label: "twelve.month", value: "12" },
    { label: "twentyfour.month", value: "24" },
  ];
  export const MonthesExtension = [
    { label: "one.month", value: "1" },
    { label: "two.month", value: "2" },
    { label: "three.month", value: "3" },
    { label: "four.month", value: "4" },
    { label: "five.month", value: "5" },
    { label: "six.month", value: "6" },
    { label: "seven.month", value: "7" },
    { label: "eight.month", value: "8" },
    { label: "nine.month", value: "9" },
    { label: "ten.month", value: "10" },
    { label: "eleven.month", value: "11" },
    { label: "twelve.month", value: "12" },
    { label: "thirteen.month", value: "13" },
    { label: "fourteen.month", value: "14" },
    { label: "fifteen.month", value: "15" },
    { label: "sixteen.month", value: "16" },
    { label: "seventeen.month", value: "17" },
    { label: "eighteen.month", value: "18" },
    { label: "nineteen.month", value: "19" },
    { label: "twenty.month", value: "20" },
    { label: "twentyone.month", value: "21" },
    { label: "twentytwo.month", value: "22" },
    { label: "twentythree.month", value: "23" },
    { label: "twentyfour.month", value: "24" },
  ];
  export const monthsLookup = [
    { name: "twoMonths", value: 2 },
    { name: "threeMonths", value: 3 },
    { name: "fourMonths", value: 4 },
    { name: "fiveMonths", value: 5 },
    { name: "sixMonths", value: 6 },
    { name: "sevenMonths", value: 7 },
    { name: "eightMonths", value: 8 },
    { name: "nineMonths", value: 9 },
    { name: "tenMonths", value: 10 },
    { name: "elevenMonths", value: 11 },
    { name: "twelveMonths", value: 12 },
    { name: "twentyFourMonths", value: 24 },
  ];
  export const emptyCustomerDetails = [
    { msgId: "rental.nameFirstNameLastName", value: "" },
    { msgId: "rental.mobileNumber", value: "" },
    { msgId: "rental.nationalIdIqama", value: "" },
    { msgId: "rental.gender", value: "" },
    { msgId: "rental.passportNumber", value: "" },
    { msgId: "rental.dateOfBirth", value: "" },
    { msgId: "rental.age", value: "" },
    { msgId: "rental.driverSLicenseIfFound", value: "" },
    { msgId: "rental.driverSLicenseStatus", value: "" },
  ];
  
  export const status = ["closed", "cancelled", "pending", "confirmed", "car_received", "invoiced"];
  export const substatus = [
    "new_request",
    "basket",
    "customer_care",
    "ally_declined",
    "late_confirmation",
    "pickup_overdue",
    "due_invoice",
    "booking_extended",
    "pending_review",
  ];
  export function Gender(formatMessage: { (params: { id: string }): string }) {
    return [
      { value: "male", label: formatMessage({ id: "male" }) },
      { value: "female", label: formatMessage({ id: "female" }) },
    ];
  }
  export const BookingFilterStatus = [
    "all",
    "pending",
    "confirmed",
    "car_received",
    "invoiced",
    "closed",
    "cancelled",
    "closed_after_confirm",
    "booking_extended",
    "rated",
    "pickup_overdue",
    "cancelled_after_confirm",
    "new_request",
    "due_invoice",
    "pending_extend",
    "late_confirmation",
  
    "rejected_extend",
    "pending_review",
  
    "ally_declined",
    // "new_request",
    // "basket",
    // "customer_care",
  ];
  
  export function titlesOptions(formatMessage: { (params: { id: string }): string }) {
    return [
      { value: "Mr", label: formatMessage({ id: "Mr" }) },
      { value: "Ms", label: formatMessage({ id: "Ms" }) },
    ];
  }
  
  export const bookingsTypes = [{ name: "daily" }, { name: "monthly" }, { name: "rent-to-own" }];
  
  export const MUIDataTableOptions = {
    serverSide: true,
    filterType: "textField",
    download: false,
    print: false,
    filter: false,
    search: false,
    sortFilterList: false,
    selectableRowsHeader: false,
    selectableRowsHideCheckboxes: true,
    rowsPerPageOptions: [10, 25, 50, 100],
  };
  export const colors = [
    "beige",
    "black",
    "white",
    "Gold",
    "Silver",
    "Blue",
    "Green",
    "grey",
    "metallic",
    "red",
    "orange",
    "yellow",
    "Champaign",
  ];
  
  export const transmissions =(t:any)=> [
    { label: t("Manual"), value: "manual" },
    { label: t("Automatic"), value: "auto" },
  ];
  export const FeatureType = [
    { label: "Car", value: "car" },
    { label: "Carversion", value: "car_version" },
  ];
  export const availabillityStatus = (t:any)=>[
    { label: t("active"), value: "true" },
    { label: t("inactive"), value: "false" },
  ];
  export const CustomerStatus = [
    { label: "blocked", value: "blocked" },
    { label: "partially_blocked", value: "partially_blocked" },
  ];
export const BannerStatus = (formatMessage: { (params: { id: string }): string }) => [
    { label: formatMessage({ id: "active" }), value: true },
    { label: formatMessage({ id: "inactive" }), value: "false" },
  ];
  export const BookingStatus = [
    { label: "Active", value: "true" },
    { label: "inActive", value: "false" },
  ];
  
  export const BusinessBookingFilterStatus = [
    "all",
    "confirmed",
    "car_received",
    "invoiced",
    "cancelled",
    "closed",
    // "pending",
    // "new_request",
    // "basket",
    // "customer_care",
    // "ally_declined",
    // "late_delivery",
    // "due_invoice",
    // "booking_extended",
    // "pending_review",
  ];
  
export const allyClassOptions = (formatMessage: { (params: { id: string }): string }) => [
    { label: formatMessage({ id: "A" }), value: "A" },
    { label: formatMessage({ id: "B" }), value: "B" },
    { label: formatMessage({ id: "C" }), value: "C" },
    { label: formatMessage({ id: "D" }), value: "D" },
  ];
export const allyClassOptionsWithAll = (formatMessage: { (params: { id: string }): string }) => [
    { label: formatMessage({ id: "all" }), value: "all" },
    { label: formatMessage({ id: "A" }), value: "A" },
    { label: formatMessage({ id: "B" }), value: "B" },
    { label: formatMessage({ id: "C" }), value: "C" },
    { label: formatMessage({ id: "D" }), value: "D" },
  ];
export const paytype = (formatMessage: { (params: { id: string }): string }) => [
    { label: formatMessage({ id: "free" }), value: "free" },
    { label: formatMessage({ id: "one.time" }), value: "one_time" },
    { label: formatMessage({ id: "daily" }), value: "daily" },
  ];
export const showFor = (formatMessage: { (params: { id: string }): string }) => [
    { label: formatMessage({ id: "ally" }), value: "ally_company" },
    { label: formatMessage({ id: "branch" }), value: "branch" },
  ];
export const PaymentMethod = (formatMessage: { (params: { id: string }): string }) => [
    { label: formatMessage({ id: "CASH" }), value: "CASH" },
    { label: formatMessage({ id: "online" }), value: "ONLINE" },
  ];
export const DeliveryPaymentMethods = (formatMessage: { (params: { id: string }): string }) => [
    { label: formatMessage({ id: "CASH" }), value: "cash" },
    { label: formatMessage({ id: "ONLINE" }), value: "online" },
    { label: formatMessage({ id: "ALL" }), value: "all" },
  ];
  
export const PaymentMethodWithAll = (formatMessage: { (params: { id: string }): string }) => [
  
    { label: formatMessage({ id: "CASH" }), value: "CASH" },
    { label: formatMessage({ id: "online" }), value: "ONLINE" },
  ];
export const PaymentStatusMethod = (formatMessage: { (params: { id: string }): string }) => [
    { label: formatMessage({ id: "all" }), value: "all" },
    { label: formatMessage({ id: "Not Paid" }), value: "not_paid" },
    { label: formatMessage({ id: "Paid" }), value: "paid" },
    { label: formatMessage({ id: "Refunded" }), value: "refunded" },
    { label: formatMessage({ id: "pending" }), value: "pending_transaction" }
  ];
export const PaymentStatusMethodExtensions = (formatMessage: { (params: { id: string }): string }) => [
    { label: formatMessage({ id: "Not Paid" }), value: "not_paid" },
    { label: formatMessage({ id: "Paid" }), value: "paid" },
  ];
export const RentTypeList =(t:any)=> [
    { label: t( "RENTAL" ), value: "RENTAL" },
    { label: t( "RENT_TO_OWN" ), value: "RENT_TO_OWN" },
  ];
export const RentTypeListForBooking = (formatMessage: { (params: { id: string }): string }) => [
    { label: formatMessage({ id: "RENTAL" }), value: "RENTAL" },
    { label: formatMessage({ id: "RENT_TO_OWN" }), value: "RENT_TO_OWN" },
    { label: formatMessage({ id: "installments" }), value: "INSTALLMENTS" },
  ];
  export const workingDays = [
    "saturday",
    "sunday",
    "monday",
    "tuesday",
    "wednesday",
    "thursday",
    "friday",
  ];
  
export const carStatus = (formatMessage: { (params: { id: string }): string }) => [
    { value: "1", label: formatMessage({ id: "active" }) },
    { value: "0", label: formatMessage({ id: "inactive" }) },
  ];
export const ticket_status = (formatMessage: { (params: { id: string }): string }) => [
    { value: "new_request", label: formatMessage({ id: "new_request" }) },
    { value: "under_review", label: formatMessage({ id: "under_review" }) },
  
    { value: "completed", label: formatMessage({ id: "completed" }) },
  ];
export const ticket_type = (formatMessage: { (params: { id: string }): string }) => [
    { value: "feedback", label: formatMessage({ id: "feedback" }) },
    { value: "refund_request", label: formatMessage({ id: "refund_request" }) },
  ];
export const ticket_category = (formatMessage: { (params: { id: string }): string }) => [
    { value: "ally", label: formatMessage({ id: "ally" }) },
    { value: "app", label: formatMessage({ id: "app" }) },
    { value: "car", label: formatMessage({ id: "car" }) },
  
  ];
export const carState = (formatMessage: { (params: { id: string }): string }) => [
    { value: "1", label: formatMessage({ id: "New" }) },
    { value: "0", label: formatMessage({ id: "Used" }) },
  ];
  
  export const workingDaysIndeeces = [6, 0, 1, 2, 3, 4, 5];
  