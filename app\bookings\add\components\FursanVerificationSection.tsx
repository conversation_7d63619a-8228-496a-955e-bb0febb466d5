import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Paper,
  FormControlLabel,
  Checkbox,
  TextField,
  Button,
  Alert,
  CircularProgress,
} from '@mui/material';

interface FursanVerificationSectionProps {
  fursanChecked: boolean;
  fursanVerified: boolean;
  fursanMemberId: string;
  onFursanCheckedChange: (checked: boolean) => void;
  onFursanMemberIdChange: (memberId: string) => void;
  onFursanVerify: (memberId: string) => Promise<boolean>;
  disabled?: boolean;
  paymentMethod: string;
  clicked?: boolean;
}

export default function FursanVerificationSection({
  fursanChecked,
  fursanVerified,
  fursanMemberId,
  onFursanCheckedChange,
  onFursanMemberIdChange,
  onFursanVerify,
  disabled = false,
  paymentMethod,
}: FursanVerificationSectionProps) {
  const { t } = useTranslation();
  const [verifying, setVerifying] = useState(false);
  const [verificationMessage, setVerificationMessage] = useState('');

  // Only show Fursan verification for non-cash payments
  if (paymentMethod === 'CASH') {
    return null;
  }

  const handleVerify = async () => {
    if (!fursanMemberId.trim()) {
      setVerificationMessage(t('please.enter.member.id'));
      return;
    }

    setVerifying(true);
    setVerificationMessage('');

    try {
      const isValid = await onFursanVerify(fursanMemberId);
      if (isValid) {
        setVerificationMessage(t('fursan.verification.success'));
      } else {
        setVerificationMessage(t('fursan.verification.failed'));
      }
    } catch (error) {
      setVerificationMessage(t('fursan.verification.error'));
    } finally {
      setVerifying(false);
    }
  };

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        {t('alfursan.membership')}
      </Typography>

      <FormControlLabel
        control={
          <Checkbox
            checked={fursanChecked}
            onChange={(e) => onFursanCheckedChange(e.target.checked)}
            disabled={disabled}
            color="primary"
          />
        }
        label={
          <Box>
            <Typography variant="body1">{t('alfursan')}</Typography>
            <Typography variant="body2" color="textSecondary">
              {t('use.alfursan.points.for.discount')}
            </Typography>
          </Box>
        }
      />

      {fursanChecked && (
        <Box sx={{ mt: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            {t('enter.alfursan.member.id')}
          </Typography>

          <Box sx={{ display: 'flex', gap: 2, alignItems: 'flex-start' }}>
            <TextField
              label={t('alfursan.member.id')}
              value={fursanMemberId}
              onChange={(e) => onFursanMemberIdChange(e.target.value)}
              placeholder={t('enter.member.id')}
              disabled={disabled || verifying || fursanVerified}
              sx={{ flex: 1 }}
            />

            <Button
              variant="contained"
              onClick={handleVerify}
              disabled={disabled || verifying || fursanVerified || !fursanMemberId.trim()}
              startIcon={verifying ? <CircularProgress size={20} /> : null}
            >
              {verifying ? t('verifying') : fursanVerified ? t('verified') : t('verify')}
            </Button>
          </Box>

          {verificationMessage && (
            <Alert severity={fursanVerified ? 'success' : 'error'} sx={{ mt: 2 }}>
              {verificationMessage}
            </Alert>
          )}

          {fursanVerified && (
            <Box
              sx={{
                mt: 3,
                p: 2,
                bgcolor: 'success.50',
                border: 1,
                borderColor: 'success.main',
                borderRadius: 1,
              }}
            >
              <Typography variant="subtitle2" gutterBottom color="success.main">
                {t('alfursan.member.verified')}
              </Typography>
              <Typography variant="body2">
                {t('member.id')}: {fursanMemberId}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {t('loyalty.points.will.be.applied.automatically')}
              </Typography>
            </Box>
          )}

          {/* Fursan benefits info */}
          <Box
            sx={{
              mt: 2,
              p: 2,
              bgcolor: 'info.50',
              border: 1,
              borderColor: 'info.main',
              borderRadius: 1,
            }}
          >
            <Typography variant="subtitle2" gutterBottom color="info.main">
              {t('alfursan.benefits')}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              • {t('earn.points.on.every.booking')}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              • {t('redeem.points.for.discounts')}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              • {t('exclusive.member.offers')}
            </Typography>
          </Box>
        </Box>
      )}
    </Paper>
  );
}
