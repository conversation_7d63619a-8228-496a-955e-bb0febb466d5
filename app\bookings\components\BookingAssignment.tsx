'use client';
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation, useQuery } from '@apollo/client';
import {
  Card,
  CardContent,
  Typography,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Box,
  Avatar,
  Chip,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Person as PersonIcon,
  Assignment as AssignmentIcon,
  Edit as EditIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';

// Import GraphQL operations
import { GET_CUSTOMER_CARE_USERS_QUERY } from '../../gql/queries/bookings';
import { ASSIGN_BOOKING_MUTATION } from '../../gql/mutations/bookings';

interface CustomerCareUser {
  id: string;
  name: string;
  email: string;
  profileImage?: string;
  isActive: boolean;
  roles: Array<{
    id: string;
    enName: string;
    arName: string;
  }>;
}

interface AssignmentHistory {
  id: string;
  assignedTo: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
  assignedBy: {
    id: string;
    name: string;
  };
  assignedAt: string;
  unassignedAt?: string;
  unassignedBy?: {
    id: string;
    name: string;
  };
  notes?: string;
  reason?: string;
  isActive: boolean;
}

interface BookingAssignmentProps {
  bookingId: string;
  currentAssignment?: {
    id: string;
    assignedTo: CustomerCareUser;
    assignedAt: string;
    notes?: string;
  };
  refetchBooking: () => void;
  canManageAssignments?: boolean;
}

interface AssignmentModalProps {
  open: boolean;
  onClose: () => void;
  bookingId: string;
  currentAssignment?: any;
  users: CustomerCareUser[];
  onAssign: (userId: string, notes: string) => void;
  onUnassign: (reason: string) => void;
  loading?: boolean;
}

function AssignmentModal({
  open,
  onClose,
  bookingId,
  currentAssignment,
  users,
  onAssign,
  onUnassign,
  loading = false,
}: AssignmentModalProps) {
  const { t } = useTranslation();
  const [selectedUserId, setSelectedUserId] = useState<string>('');
  const [notes, setNotes] = useState<string>('');
  const [unassignReason, setUnassignReason] = useState<string>('');
  const [action, setAction] = useState<'assign' | 'unassign'>('assign');
  const [error, setError] = useState<string>('');

  useEffect(() => {
    if (currentAssignment) {
      setAction('unassign');
    } else {
      setAction('assign');
    }
  }, [currentAssignment]);

  const handleSubmit = () => {
    if (loading) return;

    if (action === 'assign') {
      if (!selectedUserId) {
        setError(t('assignment.pleaseSelectUser'));
        return;
      }
      onAssign(selectedUserId, notes);
    } else {
      if (!unassignReason.trim()) {
        setError(t('assignment.pleaseProvideReason'));
        return;
      }
      onUnassign(unassignReason);
    }
    onClose();
  };

  const activeUsers = users.filter((user) => user.isActive);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {action === 'assign' ? t('assignment.assignBooking') : t('assignment.unassignBooking')}
      </DialogTitle>
      <DialogContent>
        {currentAssignment && (
          <Alert severity="info" sx={{ mb: 2 }}>
            {t('assignment.currentlyAssignedTo')}: {currentAssignment.assignedTo.name}
          </Alert>
        )}

        {action === 'assign' && (
          <Box sx={{ mt: 2 }}>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>{t('assignment.selectUser')}</InputLabel>
              <Select
                value={selectedUserId}
                onChange={(e) => {
                  setSelectedUserId(e.target.value);
                  setError('');
                }}
                error={!!error && !selectedUserId}
              >
                {activeUsers.map((user) => (
                  <MenuItem key={user.id} value={user.id}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Avatar src={user.profileImage} sx={{ width: 24, height: 24 }}>
                        {user.name.charAt(0)}
                      </Avatar>
                      <Box>
                        <Typography variant="body2">{user.name}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {user.roles && user.roles.length > 0
                            ? `${user.roles[0].enName}`
                            : t('assignment.selectUser')}
                        </Typography>
                      </Box>
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <TextField
              fullWidth
              multiline
              rows={3}
              label={t('assignment.assignmentNotes')}
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder={t('assignment.optionalNotes')}
            />
          </Box>
        )}

        {action === 'unassign' && (
          <TextField
            fullWidth
            multiline
            rows={3}
            label={t('assignment.unassignmentReason')}
            value={unassignReason}
            onChange={(e) => {
              setUnassignReason(e.target.value);
              setError('');
            }}
            error={!!error}
            helperText={error}
            placeholder={t('assignment.unassignmentReasonPlaceholder')}
            sx={{ mt: 2 }}
          />
        )}

        {error && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {error}
          </Alert>
        )}
      </DialogContent>
      <DialogActions>
        <Button
          onClick={onClose}
          disabled={loading}
          sx={{
            backgroundColor: '#677080',
            color: 'white',
            '&:hover': {
              backgroundColor: '#5a616e',
            },
            '&:disabled': {
              backgroundColor: '#cccccc',
              color: '#666666',
            },
          }}
        >
          {t('common.cancel')}
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={loading}
          sx={{
            backgroundColor: action === 'assign' ? '#00D014' : '#FF3739',
            color: 'white',
            '&:hover': {
              backgroundColor: action === 'assign' ? '#00b612' : '#e02f31',
            },
            '&:disabled': {
              backgroundColor: '#cccccc',
              color: '#666666',
            },
          }}
        >
          {loading ? (
            <CircularProgress size={20} />
          ) : action === 'assign' ? (
            t('assignment.assign')
          ) : (
            t('assignment.unassign')
          )}
        </Button>
      </DialogActions>
    </Dialog>
  );
}

interface HistoryModalProps {
  open: boolean;
  onClose: () => void;
  bookingId: string;
}

function HistoryModal({ open, onClose, bookingId }: HistoryModalProps) {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  const dateLocale = isRTL ? ar : enUS;

  // Disable history functionality for now
  // const { data, loading, error } = useQuery(GET_ASSIGNMENT_HISTORY_QUERY, {
  //   variables: { bookingId },
  //   skip: !open,
  // });

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy HH:mm', { locale: dateLocale });
    } catch {
      return '-';
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>{t('assignment.assignmentHistory')}</DialogTitle>
      <DialogContent>
        <Typography variant="body2" color="text.secondary">
          {t('assignment.noHistoryFound')}
        </Typography>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>{t('common.close')}</Button>
      </DialogActions>
    </Dialog>
  );
}

export default function BookingAssignment({
  bookingId,
  currentAssignment,
  refetchBooking,
  canManageAssignments = true,
}: BookingAssignmentProps) {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  const dateLocale = isRTL ? ar : enUS;
  const [assignmentModal, setAssignmentModal] = useState(false);
  const [historyModal, setHistoryModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    data: usersData,
    loading: usersLoading,
    error: usersError,
  } = useQuery(GET_CUSTOMER_CARE_USERS_QUERY);
  const [assignBooking] = useMutation(ASSIGN_BOOKING_MUTATION);

  const handleAssign = async (userId: string, notes: string) => {
    setLoading(true);
    setError(null);

    try {
      const result = await assignBooking({
        variables: {
          rentalId: bookingId,
          userId,
        },
      });

      if (result.data?.assignRentalTo?.status) {
        await refetchBooking();
        setAssignmentModal(false);
      } else {
        setError(result.data?.assignRentalTo?.errors?.[0] || t('assignment.errorAssigning'));
      }
    } catch (error) {
      console.error('Error assigning booking:', error);
      setError(t('assignment.errorAssigning'));
    } finally {
      setLoading(false);
    }
  };

  const handleUnassign = async (reason: string) => {
    setLoading(true);
    setError(null);

    try {
      // For now, let's show a message that unassign is not available
      // and user should reassign to another user instead
      setError(t('assignment.reassignOrUnassign'));
      setAssignmentModal(false);
    } catch (error) {
      console.error('Error unassigning booking:', error);
      setError(t('assignment.errorUnassigning'));
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy HH:mm', { locale: dateLocale });
    } catch {
      return '-';
    }
  };

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <AssignmentIcon />
            {t('assignment.title')}
          </Typography>

          <Box sx={{ display: 'flex', gap: 1 }}>
            {/* Temporarily disabled until history API is implemented */}
            {/* <Tooltip title={t('assignment.viewAssignmentHistory')}>
              <IconButton
                size="small"
                onClick={() => setHistoryModal(true)}
                sx={{
                  backgroundColor: '#00D0BD',
                  color: 'white',
                  '&:hover': {
                    backgroundColor: '#00b8a7',
                  },
                }}
              >
                <HistoryIcon />
              </IconButton>
            </Tooltip> */}

            {canManageAssignments && (
              <Tooltip
                title={
                  currentAssignment
                    ? t('assignment.reassignOrUnassign')
                    : t('assignment.assignBooking')
                }
              >
                <IconButton
                  size="small"
                  onClick={() => setAssignmentModal(true)}
                  disabled={loading || usersLoading}
                  sx={{
                    backgroundColor: '#5D92F4',
                    color: 'white',
                    '&:hover': {
                      backgroundColor: '#4a7bd9',
                    },
                    '&:disabled': {
                      backgroundColor: '#cccccc',
                      color: '#666666',
                    },
                  }}
                >
                  {loading ? <CircularProgress size={20} /> : <EditIcon />}
                </IconButton>
              </Tooltip>
            )}
          </Box>
        </Box>

        {/* Error Alert */}
        {(error || usersError) && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error || (usersError && t('assignment.errorLoadingUsers'))}
          </Alert>
        )}

        {/* Loading State */}
        {usersLoading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
            <CircularProgress />
          </Box>
        )}

        {currentAssignment ? (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar src={currentAssignment.assignedTo.profileImage}>
              {currentAssignment.assignedTo.name.charAt(0)}
            </Avatar>
            <Box sx={{ flex: 1 }}>
              <Typography variant="body1" fontWeight="medium">
                {currentAssignment.assignedTo.name}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {currentAssignment.assignedTo.email}
              </Typography>
              <Typography variant="caption" color="text.secondary" display="block">
                {t('assignment.assignedOn')}: {formatDate(currentAssignment.assignedAt)}
              </Typography>
              {currentAssignment.notes && (
                <Typography variant="body2" sx={{ mt: 1 }}>
                  {t('bookings.notes.createdBy')}: {currentAssignment.notes}
                </Typography>
              )}
            </Box>
            <Chip
              label={t('assignment.assigned')}
              sx={{
                backgroundColor: '#00D014',
                color: 'white',
              }}
              size="small"
            />
          </Box>
        ) : (
          <Box sx={{ textAlign: 'center', py: 3 }}>
            <PersonIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 1 }} />
            <Typography variant="body1" color="text.secondary">
              {t('assignment.noAssignmentFound')}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {t('assignment.notAssignedMessage')}
            </Typography>
            {/* {canManageAssignments && (
              <Button
                variant="contained"
                startIcon={<AssignmentIcon />}
                onClick={() => setAssignmentModal(true)}
                sx={{
                  mt: 2,
                  backgroundColor: '#896BD6',
                  color: 'white',
                  '&:hover': {
                    backgroundColor: '#7a5cc2',
                  },
                }}
              >
                {t('assignment.assignNow')}
              </Button>
            )} */}
          </Box>
        )}

        {/* Assignment Modal */}
        <AssignmentModal
          open={assignmentModal}
          onClose={() => setAssignmentModal(false)}
          bookingId={bookingId}
          currentAssignment={currentAssignment}
          users={usersData?.users?.collection || []}
          onAssign={handleAssign}
          onUnassign={handleUnassign}
          loading={loading}
        />

        {/* History Modal */}
        <HistoryModal
          open={historyModal}
          onClose={() => setHistoryModal(false)}
          bookingId={bookingId}
        />
      </CardContent>
    </Card>
  );
}
