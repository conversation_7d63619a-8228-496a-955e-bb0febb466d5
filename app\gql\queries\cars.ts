
// Query to fetch Cars with pagination and filtering
import { gql } from '@apollo/client';

export const GET_ALL_CARS = gql`
query getAllCars(
  $acrissCode: String
  $airportId: ID
  $allyId: ID
  $allyName: String
  $availabilityStatus: Boolean
  $branchId: ID
  $branchName: String
  $dailyPrice: Float
  $dropOffLocationId: ID
  $insuranceId: [ID!]
  $limit: Int
  $make: ID
  $model: ID
  $page: Int
  $pickUpLocationId: ID
  $plateNo: String
  $rentType: RentTypeEnum
  $sortBy: String
  $transmission: String
  $vehicleType: ID
  $version: ID
  $year: Int
) 
{
  allyCars(
      acrissCode: $acrissCode
      airportId: $airportId
      allyId: $allyId
      allyName: $allyName
      availabilityStatus: $availabilityStatus
      branchId: $branchId
      branchName: $branchName
      dailyPrice: $dailyPrice
      dropOffLocationId: $dropOffLocationId
      insuranceId: $insuranceId
      limit: $limit
      make: $make
      model: $model
      page: $page
      pickUpLocationId: $pickUpLocationId
      plateNo: $plateNo
      rentType: $rentType
      sortBy: $sortBy
      transmission: $transmission
      vehicleType: $vehicleType
      version: $version
      year: $year
    ) {
      collection {
        availabilityStatus
        carsCount
        availableCarsCount
        allyName
        transmission
        branch{
            id
            name
            allyCompany{
                id
            }
            area{
                name
            }
        }
        carModel{
        enName
        arName
        acrissCode
      }
      make{
        arName
        enName
      }
      carVersion{
          arName
          enName
        }
        createdAt
        dailyPrice
        deletedAt
        id
      
        year
      }
      metadata {
        currentPage
        limitValue
        totalCount
        totalPages
      }
    }
}
  `;

export const GET_ALL_AVAILABLE_CARS = gql`
  query GetAllAvailableCars(
    $pickStartDate: String
    $pickEndDate: String
    $dropOffLocationId: ID
    $branchId: ID
    $pickUpLocationId: ID
    $isActive: Boolean
    $make: ID
    $model: ID
    $version: ID
    $pickStartTime: String
    $pickEndTime: String
    $page: Int
    $limit: Int
    $canDelivery: Boolean
  ) {
    cars(
      pickStartDate: $pickStartDate
      pickEndDate: $pickEndDate
      branchId: $branchId
      pickUpLocationId: $pickUpLocationId
      dropOffLocationId: $dropOffLocationId
      pickStartTime: $pickStartTime
      pickEndTime: $pickEndTime
      isActive: $isActive
      make: $make
      model: $model
      version: $version
      page: $page
      limit: $limit
      canDelivery: $canDelivery
    ) {
      collection {
        id
        branchId
        branchClass
        carIdImage
        carImages
        color
        dailyPrice
        monthlyPrice
        weeklyPrice
        priceForPeriod
        deliverToCustomerLocationCost
        distance
        distanceBetweenCarUser
        distanceByDay
        distanceByMonth
        distanceByWeek
        guaranteeAmount
        lat
        lng
        lonlat
        makeId
        carModelId
        carVersionId
        transmission
        transmissionName
        year
        isUnlimited
        unlimitedFeePerDay
        make {
          id
          arName
          enName
          logo
        }
        carModel {
          id
          arName
          enName
        }
        carVersion {
          id
          arName
          enName
          year
          image
        }
        branch {
          id
          arName
          enName
          address
          branchWorkingDays {
            id
            startTime
            weekDayString
            endTime
            weekDay
          }
        }
        carInsurances {
          id
          insuranceId
          insuranceName
          value
        }
      }
      metadata {
        currentPage
        limitValue
        totalCount
        totalPages
      }
    }
  }
`;

export const GET_CAR_PROFILE = gql`
  query GetCarProfile($id: ID!) {
    carProfile(id: $id) {
      id
      branchId
      branchClass
      carIdImage
      carImages
      color
      dailyPrice
      monthlyPrice
      weeklyPrice
      priceForPeriod
      deliverToCustomerLocationCost
      distance
      distanceBetweenCarUser
      distanceByDay
      distanceByMonth
      distanceByWeek
      guaranteeAmount
      lat
      lng
      lonlat
      makeId
      carModelId
      carVersionId
      transmission
      transmissionName
      year
      accidentPenalty
      additionalDistanceCost
      allyConditions
      availabilityStatus
      bookingStatus
      carMakeName
      carModelName
      carVersionName
      make {
        id
        arName
        enName
        logo
        name
        status
      }
      carModel {
        id
        arName
        enName
        name
        acrissCode
      }
      carVersion {
        id
        arName
        enName
        name
        year
        image
      }
      vehicleType {
        id
        arName
        enName
        name
        image
        status
      }
      branch {
        id
        arName
        enName
        address
        allyCompanyId
        areaId
        bankCardImage
        branchClass
        branchState
        canDelivery
        carCount
        deliverToAirport
        distanceBetweenBranchUser
        fixedDeliveryFees
        isActive
        lat
        lng
        lonlat
        name
        officeNumber
        rate
        allyCompany {
          allyExtraServicesForAlly {
            id
            isRequired
            subtitle
            extraServiceId
            extraService {
              arTitle
              enTitle
              id
              subtitle
            }
          }
        }
        branchExtraServices {
          id
          isRequired
          subtitle
          arTitle
          enTitle
          title
          allyExtraService {
            arSubtitle
            extraService {
              title
              enTitle
              arTitle
            }
          }
        }
        availableHandoverBranches {
          id
          name
          areaId
          allyCompany {
            allyHandoverCities {
              pickUpCityId
              dropOffCityId
              price
            }
          }
        }
      }
      carFeatures {
        carId
        featureId
        key
        value
        arKey
        arValue
        enKey
        enValue
        icon
      }
      carInsurances {
        id
        carId
        insuranceId
        insuranceName
        value
        monthlyValue
      }
      ownCarDetail {
        plateNoAr
        plateNoEn
        isNewCar
        km
        color {
          name
        }
        ownCarMedia {
          mediaUrl
        }
        ownCarPlans {
          id
          createdAt
          updatedAt
          finalInstallment
          firstInstallment
          monthlyInstallment
          noOfMonths
          isActive
        }
      }
    }
  }
`;

export const GET_RENT_PRICE = gql`
  query GetRentPrice(
    $carId: ID!
    $deliverLat: Float
    $deliverLng: Float
    $deliveryType: String
    $deliveryPrice: Float
    $handoverPrice: Float
    $handoverBranchPrice: Float
    $handoverBranch: ID
    $dropOffDate: String
    $dropOffTime: String
    $insuranceId: ID
    $pickUpDate: String
    $pickUpTime: String
    $allyExtraServices: [ID!]
    $branchExtraServices: [ID!]
    $couponId: ID
    $isUnlimited: Boolean
    $usedPrice: Float
    $withWallet: Boolean
    $payWithInstallments: Boolean
    $walletPaidAmount: Float
    $suggestedPrice: Float
    $rentalId: ID
    $ownCarPlanId: ID
    $isEdit: Boolean
    $paymentMethod: AboutPricePaymentMethod
  ) {
    aboutRentPrice(
      carId: $carId
      deliverLat: $deliverLat
      deliverLng: $deliverLng
      deliveryPrice: $deliveryPrice
      handoverPrice: $handoverPrice
      handoverBranchPrice: $handoverBranchPrice
      handoverBranch: $handoverBranch
      deliveryType: $deliveryType
      dropOffDate: $dropOffDate
      dropOffTime: $dropOffTime
      insuranceId: $insuranceId
      pickUpDate: $pickUpDate
      pickUpTime: $pickUpTime
      allyExtraServices: $allyExtraServices
      branchExtraServices: $branchExtraServices
      couponId: $couponId
      isUnlimited: $isUnlimited
      usedPrice: $usedPrice
      withWallet: $withWallet
      payWithInstallments: $payWithInstallments
      walletPaidAmount: $walletPaidAmount
      suggestedPrice: $suggestedPrice
      rentalId: $rentalId
      ownCarPlanId: $ownCarPlanId
      isEdit: $isEdit
      paymentMethod: $paymentMethod
    ) {
      addsPrice
      totalAddsPrice
      dailyPrice
      handoverPrice
      deliveryPrice
      discountPercentage
      discountType
      discountValue
      insuranceIncluded
      insuranceValue
      numberOfDays
      priceBeforeDiscount
      priceBeforeInsurance
      priceBeforeTax
      couponDiscount
      couponCode
      pricePerDay
      taxValue
      totalPrice
      totalAmountDue
      totalExtraServicesPrice
      valueAddedTaxPercentage
      isUnlimited
      isUnlimitedFree
      unlimitedFeePerDay
      totalUnlimitedFee
      totalOfTheRentToOwnPrice
      totalRtoInstallmentsAmount
      rtoPriceBeforeTax
      rtoTaxValue
      allyExtraServices {
        allyCompanyId
        arSubtitle
        enSubtitle
        extraServiceId
        id
        isActive
        isRequired
        payType
        serviceValue
        showFor
        subtitle
        totalServiceValue
      }
      totalInstallmentsAmount
      completedInstallmentsAmount
      remainingDueInstallmentsAmount
      couponErrorMessage
      remainingInstallmentsAmount
      rentToOwnInstallmentBreakdown {
        firstPayment
        monthlyInstallment
        finalInstallment
      }
      branchExtraServices {
        allyExtraServiceId
        arSubtitle
        branchId
        title
        arTitle
        enTitle
        enSubtitle
        id
        isActive
        isRequired
        payType
        serviceValue
        subtitle
        totalServiceValue
      }
      installmentsBreakdown {
        amount
        status
      }
      numberOfDays
    }
  }
`;

export const GET_COUPON_AVAILABILITY = gql`
  query CarCouponAvailability($carId: ID!, $couponCode: String!, $userId: ID) {
    carCouponAvailability(carId: $carId, couponCode: $couponCode, userId: $userId) {
      car {
        id
      }
      coupon {
        id
        code
      }
      status
    }
  }
`;
