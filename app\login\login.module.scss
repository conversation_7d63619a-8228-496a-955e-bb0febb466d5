.loginLogoWrapper {
    position: relative;
    overflow: hidden;
    
    .loginLogo {
        position: relative;
        z-index: 1;
        animation: fadeInUp 1s ease-out;
        
        img {
            // filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
            transition: transform 0.3s ease;
            
            &:hover {
                transform: scale(1.05);
            }
        }
    }
}

.loginForm {
    animation: fadeInRight 1s ease-out;
    
    /* Use explicit class selectors for Material UI components to avoid ambiguity */
    & :global(.MuiTextField-root) {
        & :global(.MuiOutlinedInput-root) {
            border-radius: 12px;
            transition: all 0.3s ease;
            
            &:hover {
                background-color: rgba(0, 0, 0, 0.02);
            }
            
            &:global(.Mui-focused) {
                box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
            }
        }
    }
    
    & :global(.MuiButton-root) {
        border-radius: 12px;
        text-transform: none;
        font-weight: 600;
        padding: 12px 24px;
        transition: all 0.3s ease;
        
        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);
        }
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes backgroundMove {
    from {
        background-position: 0 0;
    }
    to {
        background-position: 100% 100%;
    }
}

@media (max-width: 960px) {
    .loginLogoWrapper {
        min-height: 200px;
        
        &::before {
            height: 100%;
        }
    }
}